{"remainingRequest": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\api\\asc\\device.js", "dependencies": [{"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\api\\asc\\device.js", "mtime": 1750833543328}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\babel.config.js", "mtime": 1750833543308}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750836929039}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750836940534}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750836931805}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listDevice", "query", "request", "url", "method", "params", "getDevice", "id", "addDevice", "data", "updateDevice", "delDevice", "exportDevice", "getDeviceStatus", "openGate", "closeGate"], "sources": ["D:/projects/gaoyi-plat/ruoyi-ui/src/api/asc/device.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询车牌机设备列表\r\nexport function listDevice(query) {\r\n  return request({\r\n    url: '/asc/device/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询车牌机设备详细\r\nexport function getDevice(id) {\r\n  return request({\r\n    url: '/asc/device/' + id,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增车牌机设备\r\nexport function addDevice(data) {\r\n  return request({\r\n    url: '/asc/device',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改车牌机设备\r\nexport function updateDevice(data) {\r\n  return request({\r\n    url: '/asc/device',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除车牌机设备\r\nexport function delDevice(id) {\r\n  return request({\r\n    url: '/asc/device/' + id,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 导出车牌机设备\r\nexport function exportDevice(query) {\r\n  return request({\r\n    url: '/asc/device/export',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 获取设备连接状态\r\nexport function getDeviceStatus() {\r\n  return request({\r\n    url: '/asc/device/status',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 手动控制开闸\r\nexport function openGate(id) {\r\n  return request({\r\n    url: '/asc/device/openGate/' + id,\r\n    method: 'post'\r\n  })\r\n}\r\n\r\n// 手动控制关闸\r\nexport function closeGate(id) {\r\n  return request({\r\n    url: '/asc/device/closeGate/' + id,\r\n    method: 'post'\r\n  })\r\n}"], "mappings": ";;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,UAAUA,CAACC,KAAK,EAAE;EAChC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,SAASA,CAACC,EAAE,EAAE;EAC5B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc,GAAGI,EAAE;IACxBH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,SAASA,CAACC,IAAI,EAAE;EAC9B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,aAAa;IAClBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,YAAYA,CAACD,IAAI,EAAE;EACjC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,aAAa;IAClBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,SAASA,CAACJ,EAAE,EAAE;EAC5B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc,GAAGI,EAAE;IACxBH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,YAAYA,CAACX,KAAK,EAAE;EAClC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASY,eAAeA,CAAA,EAAG;EAChC,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASU,QAAQA,CAACP,EAAE,EAAE;EAC3B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB,GAAGI,EAAE;IACjCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASW,SAASA,CAACR,EAAE,EAAE;EAC5B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB,GAAGI,EAAE;IAClCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}