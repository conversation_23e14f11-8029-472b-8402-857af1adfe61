{"remainingRequest": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\App.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\App.vue", "mtime": 1750833543327}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\babel.config.js", "mtime": 1750833543308}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750836929039}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750836940534}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750836929039}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750836957095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9wcm9qZWN0cy9nYW95aS1wbGF0L3J1b3lpLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdC5qcyIpLmRlZmF1bHQ7Ck9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5kZWZhdWx0ID0gdm9pZCAwOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuY29uY2F0LmpzIik7CnZhciBfVGhlbWVQaWNrZXIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvY29tcG9uZW50cy9UaGVtZVBpY2tlciIpKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIG5hbWU6ICJBcHAiLAogIGNvbXBvbmVudHM6IHsKICAgIFRoZW1lUGlja2VyOiBfVGhlbWVQaWNrZXIuZGVmYXVsdAogIH0sCiAgbWV0YUluZm86IGZ1bmN0aW9uIG1ldGFJbmZvKCkgewogICAgcmV0dXJuIHsKICAgICAgdGl0bGU6IHRoaXMuJHN0b3JlLnN0YXRlLnNldHRpbmdzLmR5bmFtaWNUaXRsZSAmJiB0aGlzLiRzdG9yZS5zdGF0ZS5zZXR0aW5ncy50aXRsZSwKICAgICAgdGl0bGVUZW1wbGF0ZTogZnVuY3Rpb24gdGl0bGVUZW1wbGF0ZSh0aXRsZSkgewogICAgICAgIHJldHVybiB0aXRsZSA/ICIiLmNvbmNhdCh0aXRsZSwgIiAtICIpLmNvbmNhdChwcm9jZXNzLmVudi5WVUVfQVBQX1RJVExFKSA6IHByb2Nlc3MuZW52LlZVRV9BUFBfVElUTEU7CiAgICAgIH0KICAgIH07CiAgfQp9Ow=="}, {"version": 3, "names": ["_ThemePicker", "_interopRequireDefault", "require", "name", "components", "ThemePicker", "metaInfo", "title", "$store", "state", "settings", "dynamicTitle", "titleTemplate", "concat", "process", "env", "VUE_APP_TITLE"], "sources": ["src/App.vue"], "sourcesContent": ["<template>\r\n  <div id=\"app\">\r\n    <router-view />\r\n    <theme-picker />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport ThemePicker from \"@/components/ThemePicker\";\r\n\r\nexport default {\r\n  name: \"App\",\r\n  components: { ThemePicker },\r\n    metaInfo() {\r\n        return {\r\n            title: this.$store.state.settings.dynamicTitle && this.$store.state.settings.title,\r\n            titleTemplate: title => {\r\n                return title ? `${title} - ${process.env.VUE_APP_TITLE}` : process.env.VUE_APP_TITLE\r\n            }\r\n        }\r\n    }\r\n};\r\n</script>\r\n<style scoped>\r\n#app .theme-picker {\r\n  display: none;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;AAQA,IAAAA,YAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;;iCAEA;EACAC,IAAA;EACAC,UAAA;IAAAC,WAAA,EAAAA;EAAA;EACAC,QAAA,WAAAA,SAAA;IACA;MACAC,KAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAC,YAAA,SAAAH,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAH,KAAA;MACAK,aAAA,WAAAA,cAAAL,KAAA;QACA,OAAAA,KAAA,MAAAM,MAAA,CAAAN,KAAA,SAAAM,MAAA,CAAAC,OAAA,CAAAC,GAAA,CAAAC,aAAA,IAAAF,OAAA,CAAAC,GAAA,CAAAC,aAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}