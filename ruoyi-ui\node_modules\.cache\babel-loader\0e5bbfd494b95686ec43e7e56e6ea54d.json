{"remainingRequest": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\views\\archives\\materialClass\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\views\\archives\\materialClass\\index.vue", "mtime": 1750833543392}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\babel.config.js", "mtime": 1750833543308}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750836929039}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750836940534}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750836929039}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750836957095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_materialClass", "require", "_vueTreeselect", "_interopRequireDefault", "name", "dicts", "components", "Treeselect", "data", "loading", "showSearch", "materialClassList", "classOptions", "title", "open", "isExpandAll", "refreshTable", "queryParams", "className", "undefined", "status", "form", "rules", "parentId", "required", "message", "trigger", "orderNum", "created", "getList", "methods", "_this", "list", "then", "response", "handleTree", "normalizer", "node", "children", "length", "id", "classId", "label", "cancel", "reset", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleAdd", "row", "_this2", "toggleExpandAll", "_this3", "$nextTick", "handleUpdate", "_this4", "getDetail", "listMaterialClassExcludeChild", "noResultsOptions", "materClassList", "push", "submitForm", "_this5", "$refs", "validate", "valid", "console", "log", "update", "$modal", "msgSuccess", "add", "handleDelete", "_this6", "confirm", "del", "catch"], "sources": ["src/views/archives/materialClass/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\">\r\n      <el-form-item label=\"分类名称\" prop=\"className\">\r\n        <el-input\r\n          v-model=\"queryParams.className\"\r\n          placeholder=\"请输入分类名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['archives:materialclass:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"info\"\r\n          plain\r\n          icon=\"el-icon-sort\"\r\n          size=\"mini\"\r\n          @click=\"toggleExpandAll\"\r\n        >展开/折叠</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table\r\n      v-if=\"refreshTable\"\r\n      v-loading=\"loading\"\r\n      :data=\"materialClassList\"\r\n      row-key=\"classId\"\r\n      :default-expand-all=\"isExpandAll\"\r\n      :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\">\r\n      <el-table-column prop=\"className\" label=\"分类名称\" width=\"260\"></el-table-column>\r\n      <el-table-column prop=\"orderNum\" label=\"排序\" width=\"200\"></el-table-column>\r\n      <el-table-column prop=\"status\" label=\"状态\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"200\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['archives:materialclass:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-plus\"\r\n            @click=\"handleAdd(scope.row)\"\r\n            v-hasPermi=\"['archives:materialclass:add']\"\r\n          >新增</el-button>\r\n          <el-button\r\n            v-if=\"scope.row.parentId != 0\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['archives:materialclass:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-row>\r\n          <el-col :span=\"24\" v-if=\"form.parentId != 0\">\r\n            <el-form-item label=\"上级分类\" prop=\"parentId\">\r\n              <treeselect v-model=\"form.parentId\" :options=\"classOptions\" :normalizer=\"normalizer\" placeholder=\"选择上级分类\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"分类名称\" prop=\"className\">\r\n              <el-input v-model=\"form.className\" placeholder=\"请输入分类名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"显示排序\" prop=\"orderNum\">\r\n              <el-input-number v-model=\"form.orderNum\" controls-position=\"right\" :min=\"0\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"分类状态\">\r\n              <el-radio-group v-model=\"form.status\">\r\n                <el-radio\r\n                  v-for=\"dict in dict.type.sys_normal_disable\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.value\"\r\n                >{{dict.label}}</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { list, getDetail, del, add, update, listMaterialClassExcludeChild} from \"@/api/archives/materialClass\";\r\nimport Treeselect from \"@riophae/vue-treeselect\";\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\n\r\nexport default {\r\n  name: \"MaterialClass\",\r\n  dicts: ['sys_normal_disable'],\r\n  components: { Treeselect },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 表格树数据\r\n      materialClassList: [],\r\n      // 部门树选项\r\n      classOptions: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 是否展开，默认全部展开\r\n      isExpandAll: true,\r\n      // 重新渲染表格状态\r\n      refreshTable: true,\r\n      // 查询参数\r\n      queryParams: {\r\n        className: undefined,\r\n        status: undefined\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        parentId: [\r\n          { required: true, message: \"上级分类不能为空\", trigger: \"blur\" }\r\n        ],\r\n        className: [\r\n          { required: true, message: \"分类名称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        orderNum: [\r\n          { required: true, message: \"显示排序不能为空\", trigger: \"blur\" }\r\n        ],\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询分类列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      list(this.queryParams).then(response => {\r\n        this.materialClassList = this.handleTree(response.data, \"classId\");\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 转换部门数据结构 */\r\n    normalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children;\r\n      }\r\n      return {\r\n        id: node.classId,\r\n        label: node.className,\r\n        children: node.children\r\n      };\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        materialClassList: undefined,\r\n        parentId: undefined,\r\n        className: undefined,\r\n        orderNum: undefined,\r\n        status: \"0\"\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd(row) {\r\n      this.reset();\r\n      if (row !== undefined) {\r\n        this.form.parentId = row.classId;\r\n      }\r\n      this.open = true;\r\n      this.title = \"添加分类\";\r\n      list().then(response => {\r\n        this.classOptions = this.handleTree(response.data, \"classId\");\r\n      });\r\n    },\r\n    /** 展开/折叠操作 */\r\n    toggleExpandAll() {\r\n      this.refreshTable = false;\r\n      this.isExpandAll = !this.isExpandAll;\r\n      this.$nextTick(() => {\r\n        this.refreshTable = true;\r\n      });\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      getDetail(row.classId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改分类\";\r\n        listMaterialClassExcludeChild(row.classId).then(response => {\r\n          this.classOptions = this.handleTree(response.data, \"classId\");\r\n          if (this.classOptions.length === 0) {\r\n            const noResultsOptions = { materClassList: this.form.parentId, className: this.form.className, children: [] };\r\n            this.classOptions.push(noResultsOptions);\r\n          }\r\n        });\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm: function() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          console.log(this.form.materClassList)\r\n          if (this.form.classId !== undefined) {\r\n            update(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            add(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      this.$modal.confirm('是否确认删除名称为\"' + row.className + '\"的数据项？').then(function() {\r\n        return del(row.classId);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    }\r\n  }\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;;AAkIA,IAAAA,cAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAC,sBAAA,CAAAF,OAAA;AACAA,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,KAAA;EACAC,UAAA;IAAAC,UAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,UAAA;MACA;MACAC,iBAAA;MACA;MACAC,YAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;MACA;MACAC,YAAA;MACA;MACAC,WAAA;QACAC,SAAA,EAAAC,SAAA;QACAC,MAAA,EAAAD;MACA;MACA;MACAE,IAAA;MACA;MACAC,KAAA;QACAC,QAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAR,SAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,QAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,aACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAtB,OAAA;MACA,IAAAuB,mBAAA,OAAAf,WAAA,EAAAgB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAApB,iBAAA,GAAAoB,KAAA,CAAAI,UAAA,CAAAD,QAAA,CAAA1B,IAAA;QACAuB,KAAA,CAAAtB,OAAA;MACA;IACA;IACA,eACA2B,UAAA,WAAAA,WAAAC,IAAA;MACA,IAAAA,IAAA,CAAAC,QAAA,KAAAD,IAAA,CAAAC,QAAA,CAAAC,MAAA;QACA,OAAAF,IAAA,CAAAC,QAAA;MACA;MACA;QACAE,EAAA,EAAAH,IAAA,CAAAI,OAAA;QACAC,KAAA,EAAAL,IAAA,CAAAnB,SAAA;QACAoB,QAAA,EAAAD,IAAA,CAAAC;MACA;IACA;IACA;IACAK,MAAA,WAAAA,OAAA;MACA,KAAA7B,IAAA;MACA,KAAA8B,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAvB,IAAA;QACAV,iBAAA,EAAAQ,SAAA;QACAI,QAAA,EAAAJ,SAAA;QACAD,SAAA,EAAAC,SAAA;QACAQ,QAAA,EAAAR,SAAA;QACAC,MAAA;MACA;MACA,KAAAyB,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAjB,OAAA;IACA;IACA,aACAkB,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA,aACAE,SAAA,WAAAA,UAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAN,KAAA;MACA,IAAAK,GAAA,KAAA9B,SAAA;QACA,KAAAE,IAAA,CAAAE,QAAA,GAAA0B,GAAA,CAAAR,OAAA;MACA;MACA,KAAA3B,IAAA;MACA,KAAAD,KAAA;MACA,IAAAmB,mBAAA,IAAAC,IAAA,WAAAC,QAAA;QACAgB,MAAA,CAAAtC,YAAA,GAAAsC,MAAA,CAAAf,UAAA,CAAAD,QAAA,CAAA1B,IAAA;MACA;IACA;IACA,cACA2C,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAApC,YAAA;MACA,KAAAD,WAAA,SAAAA,WAAA;MACA,KAAAsC,SAAA;QACAD,MAAA,CAAApC,YAAA;MACA;IACA;IACA,aACAsC,YAAA,WAAAA,aAAAL,GAAA;MAAA,IAAAM,MAAA;MACA,KAAAX,KAAA;MACA,IAAAY,wBAAA,EAAAP,GAAA,CAAAR,OAAA,EAAAR,IAAA,WAAAC,QAAA;QACAqB,MAAA,CAAAlC,IAAA,GAAAa,QAAA,CAAA1B,IAAA;QACA+C,MAAA,CAAAzC,IAAA;QACAyC,MAAA,CAAA1C,KAAA;QACA,IAAA4C,4CAAA,EAAAR,GAAA,CAAAR,OAAA,EAAAR,IAAA,WAAAC,QAAA;UACAqB,MAAA,CAAA3C,YAAA,GAAA2C,MAAA,CAAApB,UAAA,CAAAD,QAAA,CAAA1B,IAAA;UACA,IAAA+C,MAAA,CAAA3C,YAAA,CAAA2B,MAAA;YACA,IAAAmB,gBAAA;cAAAC,cAAA,EAAAJ,MAAA,CAAAlC,IAAA,CAAAE,QAAA;cAAAL,SAAA,EAAAqC,MAAA,CAAAlC,IAAA,CAAAH,SAAA;cAAAoB,QAAA;YAAA;YACAiB,MAAA,CAAA3C,YAAA,CAAAgD,IAAA,CAAAF,gBAAA;UACA;QACA;MACA;IACA;IACA;IACAG,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAC,OAAA,CAAAC,GAAA,CAAAL,MAAA,CAAAzC,IAAA,CAAAsC,cAAA;UACA,IAAAG,MAAA,CAAAzC,IAAA,CAAAoB,OAAA,KAAAtB,SAAA;YACA,IAAAiD,qBAAA,EAAAN,MAAA,CAAAzC,IAAA,EAAAY,IAAA,WAAAC,QAAA;cACA4B,MAAA,CAAAO,MAAA,CAAAC,UAAA;cACAR,MAAA,CAAAhD,IAAA;cACAgD,MAAA,CAAAjC,OAAA;YACA;UACA;YACA,IAAA0C,kBAAA,EAAAT,MAAA,CAAAzC,IAAA,EAAAY,IAAA,WAAAC,QAAA;cACA4B,MAAA,CAAAO,MAAA,CAAAC,UAAA;cACAR,MAAA,CAAAhD,IAAA;cACAgD,MAAA,CAAAjC,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACA2C,YAAA,WAAAA,aAAAvB,GAAA;MAAA,IAAAwB,MAAA;MACA,KAAAJ,MAAA,CAAAK,OAAA,gBAAAzB,GAAA,CAAA/B,SAAA,aAAAe,IAAA;QACA,WAAA0C,kBAAA,EAAA1B,GAAA,CAAAR,OAAA;MACA,GAAAR,IAAA;QACAwC,MAAA,CAAA5C,OAAA;QACA4C,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAM,KAAA;IACA;EACA;AACA", "ignoreList": []}]}