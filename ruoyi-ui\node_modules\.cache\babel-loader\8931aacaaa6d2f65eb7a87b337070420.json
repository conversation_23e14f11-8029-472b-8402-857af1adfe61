{"remainingRequest": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\views\\asc\\visitor\\self-register.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\views\\asc\\visitor\\self-register.vue", "mtime": 1750985468795}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\babel.config.js", "mtime": 1750833543308}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750836929039}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750836940534}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750836929039}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750836957095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_visitor", "require", "name", "data", "currentStep", "verifying", "submitting", "verify<PERSON><PERSON><PERSON>", "showManualInput", "showIdCardReader", "showFaceRecognition", "identityForm", "phone", "idType", "idNumber", "company", "identityRules", "required", "message", "trigger", "min", "max", "pattern", "visitForm", "reasonForVisit", "hostEmployeeName", "departmentVisited", "vehiclePlateNumber", "plannedEntryDatetime", "plannedExitDatetime", "visitRules", "companionList", "registrationId", "entryPickerOptions", "shortcuts", "text", "onClick", "picker", "$emit", "Date", "date", "setTime", "getTime", "setDate", "getDate", "setHours", "disabledDate", "time", "oneHourBefore", "now", "thirtyDaysLater", "exitPickerOptions", "computed", "progressWidth", "showVerifyOperation", "totalVisitors", "length", "methods", "selectVerifyMethod", "method", "resetVerifyDisplay", "backToVerifyMethod", "handleVerifyNext", "confirmIdentity", "_this", "$refs", "validate", "valid", "setTimeout", "$message", "success", "nextStep", "simulateIdCardRead", "_this2", "simulateFaceRecognition", "_this3", "submitRegistration", "prevStep", "addCompanion", "warning", "push", "onArrivalTimeChange", "value", "console", "log", "arrivalTime", "departureTime", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "concat", "onDepartureTimeChange", "_this4", "validateVisitors", "validateTimes", "submitData", "registration", "primaryContactName", "trim", "primaryContactPhone", "hostEmployeeId", "formatDateForBackend", "totalCompanions", "attendeesList", "visitorName", "visitorPhone", "visitorIdCard", "toUpperCase", "visitorCompany", "isPrimary", "visitorAvatarPhoto", "_toConsumableArray2", "default", "filter", "item", "map", "JSON", "stringify", "submitVisitorRegistration", "then", "response", "catch", "error", "msg", "finally", "test", "i", "visitor", "visitorTitle", "namePattern", "phonePattern", "idCard", "idPattern", "j", "dateStr", "isNaN", "resetRegister", "printCredential", "info", "showHelp", "$alert", "confirmButtonText", "contactService"], "sources": ["src/views/asc/visitor/self-register.vue"], "sourcesContent": ["<template>\r\n  <div class=\"pc-register-container\">\r\n    <!-- 顶部导航栏 -->\r\n    <div class=\"top-nav\">\r\n      <div class=\"nav-content\">\r\n        <div class=\"logo-section\">\r\n          <img src=\"@/assets/logo/logo.png\" alt=\"公司logo\" class=\"nav-logo\" />\r\n          <span class=\"company-name\">智慧园区访客登记系统</span>\r\n        </div>\r\n        <div class=\"nav-actions\">\r\n          <el-button type=\"text\" @click=\"showHelp\">使用帮助</el-button>\r\n          <el-button type=\"text\" @click=\"contactService\">联系客服</el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 主要内容区域 -->\r\n    <div class=\"main-content\">\r\n      <!-- 左侧信息面板 -->\r\n      <div class=\"info-panel\">\r\n        <div class=\"welcome-card\">\r\n          <div class=\"welcome-icon\">\r\n            <i class=\"el-icon-office-building\"></i>\r\n          </div>\r\n          <h2>欢迎访问智慧园区</h2>\r\n          <p class=\"welcome-desc\">为了您的安全和便利，请按照以下步骤完成访客登记</p>\r\n          \r\n          <div class=\"process-steps\">\r\n            <div class=\"step-item active\">\r\n              <div class=\"step-circle\">1</div>\r\n              <div class=\"step-text\">\r\n                <h4>填写信息</h4>\r\n                <p>完善访问详细信息</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"step-item\" :class=\"{ active: registrationCompleted }\">\r\n              <div class=\"step-circle\">2</div>\r\n              <div class=\"step-text\">\r\n                <h4>获取凭证</h4>\r\n                <p>生成访问凭证二维码</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"security-tips\">\r\n            <h4><i class=\"el-icon-lock\"></i> 安全提示</h4>\r\n            <ul>\r\n              <li>请确保提供真实有效的身份信息</li>\r\n              <li>您的个人信息将被严格保密</li>\r\n              <li>访问凭证仅限当次使用</li>\r\n            </ul>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 右侧表单区域 -->\r\n      <div class=\"form-panel\">\r\n        <!-- 进度指示器 -->\r\n        <div class=\"progress-indicator\">\r\n          <div class=\"progress-bar\">\r\n            <div class=\"progress-fill\" :style=\"{ width: progressWidth }\"></div>\r\n          </div>\r\n          <div class=\"progress-text\">\r\n            步骤 {{ currentStep + 1 }} / 3\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 表单内容 -->\r\n        <div class=\"form-content\">\r\n          <!-- 步骤1: 身份验证 -->\r\n          <div v-show=\"currentStep === 0\" class=\"step-form\">\r\n            <div class=\"form-header\">\r\n              <h3><i class=\"el-icon-user\"></i> 身份验证</h3>\r\n              <p>请选择合适的验证方式进行身份确认</p>\r\n            </div>\r\n            \r\n            <div class=\"verify-grid\">\r\n              <div class=\"verify-card\" \r\n                   :class=\"{ selected: verifyMethod === 'id_card' }\"\r\n                   @click=\"selectVerifyMethod('id_card')\">\r\n                <div class=\"verify-icon\">\r\n                  <i class=\"el-icon-postcard\"></i>\r\n                </div>\r\n                <h4>身份证验证</h4>\r\n                <p>使用身份证读卡器快速验证</p>\r\n                <div class=\"verify-status\" v-if=\"verifyMethod === 'id_card'\">\r\n                  <i class=\"el-icon-check\"></i>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"verify-card\"\r\n                   :class=\"{ selected: verifyMethod === 'face' }\"\r\n                   @click=\"selectVerifyMethod('face')\">\r\n                <div class=\"verify-icon\">\r\n                  <i class=\"el-icon-camera-solid\"></i>\r\n                </div>\r\n                <h4>人脸识别</h4>\r\n                <p>使用摄像头进行人脸识别</p>\r\n                <div class=\"verify-status\" v-if=\"verifyMethod === 'face'\">\r\n                  <i class=\"el-icon-check\"></i>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"verify-card\"\r\n                   :class=\"{ selected: verifyMethod === 'manual' }\"\r\n                   @click=\"selectVerifyMethod('manual')\">\r\n                <div class=\"verify-icon\">\r\n                  <i class=\"el-icon-edit-outline\"></i>\r\n                </div>\r\n                <h4>手动输入</h4>\r\n                <p>手动填写身份信息</p>\r\n                <div class=\"verify-status\" v-if=\"verifyMethod === 'manual'\">\r\n                  <i class=\"el-icon-check\"></i>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"verify-card\"\r\n                   :class=\"{ selected: verifyMethod === 'passport' }\"\r\n                   @click=\"selectVerifyMethod('passport')\">\r\n                <div class=\"verify-icon\">\r\n                  <i class=\"el-icon-document\"></i>\r\n                </div>\r\n                <h4>护照验证</h4>\r\n                <p>使用护照等其他证件</p>\r\n                <div class=\"verify-status\" v-if=\"verifyMethod === 'passport'\">\r\n                  <i class=\"el-icon-check\"></i>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 验证方式具体操作区域 -->\r\n            <div v-if=\"showManualInput\" class=\"verify-operation\">\r\n              <div class=\"operation-header\">\r\n                <h4>请填写身份信息</h4>\r\n                <el-button type=\"text\" @click=\"backToVerifyMethod\">\r\n                  <i class=\"el-icon-arrow-left\"></i> 重新选择验证方式\r\n                </el-button>\r\n              </div>\r\n              \r\n              <el-form ref=\"identityForm\" :model=\"identityForm\" :rules=\"identityRules\" \r\n                       label-width=\"100px\" class=\"identity-form\">\r\n                <el-row :gutter=\"20\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"姓名\" prop=\"name\">\r\n                      <el-input v-model=\"identityForm.name\" placeholder=\"请输入真实姓名\" \r\n                                prefix-icon=\"el-icon-user\" />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"手机号\" prop=\"phone\">\r\n                      <el-input v-model=\"identityForm.phone\" placeholder=\"请输入手机号码\"\r\n                                prefix-icon=\"el-icon-phone\" />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                \r\n                <el-row :gutter=\"20\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"证件类型\" prop=\"idType\">\r\n                      <el-select v-model=\"identityForm.idType\" placeholder=\"请选择证件类型\" style=\"width: 100%\">\r\n                        <el-option label=\"身份证\" value=\"id_card\" />\r\n                        <el-option label=\"护照\" value=\"passport\" />\r\n                        <el-option label=\"港澳通行证\" value=\"hk_mo_pass\" />\r\n                        <el-option label=\"台湾通行证\" value=\"tw_pass\" />\r\n                        <el-option label=\"其他\" value=\"other\" />\r\n                      </el-select>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"证件号码\" prop=\"idNumber\">\r\n                      <el-input v-model=\"identityForm.idNumber\" placeholder=\"请输入证件号码\"\r\n                                prefix-icon=\"el-icon-postcard\" />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                \r\n                <el-form-item label=\"所属公司\" prop=\"company\">\r\n                  <el-input v-model=\"identityForm.company\" placeholder=\"请输入公司名称\"\r\n                            prefix-icon=\"el-icon-office-building\" />\r\n                </el-form-item>\r\n              </el-form>\r\n            </div>\r\n\r\n            <!-- 身份证读卡器界面 -->\r\n            <div v-if=\"showIdCardReader\" class=\"verify-operation\">\r\n              <div class=\"operation-header\">\r\n                <h4>身份证读卡验证</h4>\r\n                <el-button type=\"text\" @click=\"backToVerifyMethod\">\r\n                  <i class=\"el-icon-arrow-left\"></i> 重新选择验证方式\r\n                </el-button>\r\n              </div>\r\n              \r\n              <div class=\"id-card-reader\">\r\n                <div class=\"reader-visual\">\r\n                  <div class=\"reader-animation\">\r\n                    <i class=\"el-icon-loading rotating\"></i>\r\n                  </div>\r\n                  <h4>请将身份证放置在读卡器上</h4>\r\n                  <p>确保身份证平放在读卡器感应区域</p>\r\n                  <div class=\"reader-tips\">\r\n                    <el-alert title=\"提示\" type=\"info\" :closable=\"false\">\r\n                      如果读卡失败，请检查身份证是否放置正确，或选择其他验证方式\r\n                    </el-alert>\r\n                  </div>\r\n                  <el-button type=\"primary\" @click=\"simulateIdCardRead\" style=\"margin-top: 20px;\">\r\n                    模拟读取成功\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 人脸识别界面 -->\r\n            <div v-if=\"showFaceRecognition\" class=\"verify-operation\">\r\n              <div class=\"operation-header\">\r\n                <h4>人脸识别验证</h4>\r\n                <el-button type=\"text\" @click=\"backToVerifyMethod\">\r\n                  <i class=\"el-icon-arrow-left\"></i> 重新选择验证方式\r\n                </el-button>\r\n              </div>\r\n              \r\n              <div class=\"face-recognition\">\r\n                <div class=\"camera-container\">\r\n                  <div class=\"camera-frame\">\r\n                    <div class=\"camera-overlay\">\r\n                      <div class=\"face-outline\"></div>\r\n                      <i class=\"el-icon-camera-solid camera-icon\"></i>\r\n                    </div>\r\n                    <div class=\"scanning-line\"></div>\r\n                  </div>\r\n                  <h4>请面向摄像头</h4>\r\n                  <p>保持面部清晰可见，等待识别完成</p>\r\n                  <el-button type=\"primary\" @click=\"simulateFaceRecognition\" style=\"margin-top: 20px;\">\r\n                    模拟识别成功\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 步骤2: 填写访问信息 -->\r\n          <div v-show=\"currentStep === 1\" class=\"step-form\">\r\n            <div class=\"form-header\">\r\n              <h3><i class=\"el-icon-edit\"></i> 完善访问信息</h3>\r\n              <p>请填写详细的访问信息</p>\r\n            </div>\r\n\r\n            <el-form ref=\"visitForm\" :model=\"visitForm\" :rules=\"visitRules\" \r\n                     label-width=\"120px\" class=\"visit-form\">\r\n              \r\n              <!-- 基本访问信息 -->\r\n              <div class=\"form-section\">\r\n                <h4 class=\"section-title\">基本访问信息</h4>\r\n                <el-row :gutter=\"20\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"来访事由\" prop=\"reasonForVisit\">\r\n                      <el-input v-model=\"visitForm.reasonForVisit\" placeholder=\"请输入来访事由\"\r\n                                prefix-icon=\"el-icon-tickets\" type=\"textarea\" :rows=\"3\" maxlength=\"200\" show-word-limit />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"被访人姓名\" prop=\"hostEmployeeName\">\r\n                      <el-input v-model=\"visitForm.hostEmployeeName\" placeholder=\"请输入被访人姓名\"\r\n                                prefix-icon=\"el-icon-user\" maxlength=\"20\" />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n\r\n                <el-row :gutter=\"20\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"被访部门\" prop=\"departmentVisited\">\r\n                      <el-input v-model=\"visitForm.departmentVisited\" placeholder=\"请输入被访部门\"\r\n                                prefix-icon=\"el-icon-office-building\" maxlength=\"50\" />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"车牌号\">\r\n                      <el-input v-model=\"visitForm.vehiclePlateNumber\" placeholder=\"如有车辆请填写车牌号，多个用逗号分隔\"\r\n                                prefix-icon=\"el-icon-truck\" />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n              </div>\r\n\r\n              <!-- 时间信息 -->\r\n              <div class=\"form-section\">\r\n                <h4 class=\"section-title\">访问时间</h4>\r\n                <el-row :gutter=\"20\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"预计到访时间\" prop=\"plannedEntryDatetime\">\r\n                      <el-date-picker\r\n                        v-model=\"visitForm.plannedEntryDatetime\"\r\n                        type=\"datetime\"\r\n                        placeholder=\"选择到访时间\"\r\n                        style=\"width: 100%\"\r\n                        value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                        :picker-options=\"entryPickerOptions\"\r\n                        @change=\"onArrivalTimeChange\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"预计离开时间\" prop=\"plannedExitDatetime\">\r\n                      <el-date-picker\r\n                        v-model=\"visitForm.plannedExitDatetime\"\r\n                        type=\"datetime\"\r\n                        placeholder=\"选择离开时间\"\r\n                        style=\"width: 100%\"\r\n                        value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                        :picker-options=\"exitPickerOptions\"\r\n                        @change=\"onDepartureTimeChange\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n              </div>\r\n\r\n              <!-- 同行人员信息 -->\r\n              <div class=\"form-section\">\r\n                <div class=\"section-header\">\r\n                  <h4 class=\"section-title\">访客信息（第一位为主联系人）共 {{ totalVisitors }} 人</h4>\r\n                  <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"addCompanion\" :disabled=\"companionList.length >= 9\">\r\n                    添加访客\r\n                  </el-button>\r\n                </div>\r\n                \r\n                <div v-if=\"companionList.length === 0\" class=\"no-companions\">\r\n                  <i class=\"el-icon-user-solid\"></i>\r\n                  <p>暂无同行人员，主访客信息已在身份验证步骤中填写</p>\r\n                </div>\r\n                \r\n                <div v-else class=\"companions-table\">\r\n                  <el-table :data=\"companionList\" border style=\"width: 100%;\">\r\n                    <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        访客{{ scope.$index + 2 }}\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"姓名\" width=\"150\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-input v-model=\"scope.row.name\" placeholder=\"请输入姓名\" size=\"small\" maxlength=\"20\" />\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"手机号\" width=\"150\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-input v-model=\"scope.row.phone\" placeholder=\"请输入手机号\" size=\"small\" />\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"身份证号\" min-width=\"180\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-input v-model=\"scope.row.idNumber\" placeholder=\"请输入身份证号\" size=\"small\" />\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"公司\" width=\"150\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-input v-model=\"scope.row.company\" placeholder=\"请输入公司（可选）\" size=\"small\" maxlength=\"100\" />\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"操作\" width=\"80\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-button size=\"mini\" type=\"danger\" icon=\"el-icon-delete\" \r\n                                   @click=\"removeCompanion(scope.$index)\" circle />\r\n                      </template>\r\n                    </el-table-column>\r\n                  </el-table>\r\n                </div>\r\n              </div>\r\n            </el-form>\r\n          </div>\r\n\r\n          <!-- 步骤3: 完成登记 -->\r\n          <div v-show=\"currentStep === 2\" class=\"step-form\">\r\n            <div class=\"form-header\">\r\n              <h3><i class=\"el-icon-circle-check\"></i> 登记完成</h3>\r\n              <p>您的访客登记已成功提交</p>\r\n            </div>\r\n\r\n            <div class=\"success-content\">\r\n              <div class=\"success-icon\">\r\n                <i class=\"el-icon-circle-check\"></i>\r\n              </div>\r\n              \r\n              <h4>登记成功！</h4>\r\n              <p class=\"success-message\">您的访客登记已提交，请等待审核通过</p>\r\n              \r\n              <!-- 登记信息摘要 -->\r\n              <div class=\"register-summary\">\r\n                <h5>登记信息摘要</h5>\r\n                <el-descriptions :column=\"2\" border>\r\n                  <el-descriptions-item label=\"访客姓名\">\r\n                    {{ identityForm.name }}\r\n                  </el-descriptions-item>\r\n                  <el-descriptions-item label=\"联系电话\">\r\n                    {{ identityForm.phone }}\r\n                  </el-descriptions-item>\r\n                  <el-descriptions-item label=\"被访人\">\r\n                    {{ visitForm.hostEmployeeName }}\r\n                  </el-descriptions-item>\r\n                  <el-descriptions-item label=\"被访部门\">\r\n                    {{ visitForm.departmentVisited }}\r\n                  </el-descriptions-item>\r\n                  <el-descriptions-item label=\"来访事由\" span=\"2\">\r\n                    {{ visitForm.reasonForVisit }}\r\n                  </el-descriptions-item>\r\n                  <el-descriptions-item label=\"预计到访时间\">\r\n                    {{ parseTime(visitForm.plannedEntryDatetime) }}\r\n                  </el-descriptions-item>\r\n                  <el-descriptions-item label=\"预计离开时间\">\r\n                    {{ parseTime(visitForm.plannedExitDatetime) }}\r\n                  </el-descriptions-item>\r\n                </el-descriptions>\r\n              </div>\r\n\r\n              <!-- 访问凭证 -->\r\n              <div class=\"access-credential\" v-if=\"registrationId\">\r\n                <h5>访问凭证</h5>\r\n                <div class=\"credential-card\">\r\n                  <div class=\"qr-code-container\">\r\n                    <div class=\"qr-code\">\r\n                      <i class=\"el-icon-qrcode\"></i>\r\n                    </div>\r\n                    <p class=\"qr-code-id\">{{ registrationId }}</p>\r\n                  </div>\r\n                  <div class=\"credential-info\">\r\n                    <h6>使用说明</h6>\r\n                    <ul>\r\n                      <li>请保存此二维码截图</li>\r\n                      <li>审核通过后可用于园区门禁</li>\r\n                      <li>凭证仅限当次访问使用</li>\r\n                      <li>如有疑问请联系园区前台</li>\r\n                    </ul>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 底部操作按钮 -->\r\n        <div class=\"form-actions\">\r\n          <el-button v-if=\"currentStep > 0\" @click=\"prevStep\" size=\"large\">\r\n            <i class=\"el-icon-arrow-left\"></i> 上一步\r\n          </el-button>\r\n          \r\n          <el-button v-if=\"currentStep === 0 && verifyMethod && !showVerifyOperation\" \r\n                     type=\"primary\" @click=\"handleVerifyNext\" size=\"large\" :loading=\"verifying\">\r\n            下一步 <i class=\"el-icon-arrow-right\"></i>\r\n          </el-button>\r\n          \r\n          <el-button v-if=\"currentStep === 0 && showManualInput\" \r\n                     type=\"primary\" @click=\"confirmIdentity\" size=\"large\" :loading=\"verifying\">\r\n            确认身份 <i class=\"el-icon-arrow-right\"></i>\r\n          </el-button>\r\n          \r\n          <el-button v-if=\"currentStep === 1\" \r\n                     type=\"primary\" @click=\"nextStep\" size=\"large\" :loading=\"submitting\">\r\n            提交登记 <i class=\"el-icon-arrow-right\"></i>\r\n          </el-button>\r\n          \r\n          <div v-if=\"currentStep === 2\" class=\"final-actions\">\r\n            <el-button type=\"primary\" @click=\"printCredential\" size=\"large\">\r\n              <i class=\"el-icon-printer\"></i> 打印凭证\r\n            </el-button>\r\n            <el-button @click=\"resetRegister\" size=\"large\">\r\n              <i class=\"el-icon-refresh\"></i> 重新登记\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { submitVisitorRegistration } from \"@/api/asc/visitor\";\r\n\r\nexport default {\r\n  name: \"SelfRegister\",\r\n  data() {\r\n    return {\r\n      currentStep: 0,\r\n      verifying: false,\r\n      submitting: false,\r\n      \r\n      // 验证方式\r\n      verifyMethod: '',\r\n      showManualInput: false,\r\n      showIdCardReader: false,\r\n      showFaceRecognition: false,\r\n      \r\n      // 身份信息\r\n      identityForm: {\r\n        name: '',\r\n        phone: '',\r\n        idType: '',\r\n        idNumber: '',\r\n        company: ''\r\n      },\r\n      identityRules: {\r\n        name: [\r\n          { required: true, message: '请输入姓名', trigger: 'blur' },\r\n          { min: 2, max: 20, message: '姓名长度应在2-20个字符之间', trigger: 'blur' },\r\n          { pattern: /^[\\u4e00-\\u9fa5a-zA-Z·\\s]+$/, message: '姓名只能包含中文、英文字母和常见符号', trigger: 'blur' }\r\n        ],\r\n        phone: [\r\n          { required: true, message: '请输入手机号', trigger: 'blur' },\r\n          { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }\r\n        ],\r\n        idType: [{ required: true, message: '请选择证件类型', trigger: 'change' }],\r\n        idNumber: [\r\n          { required: true, message: '请输入证件号码', trigger: 'blur' },\r\n          { min: 15, max: 18, message: '证件号码长度不正确', trigger: 'blur' }\r\n        ],\r\n        company: [{ max: 100, message: '公司名称不能超过100个字符', trigger: 'blur' }]\r\n      },\r\n      \r\n      // 访问信息\r\n      visitForm: {\r\n        reasonForVisit: '',\r\n        hostEmployeeName: '',\r\n        departmentVisited: '',\r\n        vehiclePlateNumber: '',\r\n        plannedEntryDatetime: null,\r\n        plannedExitDatetime: null\r\n      },\r\n      visitRules: {\r\n        reasonForVisit: [\r\n          { required: true, message: '请输入来访事由', trigger: 'blur' },\r\n          { min: 2, max: 200, message: '来访事由长度应在2-200个字符之间', trigger: 'blur' }\r\n        ],\r\n        hostEmployeeName: [\r\n          { required: true, message: '请输入被访人姓名', trigger: 'blur' },\r\n          { min: 2, max: 20, message: '被访人姓名长度应在2-20个字符之间', trigger: 'blur' }\r\n        ],\r\n        departmentVisited: [\r\n          { required: true, message: '请输入被访部门', trigger: 'blur' },\r\n          { min: 2, max: 50, message: '部门名称长度应在2-50个字符之间', trigger: 'blur' }\r\n        ],\r\n        plannedEntryDatetime: [{ required: true, message: '请选择预计到访时间', trigger: 'change' }],\r\n        plannedExitDatetime: [{ required: true, message: '请选择预计离开时间', trigger: 'change' }]\r\n      },\r\n      \r\n      // 同行人员\r\n      companionList: [],\r\n      \r\n      // 登记结果\r\n      registrationId: null,\r\n\r\n      // 时间选择器配置\r\n      entryPickerOptions: {\r\n        shortcuts: [{\r\n          text: '现在',\r\n          onClick(picker) {\r\n            picker.$emit('pick', new Date());\r\n          }\r\n        }, {\r\n          text: '1小时后',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setTime(date.getTime() + 3600 * 1000);\r\n            picker.$emit('pick', date);\r\n          }\r\n        }, {\r\n          text: '明天上午9点',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setDate(date.getDate() + 1);\r\n            date.setHours(9, 0, 0, 0);\r\n            picker.$emit('pick', date);\r\n          }\r\n        }],\r\n        disabledDate(time) {\r\n          // 允许选择当前时间前1小时到未来30天\r\n          const oneHourBefore = Date.now() - 3600 * 1000;\r\n          const thirtyDaysLater = Date.now() + 30 * 24 * 3600 * 1000;\r\n          return time.getTime() < oneHourBefore || time.getTime() > thirtyDaysLater;\r\n        }\r\n      },\r\n      \r\n      exitPickerOptions: {\r\n        shortcuts: [{\r\n          text: '2小时后',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setTime(date.getTime() + 2 * 3600 * 1000);\r\n            picker.$emit('pick', date);\r\n          }\r\n        }, {\r\n          text: '今天下午6点',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setHours(18, 0, 0, 0);\r\n            if (date.getTime() < Date.now()) {\r\n              date.setDate(date.getDate() + 1);\r\n            }\r\n            picker.$emit('pick', date);\r\n          }\r\n        }, {\r\n          text: '明天下午6点',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setDate(date.getDate() + 1);\r\n            date.setHours(18, 0, 0, 0);\r\n            picker.$emit('pick', date);\r\n          }\r\n        }],\r\n        disabledDate(time) {\r\n          // 允许选择当前时间到未来30天\r\n          const thirtyDaysLater = Date.now() + 30 * 24 * 3600 * 1000;\r\n          return time.getTime() < Date.now() - 8.64e7 || time.getTime() > thirtyDaysLater;\r\n        }\r\n      }\r\n    };\r\n  },\r\n  \r\n  computed: {\r\n    progressWidth() {\r\n      return ((this.currentStep + 1) / 3) * 100 + '%';\r\n    },\r\n    \r\n    showVerifyOperation() {\r\n      return this.showManualInput || this.showIdCardReader || this.showFaceRecognition;\r\n    },\r\n\r\n    // 总访客人数（主访客 + 同行人员）\r\n    totalVisitors() {\r\n      return 1 + this.companionList.length;\r\n    }\r\n  },\r\n  \r\n  methods: {\r\n    /** 选择验证方式 */\r\n    selectVerifyMethod(method) {\r\n      this.verifyMethod = method;\r\n      this.resetVerifyDisplay();\r\n      \r\n      switch (method) {\r\n        case 'manual':\r\n          this.showManualInput = true;\r\n          break;\r\n        case 'id_card':\r\n          this.showIdCardReader = true;\r\n          break;\r\n        case 'face':\r\n          this.showFaceRecognition = true;\r\n          break;\r\n        case 'passport':\r\n          this.showManualInput = true;\r\n          this.identityForm.idType = 'passport';\r\n          break;\r\n      }\r\n    },\r\n    \r\n    /** 重置验证显示 */\r\n    resetVerifyDisplay() {\r\n      this.showManualInput = false;\r\n      this.showIdCardReader = false;\r\n      this.showFaceRecognition = false;\r\n    },\r\n    \r\n    /** 返回验证方式选择 */\r\n    backToVerifyMethod() {\r\n      this.resetVerifyDisplay();\r\n      this.verifyMethod = '';\r\n    },\r\n\r\n    /** 处理验证方式下一步 */\r\n    handleVerifyNext() {\r\n      if (this.verifyMethod === 'manual') {\r\n        this.showManualInput = true;\r\n      } else if (this.verifyMethod === 'id_card') {\r\n        this.showIdCardReader = true;\r\n      } else if (this.verifyMethod === 'face') {\r\n        this.showFaceRecognition = true;\r\n      } else if (this.verifyMethod === 'passport') {\r\n        this.showManualInput = true;\r\n        this.identityForm.idType = 'passport';\r\n      }\r\n    },\r\n    \r\n    /** 确认身份 */\r\n    confirmIdentity() {\r\n      this.$refs.identityForm.validate(valid => {\r\n        if (valid) {\r\n          this.verifying = true;\r\n          // 模拟验证过程\r\n          setTimeout(() => {\r\n            this.verifying = false;\r\n            this.$message.success('身份验证成功');\r\n            this.nextStep();\r\n          }, 1000);\r\n        }\r\n      });\r\n    },\r\n    \r\n    /** 模拟身份证读取 */\r\n    simulateIdCardRead() {\r\n      this.identityForm = {\r\n        name: '张三',\r\n        phone: '13800138000',\r\n        idType: 'id_card',\r\n        idNumber: '110101199001011234',\r\n        company: '某某科技有限公司'\r\n      };\r\n      this.$message.success('身份证信息读取成功');\r\n      setTimeout(() => {\r\n        this.nextStep();\r\n      }, 1000);\r\n    },\r\n    \r\n    /** 模拟人脸识别 */\r\n    simulateFaceRecognition() {\r\n      this.identityForm = {\r\n        name: '李四',\r\n        phone: '13900139000',\r\n        idType: 'id_card',\r\n        idNumber: '110101199002021234',\r\n        company: '某某贸易有限公司'\r\n      };\r\n      this.$message.success('人脸识别成功');\r\n      setTimeout(() => {\r\n        this.nextStep();\r\n      }, 1000);\r\n    },\r\n    \r\n    /** 下一步 */\r\n    nextStep() {\r\n      if (this.currentStep === 1) {\r\n        this.submitRegistration();\r\n      } else {\r\n        this.currentStep++;\r\n      }\r\n    },\r\n    \r\n    /** 上一步 */\r\n    prevStep() {\r\n      this.currentStep--;\r\n      if (this.currentStep === 0) {\r\n        this.resetVerifyDisplay();\r\n      }\r\n    },\r\n    \r\n    /** 添加同行人员 */\r\n    addCompanion() {\r\n      if (this.companionList.length >= 9) {\r\n        this.$message.warning('最多只能添加9名同行人员（总计10名访客）');\r\n        return;\r\n      }\r\n\r\n      this.companionList.push({\r\n        name: '',\r\n        phone: '',\r\n        idNumber: '',\r\n        company: ''\r\n      });\r\n      \r\n      this.$message.success('已添加访客');\r\n    },\r\n    \r\n    /** 来访时间变更事件 */\r\n    onArrivalTimeChange(value) {\r\n      console.log('预计来访时间变更:', value);\r\n      // 自动设置离开时间为来访时间后4小时\r\n      if (value && !this.visitForm.plannedExitDatetime) {\r\n        const arrivalTime = new Date(value);\r\n        const departureTime = new Date(arrivalTime.getTime() + 4 * 60 * 60 * 1000);\r\n        \r\n        const year = departureTime.getFullYear();\r\n        const month = String(departureTime.getMonth() + 1).padStart(2, '0');\r\n        const day = String(departureTime.getDate()).padStart(2, '0');\r\n        const hours = String(departureTime.getHours()).padStart(2, '0');\r\n        const minutes = String(departureTime.getMinutes()).padStart(2, '0');\r\n        const seconds = String(departureTime.getSeconds()).padStart(2, '0');\r\n        \r\n        this.visitForm.plannedExitDatetime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n      }\r\n    },\r\n\r\n    /** 离开时间变更事件 */\r\n    onDepartureTimeChange(value) {\r\n      console.log('预计离开时间变更:', value);\r\n      // 验证离开时间不能早于来访时间\r\n      if (value && this.visitForm.plannedEntryDatetime) {\r\n        const arrivalTime = new Date(this.visitForm.plannedEntryDatetime);\r\n        const departureTime = new Date(value);\r\n\r\n        if (departureTime <= arrivalTime) {\r\n          this.$message.warning('预计离开时间不能早于或等于来访时间');\r\n          this.visitForm.plannedExitDatetime = '';\r\n        }\r\n      }\r\n    },\r\n    \r\n    /** 提交登记 */\r\n    submitRegistration() {\r\n      this.$refs.visitForm.validate(valid => {\r\n        if (valid) {\r\n          // 验证同行人员信息\r\n          if (!this.validateVisitors()) {\r\n            return;\r\n          }\r\n\r\n          // 验证时间\r\n          if (!this.validateTimes()) {\r\n            return;\r\n          }\r\n\r\n          this.submitting = true;\r\n          \r\n          // 构建提交数据 - 按照微信端相同的数据结构\r\n          const submitData = {\r\n            // VisitRegistrations 对象\r\n            registration: {\r\n              primaryContactName: this.identityForm.name.trim(),\r\n              primaryContactPhone: this.identityForm.phone.trim(),\r\n              reasonForVisit: this.visitForm.reasonForVisit.trim(),\r\n              hostEmployeeName: this.visitForm.hostEmployeeName.trim(),\r\n              hostEmployeeId: null, // 前端暂时没有被访人ID，后端可能会自动处理\r\n              departmentVisited: this.visitForm.departmentVisited.trim(),\r\n              plannedEntryDatetime: this.formatDateForBackend(this.visitForm.plannedEntryDatetime),\r\n              plannedExitDatetime: this.formatDateForBackend(this.visitForm.plannedExitDatetime),\r\n              vehiclePlateNumber: this.visitForm.vehiclePlateNumber ? this.visitForm.vehiclePlateNumber.trim() : '',\r\n              totalCompanions: this.companionList.length\r\n            },\r\n            // RegistrationAttendees 数组 - 使用后端期望的临时字段\r\n            attendeesList: [\r\n              // 主访客\r\n              {\r\n                visitorName: this.identityForm.name.trim(),\r\n                visitorPhone: this.identityForm.phone.trim(),\r\n                visitorIdCard: this.identityForm.idNumber.trim().toUpperCase(),\r\n                visitorCompany: this.identityForm.company ? this.identityForm.company.trim() : '',\r\n                isPrimary: \"1\", // 第一个访客必须是主联系人\r\n                visitorAvatarPhoto: null // 暂时不支持头像上传\r\n              },\r\n              // 同行人员\r\n              ...this.companionList\r\n                .filter(item => item.name && item.phone && item.idNumber)\r\n                .map(item => ({\r\n                  visitorName: item.name.trim(),\r\n                  visitorPhone: item.phone.trim(),\r\n                  visitorIdCard: item.idNumber.trim().toUpperCase(),\r\n                  visitorCompany: item.company ? item.company.trim() : '',\r\n                  isPrimary: \"0\",\r\n                  visitorAvatarPhoto: null\r\n                }))\r\n            ]\r\n          };\r\n\r\n          console.log('提交数据结构:', JSON.stringify(submitData, null, 2));\r\n          \r\n          submitVisitorRegistration(submitData)\r\n            .then(response => {\r\n              this.registrationId = response.data || 'VR' + Date.now();\r\n              this.$message.success(`${this.totalVisitors}名访客登记成功！`);\r\n              this.currentStep++;\r\n            })\r\n            .catch(error => {\r\n              this.$message.error(error.msg || '登记失败，请重试');\r\n            })\r\n            .finally(() => {\r\n              this.submitting = false;\r\n            });\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 验证访客信息 */\r\n    validateVisitors() {\r\n      // 验证主访客信息（已在身份验证步骤中验证，这里再次确认）\r\n      if (!this.identityForm.name || this.identityForm.name.trim().length < 2) {\r\n        this.$message.error('主访客姓名不能为空且长度不能少于2个字符');\r\n        return false;\r\n      }\r\n\r\n      if (!this.identityForm.phone || !/^1[3-9]\\d{9}$/.test(this.identityForm.phone.trim())) {\r\n        this.$message.error('主访客手机号格式不正确');\r\n        return false;\r\n      }\r\n\r\n      if (!this.identityForm.idNumber || this.identityForm.idNumber.trim().length < 15) {\r\n        this.$message.error('主访客身份证号不能为空');\r\n        return false;\r\n      }\r\n\r\n      // 验证同行人员信息\r\n      for (let i = 0; i < this.companionList.length; i++) {\r\n        const visitor = this.companionList[i];\r\n        const visitorTitle = `访客${i + 2}`;\r\n\r\n        // 验证姓名\r\n        if (!visitor.name || visitor.name.trim() === '') {\r\n          this.$message.error(`请输入${visitorTitle}的姓名`);\r\n          return false;\r\n        }\r\n        \r\n        if (visitor.name.trim().length < 2 || visitor.name.trim().length > 20) {\r\n          this.$message.error(`${visitorTitle}的姓名长度应在2-20个字符之间`);\r\n          return false;\r\n        }\r\n\r\n        // 验证姓名格式\r\n        const namePattern = /^[\\u4e00-\\u9fa5a-zA-Z·\\s]+$/;\r\n        if (!namePattern.test(visitor.name.trim())) {\r\n          this.$message.error(`${visitorTitle}的姓名只能包含中文、英文字母和常见符号`);\r\n          return false;\r\n        }\r\n\r\n        // 验证手机号\r\n        if (!visitor.phone || visitor.phone.trim() === '') {\r\n          this.$message.error(`请输入${visitorTitle}的手机号`);\r\n          return false;\r\n        }\r\n\r\n        const phonePattern = /^1[3-9]\\d{9}$/;\r\n        if (!phonePattern.test(visitor.phone.trim())) {\r\n          this.$message.error(`${visitorTitle}的手机号格式不正确`);\r\n          return false;\r\n        }\r\n\r\n        // 验证身份证号\r\n        if (!visitor.idNumber || visitor.idNumber.trim() === '') {\r\n          this.$message.error(`请输入${visitorTitle}的身份证号`);\r\n          return false;\r\n        }\r\n        \r\n        const idCard = visitor.idNumber.trim().toUpperCase();\r\n        \r\n        // 如果是18位身份证号，验证格式\r\n        if (idCard.length === 18) {\r\n          const idPattern = /^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$/;\r\n          if (!idPattern.test(idCard)) {\r\n            this.$message.error(`${visitorTitle}的身份证号格式不正确`);\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 检查身份证号是否与主访客重复\r\n        if (visitor.idNumber.trim().toUpperCase() === this.identityForm.idNumber.trim().toUpperCase()) {\r\n          this.$message.error(`${visitorTitle}与主访客的身份证号重复`);\r\n          return false;\r\n        }\r\n\r\n        // 检查身份证号是否与其他同行人员重复\r\n        for (let j = 0; j < this.companionList.length; j++) {\r\n          if (i !== j && visitor.idNumber.trim().toUpperCase() === this.companionList[j].idNumber.trim().toUpperCase()) {\r\n            this.$message.error(`${visitorTitle}与访客${j + 2}的身份证号重复`);\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 检查手机号是否与主访客重复\r\n        if (visitor.phone.trim() === this.identityForm.phone.trim()) {\r\n          this.$message.error(`${visitorTitle}与主访客的手机号重复，请确认是否为同一人`);\r\n          return false;\r\n        }\r\n\r\n        // 检查手机号是否与其他同行人员重复\r\n        for (let j = 0; j < this.companionList.length; j++) {\r\n          if (i !== j && visitor.phone.trim() === this.companionList[j].phone.trim()) {\r\n            this.$message.error(`${visitorTitle}与访客${j + 2}的手机号重复，请确认是否为同一人`);\r\n            return false;\r\n          }\r\n        }\r\n        \r\n        // 清理和标准化数据\r\n        visitor.name = visitor.name.trim();\r\n        visitor.phone = visitor.phone.trim();\r\n        visitor.idNumber = visitor.idNumber.trim().toUpperCase();\r\n        visitor.company = (visitor.company || '').trim();\r\n      }\r\n\r\n      return true;\r\n    },\r\n\r\n    /** 验证时间 */\r\n    validateTimes() {\r\n      if (!this.visitForm.plannedEntryDatetime) {\r\n        this.$message.error('请选择预计来访时间');\r\n        return false;\r\n      }\r\n\r\n      if (!this.visitForm.plannedExitDatetime) {\r\n        this.$message.error('请选择预计离开时间');\r\n        return false;\r\n      }\r\n\r\n      const arrivalTime = new Date(this.visitForm.plannedEntryDatetime);\r\n      const departureTime = new Date(this.visitForm.plannedExitDatetime);\r\n      const now = new Date();\r\n\r\n      // 验证来访时间不能是过去时间（允许当前时间前1小时的误差）\r\n      const oneHourBefore = new Date(now.getTime() - 60 * 60 * 1000);\r\n      if (arrivalTime < oneHourBefore) {\r\n        this.$message.error('预计来访时间不能是过去时间');\r\n        return false;\r\n      }\r\n\r\n      // 验证离开时间必须晚于来访时间\r\n      if (departureTime <= arrivalTime) {\r\n        this.$message.error('预计离开时间必须晚于来访时间');\r\n        return false;\r\n      }\r\n\r\n      return true;\r\n    },\r\n\r\n    /** 格式化日期为后端期望的格式 */\r\n    formatDateForBackend(dateStr) {\r\n      if (!dateStr) return '';\r\n      \r\n      const date = new Date(dateStr);\r\n      if (isNaN(date.getTime())) return '';\r\n      \r\n      const year = date.getFullYear();\r\n      const month = String(date.getMonth() + 1).padStart(2, '0');\r\n      const day = String(date.getDate()).padStart(2, '0');\r\n      const hours = String(date.getHours()).padStart(2, '0');\r\n      const minutes = String(date.getMinutes()).padStart(2, '0');\r\n      const seconds = String(date.getSeconds()).padStart(2, '0');\r\n      \r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n    },\r\n    \r\n    /** 重新登记 */\r\n    resetRegister() {\r\n      this.currentStep = 0;\r\n      this.resetVerifyDisplay();\r\n      this.verifyMethod = '';\r\n      this.identityForm = {\r\n        name: '',\r\n        phone: '',\r\n        idType: '',\r\n        idNumber: '',\r\n        company: ''\r\n      };\r\n      this.visitForm = {\r\n        reasonForVisit: '',\r\n        hostEmployeeName: '',\r\n        departmentVisited: '',\r\n        vehiclePlateNumber: '',\r\n        plannedEntryDatetime: null,\r\n        plannedExitDatetime: null\r\n      };\r\n      this.companionList = [];\r\n      this.registrationId = null;\r\n    },\r\n    \r\n    /** 打印凭证 */\r\n    printCredential() {\r\n      this.$message.info('正在生成打印文件...');\r\n      // TODO: 实现打印功能\r\n    },\r\n\r\n    /** 显示帮助 */\r\n    showHelp() {\r\n      this.$alert('如需帮助，请联系园区前台或拨打服务热线', '使用帮助', {\r\n        confirmButtonText: '确定'\r\n      });\r\n    },\r\n\r\n    /** 联系客服 */\r\n    contactService() {\r\n      this.$alert('服务热线：400-1234-567\\n服务时间：周一至周五 8:00-18:00', '联系客服', {\r\n        confirmButtonText: '确定'\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 主容器 */\r\n.pc-register-container {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n  font-family: 'Helvetica Neue', Arial, sans-serif;\r\n}\r\n\r\n/* 顶部导航 */\r\n.top-nav {\r\n  background: #fff;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  padding: 0 40px;\r\n  position: sticky;\r\n  top: 0;\r\n  z-index: 100;\r\n}\r\n\r\n.nav-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  height: 70px;\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.logo-section {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n}\r\n\r\n.nav-logo {\r\n  height: 40px;\r\n}\r\n\r\n.company-name {\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n.nav-actions {\r\n  display: flex;\r\n  gap: 20px;\r\n}\r\n\r\n/* 主内容区域 */\r\n.main-content {\r\n  display: flex;\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n  padding: 40px;\r\n  gap: 40px;\r\n  min-height: calc(100vh - 110px);\r\n}\r\n\r\n/* 左侧信息面板 */\r\n.info-panel {\r\n  flex: 0 0 400px;\r\n}\r\n\r\n.welcome-card {\r\n  background: #fff;\r\n  border-radius: 16px;\r\n  padding: 32px;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\r\n  height: fit-content;\r\n  position: sticky;\r\n  top: 110px;\r\n}\r\n\r\n.welcome-icon {\r\n  text-align: center;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.welcome-icon i {\r\n  font-size: 64px;\r\n  color: #3498db;\r\n}\r\n\r\n.welcome-card h2 {\r\n  text-align: center;\r\n  color: #2c3e50;\r\n  margin-bottom: 16px;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n}\r\n\r\n.welcome-desc {\r\n  text-align: center;\r\n  color: #7f8c8d;\r\n  margin-bottom: 32px;\r\n  font-size: 16px;\r\n  line-height: 1.6;\r\n}\r\n\r\n/* 流程步骤 */\r\n.process-steps {\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.step-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n  margin-bottom: 24px;\r\n  padding: 16px;\r\n  border-radius: 12px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.step-item.active {\r\n  background: linear-gradient(135deg, #3498db, #2980b9);\r\n  color: white;\r\n  transform: translateX(8px);\r\n}\r\n\r\n.step-item:not(.active) {\r\n  background: #f8f9fa;\r\n  color: #6c757d;\r\n}\r\n\r\n.step-circle {\r\n  width: 32px;\r\n  height: 32px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-weight: bold;\r\n  font-size: 14px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.step-item.active .step-circle {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  color: white;\r\n}\r\n\r\n.step-item:not(.active) .step-circle {\r\n  background: #dee2e6;\r\n  color: #6c757d;\r\n}\r\n\r\n.step-text h4 {\r\n  margin: 0 0 4px 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.step-text p {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  opacity: 0.8;\r\n}\r\n\r\n/* 安全提示 */\r\n.security-tips {\r\n  background: #f8f9fa;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  border-left: 4px solid #3498db;\r\n}\r\n\r\n.security-tips h4 {\r\n  margin: 0 0 12px 0;\r\n  color: #2c3e50;\r\n  font-size: 16px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.security-tips ul {\r\n  margin: 0;\r\n  padding-left: 20px;\r\n  color: #6c757d;\r\n}\r\n\r\n.security-tips li {\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n  line-height: 1.4;\r\n}\r\n\r\n/* 右侧表单面板 */\r\n.form-panel {\r\n  flex: 1;\r\n  background: #fff;\r\n  border-radius: 16px;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n/* 进度指示器 */\r\n.progress-indicator {\r\n  background: #f8f9fa;\r\n  padding: 24px 32px;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.progress-bar {\r\n  height: 6px;\r\n  background: #e9ecef;\r\n  border-radius: 3px;\r\n  overflow: hidden;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.progress-fill {\r\n  height: 100%;\r\n  background: linear-gradient(90deg, #3498db, #2980b9);\r\n  border-radius: 3px;\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.progress-text {\r\n  text-align: center;\r\n  color: #6c757d;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 表单内容 */\r\n.form-content {\r\n  padding: 32px;\r\n  max-height: calc(100vh - 300px);\r\n  overflow-y: auto;\r\n}\r\n\r\n.form-header {\r\n  text-align: center;\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.form-header h3 {\r\n  color: #2c3e50;\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  margin-bottom: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 12px;\r\n}\r\n\r\n.form-header p {\r\n  color: #7f8c8d;\r\n  margin: 0;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 验证方式网格 */\r\n.verify-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 20px;\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.verify-card {\r\n  position: relative;\r\n  background: #f8f9fa;\r\n  border: 2px solid #e9ecef;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.verify-card:hover {\r\n  border-color: #3498db;\r\n  background: #fff;\r\n  box-shadow: 0 4px 16px rgba(52, 152, 219, 0.15);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.verify-card.selected {\r\n  border-color: #3498db;\r\n  background: linear-gradient(135deg, #3498db, #2980b9);\r\n  color: white;\r\n  box-shadow: 0 8px 24px rgba(52, 152, 219, 0.3);\r\n}\r\n\r\n.verify-icon {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.verify-icon i {\r\n  font-size: 48px;\r\n  color: #3498db;\r\n}\r\n\r\n.verify-card.selected .verify-icon i {\r\n  color: white;\r\n}\r\n\r\n.verify-card h4 {\r\n  margin: 0 0 8px 0;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.verify-card p {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  opacity: 0.8;\r\n}\r\n\r\n.verify-status {\r\n  position: absolute;\r\n  top: 12px;\r\n  right: 12px;\r\n  width: 24px;\r\n  height: 24px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n/* 验证操作区域 */\r\n.verify-operation {\r\n  margin-top: 32px;\r\n  padding: 24px;\r\n  background: #f8f9fa;\r\n  border-radius: 12px;\r\n}\r\n\r\n.operation-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.operation-header h4 {\r\n  margin: 0;\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n/* 身份表单 */\r\n.identity-form {\r\n  background: white;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n}\r\n\r\n/* 身份证读卡器 */\r\n.id-card-reader {\r\n  text-align: center;\r\n}\r\n\r\n.reader-visual {\r\n  background: white;\r\n  padding: 40px;\r\n  border-radius: 12px;\r\n}\r\n\r\n.reader-animation {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.rotating {\r\n  animation: rotate 2s linear infinite;\r\n}\r\n\r\n@keyframes rotate {\r\n  from { transform: rotate(0deg); }\r\n  to { transform: rotate(360deg); }\r\n}\r\n\r\n.reader-animation i {\r\n  font-size: 64px;\r\n  color: #3498db;\r\n}\r\n\r\n.reader-visual h4 {\r\n  color: #2c3e50;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.reader-visual p {\r\n  color: #7f8c8d;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.reader-tips {\r\n  margin: 24px 0;\r\n}\r\n\r\n/* 人脸识别 */\r\n.face-recognition {\r\n  text-align: center;\r\n}\r\n\r\n.camera-container {\r\n  background: white;\r\n  padding: 40px;\r\n  border-radius: 12px;\r\n}\r\n\r\n.camera-frame {\r\n  position: relative;\r\n  width: 280px;\r\n  height: 210px;\r\n  margin: 0 auto 24px;\r\n  border: 3px solid #3498db;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #f8f9fa;\r\n  overflow: hidden;\r\n}\r\n\r\n.camera-overlay {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 16px;\r\n}\r\n\r\n.face-outline {\r\n  width: 120px;\r\n  height: 150px;\r\n  border: 2px dashed #3498db;\r\n  border-radius: 60px;\r\n  opacity: 0.6;\r\n}\r\n\r\n.camera-icon {\r\n  font-size: 32px;\r\n  color: #3498db;\r\n}\r\n\r\n.scanning-line {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 2px;\r\n  background: linear-gradient(90deg, transparent, #3498db, transparent);\r\n  animation: scan 2s ease-in-out infinite;\r\n}\r\n\r\n@keyframes scan {\r\n  0% { transform: translateY(0); }\r\n  50% { transform: translateY(206px); }\r\n  100% { transform: translateY(0); }\r\n}\r\n\r\n/* 表单分组 */\r\n.form-section {\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.section-title {\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin-bottom: 16px;\r\n  padding-bottom: 8px;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n/* 同行人员 */\r\n.no-companions {\r\n  text-align: center;\r\n  padding: 40px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.no-companions i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  color: #bdc3c7;\r\n}\r\n\r\n.companions-table {\r\n  background: #f8f9fa;\r\n  padding: 16px;\r\n  border-radius: 8px;\r\n}\r\n\r\n/* 成功页面 */\r\n.success-content {\r\n  text-align: center;\r\n}\r\n\r\n.success-icon {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.success-icon i {\r\n  font-size: 80px;\r\n  color: #27ae60;\r\n}\r\n\r\n.success-content h4 {\r\n  color: #2c3e50;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.success-message {\r\n  color: #7f8c8d;\r\n  font-size: 16px;\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.register-summary {\r\n  background: #f8f9fa;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n  margin-bottom: 32px;\r\n  text-align: left;\r\n}\r\n\r\n.register-summary h5 {\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin-bottom: 16px;\r\n  text-align: center;\r\n}\r\n\r\n.access-credential {\r\n  background: #f8f9fa;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n  text-align: left;\r\n}\r\n\r\n.access-credential h5 {\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin-bottom: 16px;\r\n  text-align: center;\r\n}\r\n\r\n.credential-card {\r\n  display: flex;\r\n  gap: 24px;\r\n  background: white;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n  border: 2px solid #e9ecef;\r\n}\r\n\r\n.qr-code-container {\r\n  flex: 0 0 120px;\r\n  text-align: center;\r\n}\r\n\r\n.qr-code {\r\n  width: 120px;\r\n  height: 120px;\r\n  border: 2px solid #e9ecef;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #f8f9fa;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.qr-code i {\r\n  font-size: 48px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.qr-code-id {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n  font-family: monospace;\r\n  margin: 0;\r\n}\r\n\r\n.credential-info {\r\n  flex: 1;\r\n}\r\n\r\n.credential-info h6 {\r\n  color: #2c3e50;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.credential-info ul {\r\n  margin: 0;\r\n  padding-left: 20px;\r\n  color: #6c757d;\r\n}\r\n\r\n.credential-info li {\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n  line-height: 1.4;\r\n}\r\n\r\n/* 底部操作按钮 */\r\n.form-actions {\r\n  padding: 24px 32px;\r\n  background: #f8f9fa;\r\n  border-top: 1px solid #e9ecef;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.final-actions {\r\n  display: flex;\r\n  gap: 16px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n  .main-content {\r\n    flex-direction: column;\r\n    gap: 24px;\r\n  }\r\n  \r\n  .info-panel {\r\n    flex: none;\r\n  }\r\n  \r\n  .welcome-card {\r\n    position: static;\r\n  }\r\n  \r\n  .verify-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .main-content {\r\n    padding: 20px;\r\n  }\r\n  \r\n  .nav-content {\r\n    padding: 0 20px;\r\n  }\r\n  \r\n  .company-name {\r\n    font-size: 16px;\r\n  }\r\n  \r\n  .form-content {\r\n    padding: 20px;\r\n  }\r\n  \r\n  .credential-card {\r\n    flex-direction: column;\r\n    text-align: center;\r\n  }\r\n  \r\n  .form-actions {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n}\r\n\r\n/* Element UI 样式覆盖 */\r\n.el-form-item {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.el-input__inner {\r\n  height: 44px;\r\n  border-radius: 8px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.el-input__inner:focus {\r\n  border-color: #3498db;\r\n  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.1);\r\n}\r\n\r\n.el-select {\r\n  width: 100%;\r\n}\r\n\r\n.el-date-editor {\r\n  width: 100%;\r\n}\r\n\r\n.el-button--large {\r\n  height: 44px;\r\n  padding: 12px 24px;\r\n  border-radius: 8px;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n}\r\n\r\n.el-button--primary {\r\n  background: linear-gradient(135deg, #3498db, #2980b9);\r\n  border: none;\r\n}\r\n\r\n.el-button--primary:hover {\r\n  background: linear-gradient(135deg, #2980b9, #1f4e79);\r\n}\r\n\r\n.el-descriptions {\r\n  border-radius: 8px;\r\n}\r\n\r\n.el-table {\r\n  border-radius: 8px;\r\n}\r\n\r\n.el-alert {\r\n  border-radius: 8px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAydA,IAAAA,QAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MACAC,SAAA;MACAC,UAAA;MAEA;MACAC,YAAA;MACAC,eAAA;MACAC,gBAAA;MACAC,mBAAA;MAEA;MACAC,YAAA;QACAT,IAAA;QACAU,KAAA;QACAC,MAAA;QACAC,QAAA;QACAC,OAAA;MACA;MACAC,aAAA;QACAd,IAAA,GACA;UAAAe,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAG,OAAA;UAAAJ,OAAA;UAAAC,OAAA;QAAA,EACA;QACAP,KAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAG,OAAA;UAAAJ,OAAA;UAAAC,OAAA;QAAA,EACA;QACAN,MAAA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAL,QAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAJ,OAAA;UAAAM,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA;MACA;MAEA;MACAI,SAAA;QACAC,cAAA;QACAC,gBAAA;QACAC,iBAAA;QACAC,kBAAA;QACAC,oBAAA;QACAC,mBAAA;MACA;MACAC,UAAA;QACAN,cAAA,GACA;UAAAP,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAM,gBAAA,GACA;UAAAR,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAO,iBAAA,GACA;UAAAT,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAS,oBAAA;UAAAX,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAU,mBAAA;UAAAZ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;MAEA;MACAY,aAAA;MAEA;MACAC,cAAA;MAEA;MACAC,kBAAA;QACAC,SAAA;UACAC,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACAA,MAAA,CAAAC,KAAA,aAAAC,IAAA;UACA;QACA;UACAJ,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAG,IAAA,OAAAD,IAAA;YACAC,IAAA,CAAAC,OAAA,CAAAD,IAAA,CAAAE,OAAA;YACAL,MAAA,CAAAC,KAAA,SAAAE,IAAA;UACA;QACA;UACAL,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAG,IAAA,OAAAD,IAAA;YACAC,IAAA,CAAAG,OAAA,CAAAH,IAAA,CAAAI,OAAA;YACAJ,IAAA,CAAAK,QAAA;YACAR,MAAA,CAAAC,KAAA,SAAAE,IAAA;UACA;QACA;QACAM,YAAA,WAAAA,aAAAC,IAAA;UACA;UACA,IAAAC,aAAA,GAAAT,IAAA,CAAAU,GAAA;UACA,IAAAC,eAAA,GAAAX,IAAA,CAAAU,GAAA;UACA,OAAAF,IAAA,CAAAL,OAAA,KAAAM,aAAA,IAAAD,IAAA,CAAAL,OAAA,KAAAQ,eAAA;QACA;MACA;MAEAC,iBAAA;QACAjB,SAAA;UACAC,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAG,IAAA,OAAAD,IAAA;YACAC,IAAA,CAAAC,OAAA,CAAAD,IAAA,CAAAE,OAAA;YACAL,MAAA,CAAAC,KAAA,SAAAE,IAAA;UACA;QACA;UACAL,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAG,IAAA,OAAAD,IAAA;YACAC,IAAA,CAAAK,QAAA;YACA,IAAAL,IAAA,CAAAE,OAAA,KAAAH,IAAA,CAAAU,GAAA;cACAT,IAAA,CAAAG,OAAA,CAAAH,IAAA,CAAAI,OAAA;YACA;YACAP,MAAA,CAAAC,KAAA,SAAAE,IAAA;UACA;QACA;UACAL,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAG,IAAA,OAAAD,IAAA;YACAC,IAAA,CAAAG,OAAA,CAAAH,IAAA,CAAAI,OAAA;YACAJ,IAAA,CAAAK,QAAA;YACAR,MAAA,CAAAC,KAAA,SAAAE,IAAA;UACA;QACA;QACAM,YAAA,WAAAA,aAAAC,IAAA;UACA;UACA,IAAAG,eAAA,GAAAX,IAAA,CAAAU,GAAA;UACA,OAAAF,IAAA,CAAAL,OAAA,KAAAH,IAAA,CAAAU,GAAA,eAAAF,IAAA,CAAAL,OAAA,KAAAQ,eAAA;QACA;MACA;IACA;EACA;EAEAE,QAAA;IACAC,aAAA,WAAAA,cAAA;MACA,aAAAjD,WAAA;IACA;IAEAkD,mBAAA,WAAAA,oBAAA;MACA,YAAA9C,eAAA,SAAAC,gBAAA,SAAAC,mBAAA;IACA;IAEA;IACA6C,aAAA,WAAAA,cAAA;MACA,gBAAAxB,aAAA,CAAAyB,MAAA;IACA;EACA;EAEAC,OAAA;IACA,aACAC,kBAAA,WAAAA,mBAAAC,MAAA;MACA,KAAApD,YAAA,GAAAoD,MAAA;MACA,KAAAC,kBAAA;MAEA,QAAAD,MAAA;QACA;UACA,KAAAnD,eAAA;UACA;QACA;UACA,KAAAC,gBAAA;UACA;QACA;UACA,KAAAC,mBAAA;UACA;QACA;UACA,KAAAF,eAAA;UACA,KAAAG,YAAA,CAAAE,MAAA;UACA;MACA;IACA;IAEA,aACA+C,kBAAA,WAAAA,mBAAA;MACA,KAAApD,eAAA;MACA,KAAAC,gBAAA;MACA,KAAAC,mBAAA;IACA;IAEA,eACAmD,kBAAA,WAAAA,mBAAA;MACA,KAAAD,kBAAA;MACA,KAAArD,YAAA;IACA;IAEA,gBACAuD,gBAAA,WAAAA,iBAAA;MACA,SAAAvD,YAAA;QACA,KAAAC,eAAA;MACA,gBAAAD,YAAA;QACA,KAAAE,gBAAA;MACA,gBAAAF,YAAA;QACA,KAAAG,mBAAA;MACA,gBAAAH,YAAA;QACA,KAAAC,eAAA;QACA,KAAAG,YAAA,CAAAE,MAAA;MACA;IACA;IAEA,WACAkD,eAAA,WAAAA,gBAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,KAAA,CAAAtD,YAAA,CAAAuD,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,KAAA,CAAA3D,SAAA;UACA;UACA+D,UAAA;YACAJ,KAAA,CAAA3D,SAAA;YACA2D,KAAA,CAAAK,QAAA,CAAAC,OAAA;YACAN,KAAA,CAAAO,QAAA;UACA;QACA;MACA;IACA;IAEA,cACAC,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,MAAA;MACA,KAAA9D,YAAA;QACAT,IAAA;QACAU,KAAA;QACAC,MAAA;QACAC,QAAA;QACAC,OAAA;MACA;MACA,KAAAsD,QAAA,CAAAC,OAAA;MACAF,UAAA;QACAK,MAAA,CAAAF,QAAA;MACA;IACA;IAEA,aACAG,uBAAA,WAAAA,wBAAA;MAAA,IAAAC,MAAA;MACA,KAAAhE,YAAA;QACAT,IAAA;QACAU,KAAA;QACAC,MAAA;QACAC,QAAA;QACAC,OAAA;MACA;MACA,KAAAsD,QAAA,CAAAC,OAAA;MACAF,UAAA;QACAO,MAAA,CAAAJ,QAAA;MACA;IACA;IAEA,UACAA,QAAA,WAAAA,SAAA;MACA,SAAAnE,WAAA;QACA,KAAAwE,kBAAA;MACA;QACA,KAAAxE,WAAA;MACA;IACA;IAEA,UACAyE,QAAA,WAAAA,SAAA;MACA,KAAAzE,WAAA;MACA,SAAAA,WAAA;QACA,KAAAwD,kBAAA;MACA;IACA;IAEA,aACAkB,YAAA,WAAAA,aAAA;MACA,SAAA/C,aAAA,CAAAyB,MAAA;QACA,KAAAa,QAAA,CAAAU,OAAA;QACA;MACA;MAEA,KAAAhD,aAAA,CAAAiD,IAAA;QACA9E,IAAA;QACAU,KAAA;QACAE,QAAA;QACAC,OAAA;MACA;MAEA,KAAAsD,QAAA,CAAAC,OAAA;IACA;IAEA,eACAW,mBAAA,WAAAA,oBAAAC,KAAA;MACAC,OAAA,CAAAC,GAAA,cAAAF,KAAA;MACA;MACA,IAAAA,KAAA,UAAA3D,SAAA,CAAAM,mBAAA;QACA,IAAAwD,WAAA,OAAA9C,IAAA,CAAA2C,KAAA;QACA,IAAAI,aAAA,OAAA/C,IAAA,CAAA8C,WAAA,CAAA3C,OAAA;QAEA,IAAA6C,IAAA,GAAAD,aAAA,CAAAE,WAAA;QACA,IAAAC,KAAA,GAAAC,MAAA,CAAAJ,aAAA,CAAAK,QAAA,QAAAC,QAAA;QACA,IAAAC,GAAA,GAAAH,MAAA,CAAAJ,aAAA,CAAA1C,OAAA,IAAAgD,QAAA;QACA,IAAAE,KAAA,GAAAJ,MAAA,CAAAJ,aAAA,CAAAS,QAAA,IAAAH,QAAA;QACA,IAAAI,OAAA,GAAAN,MAAA,CAAAJ,aAAA,CAAAW,UAAA,IAAAL,QAAA;QACA,IAAAM,OAAA,GAAAR,MAAA,CAAAJ,aAAA,CAAAa,UAAA,IAAAP,QAAA;QAEA,KAAArE,SAAA,CAAAM,mBAAA,MAAAuE,MAAA,CAAAb,IAAA,OAAAa,MAAA,CAAAX,KAAA,OAAAW,MAAA,CAAAP,GAAA,OAAAO,MAAA,CAAAN,KAAA,OAAAM,MAAA,CAAAJ,OAAA,OAAAI,MAAA,CAAAF,OAAA;MACA;IACA;IAEA,eACAG,qBAAA,WAAAA,sBAAAnB,KAAA;MACAC,OAAA,CAAAC,GAAA,cAAAF,KAAA;MACA;MACA,IAAAA,KAAA,SAAA3D,SAAA,CAAAK,oBAAA;QACA,IAAAyD,WAAA,OAAA9C,IAAA,MAAAhB,SAAA,CAAAK,oBAAA;QACA,IAAA0D,aAAA,OAAA/C,IAAA,CAAA2C,KAAA;QAEA,IAAAI,aAAA,IAAAD,WAAA;UACA,KAAAhB,QAAA,CAAAU,OAAA;UACA,KAAAxD,SAAA,CAAAM,mBAAA;QACA;MACA;IACA;IAEA,WACA+C,kBAAA,WAAAA,mBAAA;MAAA,IAAA0B,MAAA;MACA,KAAArC,KAAA,CAAA1C,SAAA,CAAA2C,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA,KAAAmC,MAAA,CAAAC,gBAAA;YACA;UACA;;UAEA;UACA,KAAAD,MAAA,CAAAE,aAAA;YACA;UACA;UAEAF,MAAA,CAAAhG,UAAA;;UAEA;UACA,IAAAmG,UAAA;YACA;YACAC,YAAA;cACAC,kBAAA,EAAAL,MAAA,CAAA3F,YAAA,CAAAT,IAAA,CAAA0G,IAAA;cACAC,mBAAA,EAAAP,MAAA,CAAA3F,YAAA,CAAAC,KAAA,CAAAgG,IAAA;cACApF,cAAA,EAAA8E,MAAA,CAAA/E,SAAA,CAAAC,cAAA,CAAAoF,IAAA;cACAnF,gBAAA,EAAA6E,MAAA,CAAA/E,SAAA,CAAAE,gBAAA,CAAAmF,IAAA;cACAE,cAAA;cAAA;cACApF,iBAAA,EAAA4E,MAAA,CAAA/E,SAAA,CAAAG,iBAAA,CAAAkF,IAAA;cACAhF,oBAAA,EAAA0E,MAAA,CAAAS,oBAAA,CAAAT,MAAA,CAAA/E,SAAA,CAAAK,oBAAA;cACAC,mBAAA,EAAAyE,MAAA,CAAAS,oBAAA,CAAAT,MAAA,CAAA/E,SAAA,CAAAM,mBAAA;cACAF,kBAAA,EAAA2E,MAAA,CAAA/E,SAAA,CAAAI,kBAAA,GAAA2E,MAAA,CAAA/E,SAAA,CAAAI,kBAAA,CAAAiF,IAAA;cACAI,eAAA,EAAAV,MAAA,CAAAvE,aAAA,CAAAyB;YACA;YACA;YACAyD,aAAA;YACA;YACA;cACAC,WAAA,EAAAZ,MAAA,CAAA3F,YAAA,CAAAT,IAAA,CAAA0G,IAAA;cACAO,YAAA,EAAAb,MAAA,CAAA3F,YAAA,CAAAC,KAAA,CAAAgG,IAAA;cACAQ,aAAA,EAAAd,MAAA,CAAA3F,YAAA,CAAAG,QAAA,CAAA8F,IAAA,GAAAS,WAAA;cACAC,cAAA,EAAAhB,MAAA,CAAA3F,YAAA,CAAAI,OAAA,GAAAuF,MAAA,CAAA3F,YAAA,CAAAI,OAAA,CAAA6F,IAAA;cACAW,SAAA;cAAA;cACAC,kBAAA;YACA,GAAApB,MAAA,KAAAqB,mBAAA,CAAAC,OAAA,EAEApB,MAAA,CAAAvE,aAAA,CACA4F,MAAA,WAAAC,IAAA;cAAA,OAAAA,IAAA,CAAA1H,IAAA,IAAA0H,IAAA,CAAAhH,KAAA,IAAAgH,IAAA,CAAA9G,QAAA;YAAA,GACA+G,GAAA,WAAAD,IAAA;cAAA;gBACAV,WAAA,EAAAU,IAAA,CAAA1H,IAAA,CAAA0G,IAAA;gBACAO,YAAA,EAAAS,IAAA,CAAAhH,KAAA,CAAAgG,IAAA;gBACAQ,aAAA,EAAAQ,IAAA,CAAA9G,QAAA,CAAA8F,IAAA,GAAAS,WAAA;gBACAC,cAAA,EAAAM,IAAA,CAAA7G,OAAA,GAAA6G,IAAA,CAAA7G,OAAA,CAAA6F,IAAA;gBACAW,SAAA;gBACAC,kBAAA;cACA;YAAA;UAEA;UAEArC,OAAA,CAAAC,GAAA,YAAA0C,IAAA,CAAAC,SAAA,CAAAtB,UAAA;UAEA,IAAAuB,kCAAA,EAAAvB,UAAA,EACAwB,IAAA,WAAAC,QAAA;YACA5B,MAAA,CAAAtE,cAAA,GAAAkG,QAAA,CAAA/H,IAAA,WAAAoC,IAAA,CAAAU,GAAA;YACAqD,MAAA,CAAAjC,QAAA,CAAAC,OAAA,IAAA8B,MAAA,CAAAE,MAAA,CAAA/C,aAAA;YACA+C,MAAA,CAAAlG,WAAA;UACA,GACA+H,KAAA,WAAAC,KAAA;YACA9B,MAAA,CAAAjC,QAAA,CAAA+D,KAAA,CAAAA,KAAA,CAAAC,GAAA;UACA,GACAC,OAAA;YACAhC,MAAA,CAAAhG,UAAA;UACA;QACA;MACA;IACA;IAEA,aACAiG,gBAAA,WAAAA,iBAAA;MACA;MACA,UAAA5F,YAAA,CAAAT,IAAA,SAAAS,YAAA,CAAAT,IAAA,CAAA0G,IAAA,GAAApD,MAAA;QACA,KAAAa,QAAA,CAAA+D,KAAA;QACA;MACA;MAEA,UAAAzH,YAAA,CAAAC,KAAA,qBAAA2H,IAAA,MAAA5H,YAAA,CAAAC,KAAA,CAAAgG,IAAA;QACA,KAAAvC,QAAA,CAAA+D,KAAA;QACA;MACA;MAEA,UAAAzH,YAAA,CAAAG,QAAA,SAAAH,YAAA,CAAAG,QAAA,CAAA8F,IAAA,GAAApD,MAAA;QACA,KAAAa,QAAA,CAAA+D,KAAA;QACA;MACA;;MAEA;MACA,SAAAI,CAAA,MAAAA,CAAA,QAAAzG,aAAA,CAAAyB,MAAA,EAAAgF,CAAA;QACA,IAAAC,OAAA,QAAA1G,aAAA,CAAAyG,CAAA;QACA,IAAAE,YAAA,kBAAAtC,MAAA,CAAAoC,CAAA;;QAEA;QACA,KAAAC,OAAA,CAAAvI,IAAA,IAAAuI,OAAA,CAAAvI,IAAA,CAAA0G,IAAA;UACA,KAAAvC,QAAA,CAAA+D,KAAA,sBAAAhC,MAAA,CAAAsC,YAAA;UACA;QACA;QAEA,IAAAD,OAAA,CAAAvI,IAAA,CAAA0G,IAAA,GAAApD,MAAA,QAAAiF,OAAA,CAAAvI,IAAA,CAAA0G,IAAA,GAAApD,MAAA;UACA,KAAAa,QAAA,CAAA+D,KAAA,IAAAhC,MAAA,CAAAsC,YAAA;UACA;QACA;;QAEA;QACA,IAAAC,WAAA;QACA,KAAAA,WAAA,CAAAJ,IAAA,CAAAE,OAAA,CAAAvI,IAAA,CAAA0G,IAAA;UACA,KAAAvC,QAAA,CAAA+D,KAAA,IAAAhC,MAAA,CAAAsC,YAAA;UACA;QACA;;QAEA;QACA,KAAAD,OAAA,CAAA7H,KAAA,IAAA6H,OAAA,CAAA7H,KAAA,CAAAgG,IAAA;UACA,KAAAvC,QAAA,CAAA+D,KAAA,sBAAAhC,MAAA,CAAAsC,YAAA;UACA;QACA;QAEA,IAAAE,YAAA;QACA,KAAAA,YAAA,CAAAL,IAAA,CAAAE,OAAA,CAAA7H,KAAA,CAAAgG,IAAA;UACA,KAAAvC,QAAA,CAAA+D,KAAA,IAAAhC,MAAA,CAAAsC,YAAA;UACA;QACA;;QAEA;QACA,KAAAD,OAAA,CAAA3H,QAAA,IAAA2H,OAAA,CAAA3H,QAAA,CAAA8F,IAAA;UACA,KAAAvC,QAAA,CAAA+D,KAAA,sBAAAhC,MAAA,CAAAsC,YAAA;UACA;QACA;QAEA,IAAAG,MAAA,GAAAJ,OAAA,CAAA3H,QAAA,CAAA8F,IAAA,GAAAS,WAAA;;QAEA;QACA,IAAAwB,MAAA,CAAArF,MAAA;UACA,IAAAsF,SAAA;UACA,KAAAA,SAAA,CAAAP,IAAA,CAAAM,MAAA;YACA,KAAAxE,QAAA,CAAA+D,KAAA,IAAAhC,MAAA,CAAAsC,YAAA;YACA;UACA;QACA;;QAEA;QACA,IAAAD,OAAA,CAAA3H,QAAA,CAAA8F,IAAA,GAAAS,WAAA,YAAA1G,YAAA,CAAAG,QAAA,CAAA8F,IAAA,GAAAS,WAAA;UACA,KAAAhD,QAAA,CAAA+D,KAAA,IAAAhC,MAAA,CAAAsC,YAAA;UACA;QACA;;QAEA;QACA,SAAAK,CAAA,MAAAA,CAAA,QAAAhH,aAAA,CAAAyB,MAAA,EAAAuF,CAAA;UACA,IAAAP,CAAA,KAAAO,CAAA,IAAAN,OAAA,CAAA3H,QAAA,CAAA8F,IAAA,GAAAS,WAAA,YAAAtF,aAAA,CAAAgH,CAAA,EAAAjI,QAAA,CAAA8F,IAAA,GAAAS,WAAA;YACA,KAAAhD,QAAA,CAAA+D,KAAA,IAAAhC,MAAA,CAAAsC,YAAA,wBAAAtC,MAAA,CAAA2C,CAAA;YACA;UACA;QACA;;QAEA;QACA,IAAAN,OAAA,CAAA7H,KAAA,CAAAgG,IAAA,YAAAjG,YAAA,CAAAC,KAAA,CAAAgG,IAAA;UACA,KAAAvC,QAAA,CAAA+D,KAAA,IAAAhC,MAAA,CAAAsC,YAAA;UACA;QACA;;QAEA;QACA,SAAAK,EAAA,MAAAA,EAAA,QAAAhH,aAAA,CAAAyB,MAAA,EAAAuF,EAAA;UACA,IAAAP,CAAA,KAAAO,EAAA,IAAAN,OAAA,CAAA7H,KAAA,CAAAgG,IAAA,YAAA7E,aAAA,CAAAgH,EAAA,EAAAnI,KAAA,CAAAgG,IAAA;YACA,KAAAvC,QAAA,CAAA+D,KAAA,IAAAhC,MAAA,CAAAsC,YAAA,wBAAAtC,MAAA,CAAA2C,EAAA;YACA;UACA;QACA;;QAEA;QACAN,OAAA,CAAAvI,IAAA,GAAAuI,OAAA,CAAAvI,IAAA,CAAA0G,IAAA;QACA6B,OAAA,CAAA7H,KAAA,GAAA6H,OAAA,CAAA7H,KAAA,CAAAgG,IAAA;QACA6B,OAAA,CAAA3H,QAAA,GAAA2H,OAAA,CAAA3H,QAAA,CAAA8F,IAAA,GAAAS,WAAA;QACAoB,OAAA,CAAA1H,OAAA,IAAA0H,OAAA,CAAA1H,OAAA,QAAA6F,IAAA;MACA;MAEA;IACA;IAEA,WACAJ,aAAA,WAAAA,cAAA;MACA,UAAAjF,SAAA,CAAAK,oBAAA;QACA,KAAAyC,QAAA,CAAA+D,KAAA;QACA;MACA;MAEA,UAAA7G,SAAA,CAAAM,mBAAA;QACA,KAAAwC,QAAA,CAAA+D,KAAA;QACA;MACA;MAEA,IAAA/C,WAAA,OAAA9C,IAAA,MAAAhB,SAAA,CAAAK,oBAAA;MACA,IAAA0D,aAAA,OAAA/C,IAAA,MAAAhB,SAAA,CAAAM,mBAAA;MACA,IAAAoB,GAAA,OAAAV,IAAA;;MAEA;MACA,IAAAS,aAAA,OAAAT,IAAA,CAAAU,GAAA,CAAAP,OAAA;MACA,IAAA2C,WAAA,GAAArC,aAAA;QACA,KAAAqB,QAAA,CAAA+D,KAAA;QACA;MACA;;MAEA;MACA,IAAA9C,aAAA,IAAAD,WAAA;QACA,KAAAhB,QAAA,CAAA+D,KAAA;QACA;MACA;MAEA;IACA;IAEA,oBACArB,oBAAA,WAAAA,qBAAAiC,OAAA;MACA,KAAAA,OAAA;MAEA,IAAAxG,IAAA,OAAAD,IAAA,CAAAyG,OAAA;MACA,IAAAC,KAAA,CAAAzG,IAAA,CAAAE,OAAA;MAEA,IAAA6C,IAAA,GAAA/C,IAAA,CAAAgD,WAAA;MACA,IAAAC,KAAA,GAAAC,MAAA,CAAAlD,IAAA,CAAAmD,QAAA,QAAAC,QAAA;MACA,IAAAC,GAAA,GAAAH,MAAA,CAAAlD,IAAA,CAAAI,OAAA,IAAAgD,QAAA;MACA,IAAAE,KAAA,GAAAJ,MAAA,CAAAlD,IAAA,CAAAuD,QAAA,IAAAH,QAAA;MACA,IAAAI,OAAA,GAAAN,MAAA,CAAAlD,IAAA,CAAAyD,UAAA,IAAAL,QAAA;MACA,IAAAM,OAAA,GAAAR,MAAA,CAAAlD,IAAA,CAAA2D,UAAA,IAAAP,QAAA;MAEA,UAAAQ,MAAA,CAAAb,IAAA,OAAAa,MAAA,CAAAX,KAAA,OAAAW,MAAA,CAAAP,GAAA,OAAAO,MAAA,CAAAN,KAAA,OAAAM,MAAA,CAAAJ,OAAA,OAAAI,MAAA,CAAAF,OAAA;IACA;IAEA,WACAgD,aAAA,WAAAA,cAAA;MACA,KAAA9I,WAAA;MACA,KAAAwD,kBAAA;MACA,KAAArD,YAAA;MACA,KAAAI,YAAA;QACAT,IAAA;QACAU,KAAA;QACAC,MAAA;QACAC,QAAA;QACAC,OAAA;MACA;MACA,KAAAQ,SAAA;QACAC,cAAA;QACAC,gBAAA;QACAC,iBAAA;QACAC,kBAAA;QACAC,oBAAA;QACAC,mBAAA;MACA;MACA,KAAAE,aAAA;MACA,KAAAC,cAAA;IACA;IAEA,WACAmH,eAAA,WAAAA,gBAAA;MACA,KAAA9E,QAAA,CAAA+E,IAAA;MACA;IACA;IAEA,WACAC,QAAA,WAAAA,SAAA;MACA,KAAAC,MAAA;QACAC,iBAAA;MACA;IACA;IAEA,WACAC,cAAA,WAAAA,eAAA;MACA,KAAAF,MAAA;QACAC,iBAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}