{"remainingRequest": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\views\\asc\\visitor\\self-register.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\views\\asc\\visitor\\self-register.vue", "mtime": 1750986040997}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\babel.config.js", "mtime": 1750833543308}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750836929039}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750836940534}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750836929039}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750836957095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_visitor", "require", "name", "data", "submitting", "registrationCompleted", "formData", "visitors", "phone", "idCard", "company", "isMainContact", "visitInfo", "reasonForVisit", "hostEmployeeName", "departmentVisited", "vehiclePlateNumber", "plannedEntryDatetime", "plannedExitDatetime", "visitRules", "required", "message", "trigger", "min", "max", "pattern", "registrationId", "entryPickerOptions", "shortcuts", "text", "onClick", "picker", "$emit", "Date", "date", "setTime", "getTime", "setDate", "getDate", "setHours", "disabledDate", "time", "oneHourBefore", "now", "thirtyDaysLater", "exitPickerOptions", "computed", "totalVisitors", "length", "mainContact", "methods", "addVisitor", "$message", "warning", "push", "success", "removeVisitor", "index", "splice", "formatDateForBackend", "dateStr", "isNaN", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "concat", "onArrivalTimeChange", "value", "console", "log", "arrivalTime", "departureTime", "onDepartureTimeChange", "submitForm", "_this", "_asyncToGenerator2", "default", "_regenerator2", "m", "_callee", "submitData", "response", "_t", "w", "_context", "n", "p", "$refs", "visitForm", "validate", "validateVisitors", "a", "validateTimes", "registration", "primaryContactName", "trim", "primaryContactPhone", "hostEmployeeId", "totalCompanions", "attendeesList", "map", "visitor", "visitorName", "visitorPhone", "visitorIdCard", "toUpperCase", "visitorCompany", "isPrimary", "visitorAvatarPhoto", "JSON", "stringify", "submitVisitorRegistration", "v", "code", "error", "msg", "f", "i", "visitorTitle", "phonePattern", "test", "idPattern", "j", "entryTime", "exitTime", "visitDuration", "maxDuration", "resetForm", "_this2", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "printCredential", "info", "parseTime"], "sources": ["src/views/asc/visitor/self-register.vue"], "sourcesContent": ["<template>\r\n  <div class=\"pc-register-container\">\r\n    <!-- 顶部导航栏 -->\r\n    <div class=\"top-nav\">\r\n      <div class=\"nav-content\">\r\n        <div class=\"logo-section\">\r\n          <img src=\"@/assets/logo/logo.png\" alt=\"公司logo\" class=\"nav-logo\" />\r\n          <span class=\"company-name\">智慧园区访客登记系统</span>\r\n        </div>\r\n        <div class=\"nav-actions\">\r\n          <el-button type=\"text\" @click=\"showHelp\">使用帮助</el-button>\r\n          <el-button type=\"text\" @click=\"contactService\">联系客服</el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 主要内容区域 -->\r\n    <div class=\"main-content\">\r\n      <!-- 左侧信息面板 -->\r\n      <div class=\"info-panel\">\r\n        <div class=\"welcome-card\">\r\n          <div class=\"welcome-icon\">\r\n            <i class=\"el-icon-office-building\"></i>\r\n          </div>\r\n          <h2>欢迎访问智慧园区</h2>\r\n          <p class=\"welcome-desc\">为了您的安全和便利，请填写以下信息完成访客登记</p>\r\n\r\n          <div class=\"process-steps\">\r\n            <div class=\"step-item\" :class=\"{ active: !registrationCompleted }\">\r\n              <div class=\"step-circle\">1</div>\r\n              <div class=\"step-text\">\r\n                <h4>填写信息</h4>\r\n                <p>完善访问详细信息</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"step-item\" :class=\"{ active: registrationCompleted }\">\r\n              <div class=\"step-circle\">2</div>\r\n              <div class=\"step-text\">\r\n                <h4>获取凭证</h4>\r\n                <p>生成访问凭证二维码</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"security-tips\">\r\n            <h4><i class=\"el-icon-lock\"></i> 安全提示</h4>\r\n            <ul>\r\n              <li>请确保提供真实有效的身份信息</li>\r\n              <li>您的个人信息将被严格保密</li>\r\n              <li>访问凭证仅限当次使用</li>\r\n            </ul>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 右侧表单区域 -->\r\n      <div class=\"form-panel\">\r\n        <!-- 表单标题 -->\r\n        <div class=\"form-header\">\r\n          <h3 v-if=\"!registrationCompleted\"><i class=\"el-icon-edit\"></i> 访客登记信息</h3>\r\n          <h3 v-else><i class=\"el-icon-circle-check\"></i> 登记完成</h3>\r\n          <p v-if=\"!registrationCompleted\">请填写详细的访问信息</p>\r\n          <p v-else>您的访客登记已成功提交</p>\r\n        </div>\r\n\r\n        <!-- 表单内容 -->\r\n        <div class=\"form-content\" v-if=\"!registrationCompleted\">\r\n          <el-form ref=\"visitForm\" :model=\"formData\" :rules=\"visitRules\"\r\n                   label-width=\"120px\" class=\"visit-form\">\r\n\r\n            <!-- 基本访问信息 -->\r\n            <div class=\"form-section\">\r\n              <h4 class=\"section-title\">基本访问信息</h4>\r\n              <el-row :gutter=\"20\">\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"来访事由\" prop=\"visitInfo.reasonForVisit\">\r\n                    <el-input v-model=\"formData.visitInfo.reasonForVisit\" placeholder=\"请输入来访事由\"\r\n                              prefix-icon=\"el-icon-tickets\" type=\"textarea\" :rows=\"3\" maxlength=\"200\" show-word-limit />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"被访人姓名\" prop=\"visitInfo.hostEmployeeName\">\r\n                    <el-input v-model=\"formData.visitInfo.hostEmployeeName\" placeholder=\"请输入被访人姓名\"\r\n                              prefix-icon=\"el-icon-user\" maxlength=\"20\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n\r\n              <el-row :gutter=\"20\">\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"被访部门\" prop=\"visitInfo.departmentVisited\">\r\n                    <el-input v-model=\"formData.visitInfo.departmentVisited\" placeholder=\"请输入被访部门\"\r\n                              prefix-icon=\"el-icon-office-building\" maxlength=\"50\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"车牌号\">\r\n                    <el-input v-model=\"formData.visitInfo.vehiclePlateNumber\" placeholder=\"如有车辆请填写车牌号，多个用逗号分隔\"\r\n                              prefix-icon=\"el-icon-truck\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </div>\r\n\r\n            <!-- 时间信息 -->\r\n            <div class=\"form-section\">\r\n              <h4 class=\"section-title\">访问时间</h4>\r\n              <el-row :gutter=\"20\">\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"预计到访时间\" prop=\"visitInfo.plannedEntryDatetime\">\r\n                    <el-date-picker\r\n                      v-model=\"formData.visitInfo.plannedEntryDatetime\"\r\n                      type=\"datetime\"\r\n                      placeholder=\"选择到访时间\"\r\n                      style=\"width: 100%\"\r\n                      value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                      :picker-options=\"entryPickerOptions\"\r\n                      @change=\"onArrivalTimeChange\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"预计离开时间\" prop=\"visitInfo.plannedExitDatetime\">\r\n                    <el-date-picker\r\n                      v-model=\"formData.visitInfo.plannedExitDatetime\"\r\n                      type=\"datetime\"\r\n                      placeholder=\"选择离开时间\"\r\n                      style=\"width: 100%\"\r\n                      value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                      :picker-options=\"exitPickerOptions\"\r\n                      @change=\"onDepartureTimeChange\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </div>\r\n\r\n            <!-- 访客信息 -->\r\n            <div class=\"form-section\">\r\n              <div class=\"section-header\">\r\n                <h4 class=\"section-title\">访客信息（第一位为主联系人）共 {{ totalVisitors }} 人</h4>\r\n                <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"addVisitor\" :disabled=\"formData.visitors.length >= 10\">\r\n                  添加访客\r\n                </el-button>\r\n              </div>\r\n\r\n              <div class=\"visitors-list\">\r\n                <div v-for=\"(visitor, index) in formData.visitors\" :key=\"index\" class=\"visitor-item\">\r\n                  <div class=\"visitor-header\">\r\n                    <h5 class=\"visitor-title\">\r\n                      {{ index === 0 ? '主联系人' : `访客 ${index + 1}` }}\r\n                    </h5>\r\n                    <el-button v-if=\"index > 0\" size=\"mini\" type=\"danger\" icon=\"el-icon-delete\"\r\n                               @click=\"removeVisitor(index)\" circle />\r\n                  </div>\r\n\r\n                  <el-row :gutter=\"20\">\r\n                    <el-col :span=\"12\">\r\n                      <el-form-item :label=\"`姓名`\" :prop=\"`visitors.${index}.name`\">\r\n                        <el-input v-model=\"visitor.name\" placeholder=\"请输入姓名\" maxlength=\"20\" />\r\n                      </el-form-item>\r\n                    </el-col>\r\n                    <el-col :span=\"12\">\r\n                      <el-form-item :label=\"`手机号`\" :prop=\"`visitors.${index}.phone`\">\r\n                        <el-input v-model=\"visitor.phone\" placeholder=\"请输入手机号\" />\r\n                      </el-form-item>\r\n                    </el-col>\r\n                  </el-row>\r\n\r\n                  <el-row :gutter=\"20\">\r\n                    <el-col :span=\"12\">\r\n                      <el-form-item :label=\"`身份证号`\" :prop=\"`visitors.${index}.idCard`\">\r\n                        <el-input v-model=\"visitor.idCard\" placeholder=\"请输入身份证号\" />\r\n                      </el-form-item>\r\n                    </el-col>\r\n                    <el-col :span=\"12\">\r\n                      <el-form-item :label=\"`公司名称`\">\r\n                        <el-input v-model=\"visitor.company\" placeholder=\"请输入公司名称（可选）\" maxlength=\"100\" />\r\n                      </el-form-item>\r\n                    </el-col>\r\n                  </el-row>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-form>\r\n        </div>\r\n\r\n        <!-- 登记成功页面 -->\r\n        <div class=\"success-content\" v-if=\"registrationCompleted\">\r\n          <div class=\"success-icon\">\r\n            <i class=\"el-icon-circle-check\"></i>\r\n          </div>\r\n\r\n          <h4>登记成功！</h4>\r\n          <p class=\"success-message\">您的访客登记已提交，请等待审核通过</p>\r\n\r\n          <!-- 登记信息摘要 -->\r\n          <div class=\"register-summary\">\r\n            <h5>登记信息摘要</h5>\r\n            <el-descriptions :column=\"2\" border>\r\n              <el-descriptions-item label=\"主联系人\">\r\n                {{ mainContact.name }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"联系电话\">\r\n                {{ mainContact.phone }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"被访人\">\r\n                {{ formData.visitInfo.hostEmployeeName }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"被访部门\">\r\n                {{ formData.visitInfo.departmentVisited }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"来访事由\" span=\"2\">\r\n                {{ formData.visitInfo.reasonForVisit }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"预计到访时间\">\r\n                {{ parseTime(formData.visitInfo.plannedEntryDatetime) }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"预计离开时间\">\r\n                {{ parseTime(formData.visitInfo.plannedExitDatetime) }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"访客总数\">\r\n                {{ totalVisitors }} 人\r\n              </el-descriptions-item>\r\n            </el-descriptions>\r\n          </div>\r\n\r\n          <!-- 访问凭证 -->\r\n          <div class=\"access-credential\" v-if=\"registrationId\">\r\n            <h5>访问凭证</h5>\r\n            <div class=\"credential-card\">\r\n              <div class=\"qr-code-container\">\r\n                <div class=\"qr-code\">\r\n                  <i class=\"el-icon-qrcode\"></i>\r\n                </div>\r\n                <p class=\"qr-code-id\">{{ registrationId }}</p>\r\n              </div>\r\n              <div class=\"credential-info\">\r\n                <h6>使用说明</h6>\r\n                <ul>\r\n                  <li>请保存此二维码截图</li>\r\n                  <li>审核通过后可用于园区门禁</li>\r\n                  <li>凭证仅限当次访问使用</li>\r\n                  <li>如有疑问请联系园区前台</li>\r\n                </ul>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 底部操作按钮 -->\r\n        <div class=\"form-actions\">\r\n          <el-button v-if=\"!registrationCompleted\"\r\n                     type=\"primary\" @click=\"submitForm\" size=\"large\" :loading=\"submitting\">\r\n            {{ submitting ? '登记中...' : `确认登记 (${totalVisitors}人)` }}\r\n          </el-button>\r\n\r\n          <el-button v-if=\"!registrationCompleted\" @click=\"resetForm\" size=\"large\">\r\n            重置表单\r\n          </el-button>\r\n\r\n          <div v-if=\"registrationCompleted\" class=\"final-actions\">\r\n            <el-button type=\"primary\" @click=\"printCredential\" size=\"large\">\r\n              <i class=\"el-icon-printer\"></i> 打印凭证\r\n            </el-button>\r\n            <el-button @click=\"resetForm\" size=\"large\">\r\n              <i class=\"el-icon-refresh\"></i> 重新登记\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { submitVisitorRegistration } from \"@/api/asc/visitor\";\r\n\r\nexport default {\r\n  name: \"SelfRegister\",\r\n  data() {\r\n    return {\r\n      submitting: false,\r\n      registrationCompleted: false,\r\n\r\n      // 表单数据 - 与微信端保持一致的结构\r\n      formData: {\r\n        // 访客列表（第一位为主联系人）\r\n        visitors: [\r\n          {\r\n            name: '',\r\n            phone: '',\r\n            idCard: '',\r\n            company: '',\r\n            isMainContact: true\r\n          }\r\n        ],\r\n        // 来访信息\r\n        visitInfo: {\r\n          reasonForVisit: '',\r\n          hostEmployeeName: '',\r\n          departmentVisited: '',\r\n          vehiclePlateNumber: '',\r\n          plannedEntryDatetime: '',\r\n          plannedExitDatetime: ''\r\n        }\r\n      },\r\n\r\n      // 表单验证规则 - 与微信端保持一致\r\n      visitRules: {\r\n        'visitInfo.reasonForVisit': [\r\n          { required: true, message: '请输入来访事由', trigger: 'blur' },\r\n          { min: 2, max: 200, message: '来访事由长度应在2-200个字符之间', trigger: 'blur' }\r\n        ],\r\n        'visitInfo.hostEmployeeName': [\r\n          { required: true, message: '请输入被访人姓名', trigger: 'blur' },\r\n          { min: 2, max: 20, message: '被访人姓名长度应在2-20个字符之间', trigger: 'blur' }\r\n        ],\r\n        'visitInfo.departmentVisited': [\r\n          { required: true, message: '请输入被访部门', trigger: 'blur' },\r\n          { min: 2, max: 50, message: '部门名称长度应在2-50个字符之间', trigger: 'blur' }\r\n        ],\r\n        'visitInfo.plannedEntryDatetime': [\r\n          { required: true, message: '请选择预计来访时间', trigger: 'change' }\r\n        ],\r\n        'visitInfo.plannedExitDatetime': [\r\n          { required: true, message: '请选择预计离开时间', trigger: 'change' }\r\n        ],\r\n        // 动态访客验证规则\r\n        'visitors.0.name': [\r\n          { required: true, message: '请输入主联系人姓名', trigger: 'blur' },\r\n          { min: 2, max: 20, message: '姓名长度应在2-20个字符之间', trigger: 'blur' }\r\n        ],\r\n        'visitors.0.phone': [\r\n          { required: true, message: '请输入主联系人手机号', trigger: 'blur' },\r\n          { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }\r\n        ],\r\n        'visitors.0.idCard': [\r\n          { required: true, message: '请输入主联系人身份证号', trigger: 'blur' }\r\n        ]\r\n      },\r\n\r\n      // 登记结果\r\n      registrationId: null,\r\n\r\n      // 时间选择器配置\r\n      entryPickerOptions: {\r\n        shortcuts: [{\r\n          text: '现在',\r\n          onClick(picker) {\r\n            picker.$emit('pick', new Date());\r\n          }\r\n        }, {\r\n          text: '1小时后',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setTime(date.getTime() + 3600 * 1000);\r\n            picker.$emit('pick', date);\r\n          }\r\n        }, {\r\n          text: '明天上午9点',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setDate(date.getDate() + 1);\r\n            date.setHours(9, 0, 0, 0);\r\n            picker.$emit('pick', date);\r\n          }\r\n        }],\r\n        disabledDate(time) {\r\n          // 允许选择当前时间前1小时到未来30天\r\n          const oneHourBefore = Date.now() - 3600 * 1000;\r\n          const thirtyDaysLater = Date.now() + 30 * 24 * 3600 * 1000;\r\n          return time.getTime() < oneHourBefore || time.getTime() > thirtyDaysLater;\r\n        }\r\n      },\r\n\r\n      exitPickerOptions: {\r\n        shortcuts: [{\r\n          text: '2小时后',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setTime(date.getTime() + 2 * 3600 * 1000);\r\n            picker.$emit('pick', date);\r\n          }\r\n        }, {\r\n          text: '今天下午6点',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setHours(18, 0, 0, 0);\r\n            if (date.getTime() < Date.now()) {\r\n              date.setDate(date.getDate() + 1);\r\n            }\r\n            picker.$emit('pick', date);\r\n          }\r\n        }, {\r\n          text: '明天下午6点',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setDate(date.getDate() + 1);\r\n            date.setHours(18, 0, 0, 0);\r\n            picker.$emit('pick', date);\r\n          }\r\n        }],\r\n        disabledDate(time) {\r\n          // 允许选择当前时间到未来30天\r\n          const thirtyDaysLater = Date.now() + 30 * 24 * 3600 * 1000;\r\n          return time.getTime() < Date.now() - 8.64e7 || time.getTime() > thirtyDaysLater;\r\n        }\r\n      }\r\n    };\r\n  },\r\n\r\n  computed: {\r\n    // 总访客人数\r\n    totalVisitors() {\r\n      return this.formData.visitors.length;\r\n    },\r\n\r\n    // 主联系人信息\r\n    mainContact() {\r\n      return this.formData.visitors[0] || {};\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    // 添加访客\r\n    addVisitor() {\r\n      if (this.formData.visitors.length >= 10) {\r\n        this.$message.warning('最多只能添加10名访客');\r\n        return;\r\n      }\r\n\r\n      this.formData.visitors.push({\r\n        name: '',\r\n        phone: '',\r\n        idCard: '',\r\n        company: '',\r\n        isMainContact: false\r\n      });\r\n\r\n      this.$message.success('已添加访客');\r\n    },\r\n\r\n    // 移除访客\r\n    removeVisitor(index) {\r\n      if (index === 0) {\r\n        this.$message.warning('不能删除主联系人');\r\n        return;\r\n      }\r\n\r\n      this.formData.visitors.splice(index, 1);\r\n      this.$message.success('已移除访客');\r\n    },\r\n\r\n    // 格式化日期为后端期望的格式\r\n    formatDateForBackend(dateStr) {\r\n      if (!dateStr) return '';\r\n\r\n      const date = new Date(dateStr);\r\n      if (isNaN(date.getTime())) return '';\r\n\r\n      const year = date.getFullYear();\r\n      const month = String(date.getMonth() + 1).padStart(2, '0');\r\n      const day = String(date.getDate()).padStart(2, '0');\r\n      const hours = String(date.getHours()).padStart(2, '0');\r\n      const minutes = String(date.getMinutes()).padStart(2, '0');\r\n      const seconds = String(date.getSeconds()).padStart(2, '0');\r\n\r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n    },\r\n\r\n    /** 来访时间变更事件 */\r\n    onArrivalTimeChange(value) {\r\n      console.log('预计来访时间变更:', value);\r\n      // 自动设置离开时间为来访时间后4小时\r\n      if (value && !this.formData.visitInfo.plannedExitDatetime) {\r\n        const arrivalTime = new Date(value);\r\n        const departureTime = new Date(arrivalTime.getTime() + 4 * 60 * 60 * 1000);\r\n\r\n        const year = departureTime.getFullYear();\r\n        const month = String(departureTime.getMonth() + 1).padStart(2, '0');\r\n        const day = String(departureTime.getDate()).padStart(2, '0');\r\n        const hours = String(departureTime.getHours()).padStart(2, '0');\r\n        const minutes = String(departureTime.getMinutes()).padStart(2, '0');\r\n        const seconds = String(departureTime.getSeconds()).padStart(2, '0');\r\n\r\n        this.formData.visitInfo.plannedExitDatetime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n      }\r\n    },\r\n\r\n    /** 离开时间变更事件 */\r\n    onDepartureTimeChange(value) {\r\n      console.log('预计离开时间变更:', value);\r\n      // 验证离开时间不能早于来访时间\r\n      if (value && this.formData.visitInfo.plannedEntryDatetime) {\r\n        const arrivalTime = new Date(this.formData.visitInfo.plannedEntryDatetime);\r\n        const departureTime = new Date(value);\r\n\r\n        if (departureTime <= arrivalTime) {\r\n          this.$message.warning('预计离开时间不能早于或等于来访时间');\r\n          this.formData.visitInfo.plannedExitDatetime = '';\r\n        }\r\n      }\r\n    },\r\n\r\n    /** 提交表单 */\r\n    async submitForm() {\r\n      try {\r\n        // 验证表单\r\n        await this.$refs.visitForm.validate();\r\n\r\n        // 验证访客信息\r\n        if (!this.validateVisitors()) {\r\n          return;\r\n        }\r\n\r\n        // 验证时间\r\n        if (!this.validateTimes()) {\r\n          return;\r\n        }\r\n\r\n        this.submitting = true;\r\n\r\n        // 获取主联系人信息\r\n        const mainContact = this.formData.visitors[0];\r\n\r\n        const submitData = {\r\n          // VisitRegistrations 对象\r\n          registration: {\r\n            primaryContactName: mainContact.name.trim(),\r\n            primaryContactPhone: mainContact.phone.trim(),\r\n            reasonForVisit: this.formData.visitInfo.reasonForVisit,\r\n            hostEmployeeName: this.formData.visitInfo.hostEmployeeName,\r\n            hostEmployeeId: null, // 前端暂时没有被访人ID，后端可能会自动处理\r\n            departmentVisited: this.formData.visitInfo.departmentVisited,\r\n            plannedEntryDatetime: this.formatDateForBackend(this.formData.visitInfo.plannedEntryDatetime),\r\n            plannedExitDatetime: this.formatDateForBackend(this.formData.visitInfo.plannedExitDatetime),\r\n            vehiclePlateNumber: this.formData.visitInfo.vehiclePlateNumber || '',\r\n            totalCompanions: this.formData.visitors.length - 1\r\n          },\r\n          // RegistrationAttendees 数组 - 使用后端期望的临时字段\r\n          attendeesList: this.formData.visitors.map((visitor, index) => ({\r\n            visitorName: visitor.name.trim(),\r\n            visitorPhone: visitor.phone.trim(),\r\n            visitorIdCard: visitor.idCard.trim().toUpperCase(),\r\n            visitorCompany: (visitor.company || '').trim(),\r\n            isPrimary: index === 0 ? \"1\" : \"0\", // 第一个访客必须是主联系人\r\n            visitorAvatarPhoto: null // 暂时不支持头像上传\r\n          }))\r\n        };\r\n\r\n        console.log('提交数据结构:', JSON.stringify(submitData, null, 2));\r\n\r\n        // 调用API提交数据\r\n        const response = await submitVisitorRegistration(submitData);\r\n\r\n        if (response.code === 200) {\r\n          this.registrationId = response.data || 'VR' + Date.now();\r\n          this.$message.success(`${this.totalVisitors}名访客登记成功！`);\r\n          this.registrationCompleted = true;\r\n        } else {\r\n          this.$message.error(response.msg || '登记失败，请重试');\r\n        }\r\n      } catch (error) {\r\n        console.error('提交表单失败:', error);\r\n        this.$message.error('登记失败，请检查网络连接');\r\n      } finally {\r\n        this.submitting = false;\r\n      }\r\n    },\r\n\r\n    // 验证访客信息\r\n    validateVisitors() {\r\n      for (let i = 0; i < this.formData.visitors.length; i++) {\r\n        const visitor = this.formData.visitors[i];\r\n        const visitorTitle = i === 0 ? '主联系人' : `访客${i + 1}`;\r\n\r\n        // 验证姓名\r\n        if (!visitor.name || visitor.name.trim() === '') {\r\n          this.$message.error(`请输入${visitorTitle}的姓名`);\r\n          return false;\r\n        }\r\n\r\n        if (visitor.name.trim().length < 2 || visitor.name.trim().length > 20) {\r\n          this.$message.error(`${visitorTitle}的姓名长度应在2-20个字符之间`);\r\n          return false;\r\n        }\r\n\r\n        // 验证手机号\r\n        if (!visitor.phone || visitor.phone.trim() === '') {\r\n          this.$message.error(`请输入${visitorTitle}的手机号`);\r\n          return false;\r\n        }\r\n\r\n        // 验证手机号格式（与后端保持一致）\r\n        const phonePattern = /^1[3-9]\\d{9}$/;\r\n        if (!phonePattern.test(visitor.phone.trim())) {\r\n          this.$message.error(`${visitorTitle}的手机号格式不正确`);\r\n          return false;\r\n        }\r\n\r\n        // 验证身份证号\r\n        if (!visitor.idCard || visitor.idCard.trim() === '') {\r\n          this.$message.error(`请输入${visitorTitle}的身份证号`);\r\n          return false;\r\n        }\r\n\r\n        const idCard = visitor.idCard.trim().toUpperCase();\r\n\r\n        // 如果是18位身份证号，验证格式\r\n        if (idCard.length === 18) {\r\n          const idPattern = /^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$/;\r\n          if (!idPattern.test(idCard)) {\r\n            this.$message.error(`${visitorTitle}的身份证号格式不正确`);\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 检查身份证号是否重复\r\n        for (let j = 0; j < this.formData.visitors.length; j++) {\r\n          if (i !== j && visitor.idCard.trim().toUpperCase() === this.formData.visitors[j].idCard.trim().toUpperCase()) {\r\n            this.$message.error(`${visitorTitle}与访客${j === 0 ? '主联系人' : j + 1}的身份证号重复`);\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 检查手机号是否重复\r\n        for (let j = 0; j < this.formData.visitors.length; j++) {\r\n          if (i !== j && visitor.phone.trim() === this.formData.visitors[j].phone.trim()) {\r\n            this.$message.error(`${visitorTitle}与访客${j === 0 ? '主联系人' : j + 1}的手机号重复，请确认是否为同一人`);\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 清理和标准化数据\r\n        visitor.name = visitor.name.trim();\r\n        visitor.phone = visitor.phone.trim();\r\n        visitor.idCard = visitor.idCard.trim().toUpperCase();\r\n        visitor.company = (visitor.company || '').trim();\r\n      }\r\n\r\n      return true;\r\n    },\r\n\r\n    // 验证时间\r\n    validateTimes() {\r\n      if (!this.formData.visitInfo.plannedEntryDatetime) {\r\n        this.$message.error('请选择预计来访时间');\r\n        return false;\r\n      }\r\n\r\n      if (!this.formData.visitInfo.plannedExitDatetime) {\r\n        this.$message.error('请选择预计离开时间');\r\n        return false;\r\n      }\r\n\r\n      const entryTime = new Date(this.formData.visitInfo.plannedEntryDatetime);\r\n      const exitTime = new Date(this.formData.visitInfo.plannedExitDatetime);\r\n      const now = new Date();\r\n\r\n      // 检查来访时间不能早于当前时间1小时前\r\n      const oneHourBefore = new Date(now.getTime() - 60 * 60 * 1000);\r\n      if (entryTime < oneHourBefore) {\r\n        this.$message.error('预计来访时间不能早于当前时间1小时前');\r\n        return false;\r\n      }\r\n\r\n      // 检查离开时间必须晚于来访时间\r\n      if (exitTime <= entryTime) {\r\n        this.$message.error('预计离开时间必须晚于来访时间');\r\n        return false;\r\n      }\r\n\r\n      // 检查访问时长不能超过24小时\r\n      const visitDuration = exitTime.getTime() - entryTime.getTime();\r\n      const maxDuration = 24 * 60 * 60 * 1000; // 24小时\r\n      if (visitDuration > maxDuration) {\r\n        this.$message.error('单次访问时长不能超过24小时');\r\n        return false;\r\n      }\r\n\r\n      return true;\r\n    },\r\n\r\n    // 重置表单\r\n    resetForm() {\r\n      this.$confirm('确定要重置表单吗？当前填写的信息将会丢失。', '确认重置', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 重置所有数据\r\n        this.registrationCompleted = false;\r\n\r\n        this.formData = {\r\n          visitors: [\r\n            {\r\n              name: '',\r\n              phone: '',\r\n              idCard: '',\r\n              company: '',\r\n              isMainContact: true\r\n            }\r\n          ],\r\n          visitInfo: {\r\n            reasonForVisit: '',\r\n            hostEmployeeName: '',\r\n            departmentVisited: '',\r\n            vehiclePlateNumber: '',\r\n            plannedEntryDatetime: '',\r\n            plannedExitDatetime: ''\r\n          }\r\n        };\r\n\r\n        this.registrationId = null;\r\n\r\n        this.$message.success('表单已重置');\r\n      });\r\n    },\r\n\r\n    // 打印凭证\r\n    printCredential() {\r\n      this.$message.info('打印功能开发中...');\r\n    },\r\n\r\n    // 格式化时间显示\r\n    parseTime(time) {\r\n      if (!time) return '';\r\n      const date = new Date(time);\r\n      if (isNaN(date.getTime())) return '';\r\n\r\n      const year = date.getFullYear();\r\n      const month = String(date.getMonth() + 1).padStart(2, '0');\r\n      const day = String(date.getDate()).padStart(2, '0');\r\n      const hours = String(date.getHours()).padStart(2, '0');\r\n      const minutes = String(date.getMinutes()).padStart(2, '0');\r\n\r\n      return `${year}-${month}-${day} ${hours}:${minutes}`;\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 主容器 */\r\n.pc-register-container {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n  font-family: 'Helvetica Neue', Arial, sans-serif;\r\n}\r\n\r\n/* 顶部导航 */\r\n.top-nav {\r\n  background: #fff;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  padding: 0 40px;\r\n  position: sticky;\r\n  top: 0;\r\n  z-index: 100;\r\n}\r\n\r\n.nav-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  height: 70px;\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.logo-section {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n}\r\n\r\n.nav-logo {\r\n  height: 40px;\r\n}\r\n\r\n.company-name {\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n.nav-actions {\r\n  display: flex;\r\n  gap: 20px;\r\n}\r\n\r\n/* 主内容区域 */\r\n.main-content {\r\n  display: flex;\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n  padding: 40px;\r\n  gap: 40px;\r\n  min-height: calc(100vh - 110px);\r\n}\r\n\r\n/* 左侧信息面板 */\r\n.info-panel {\r\n  flex: 0 0 400px;\r\n}\r\n\r\n.welcome-card {\r\n  background: #fff;\r\n  border-radius: 16px;\r\n  padding: 32px;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\r\n  height: fit-content;\r\n  position: sticky;\r\n  top: 110px;\r\n}\r\n\r\n.welcome-icon {\r\n  text-align: center;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.welcome-icon i {\r\n  font-size: 64px;\r\n  color: #3498db;\r\n}\r\n\r\n.welcome-card h2 {\r\n  text-align: center;\r\n  color: #2c3e50;\r\n  margin-bottom: 16px;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n}\r\n\r\n.welcome-desc {\r\n  text-align: center;\r\n  color: #7f8c8d;\r\n  margin-bottom: 32px;\r\n  font-size: 16px;\r\n  line-height: 1.6;\r\n}\r\n\r\n/* 流程步骤 */\r\n.process-steps {\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.step-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n  margin-bottom: 24px;\r\n  padding: 16px;\r\n  border-radius: 12px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.step-item.active {\r\n  background: linear-gradient(135deg, #3498db, #2980b9);\r\n  color: white;\r\n  transform: translateX(8px);\r\n}\r\n\r\n.step-item:not(.active) {\r\n  background: #f8f9fa;\r\n  color: #6c757d;\r\n}\r\n\r\n.step-circle {\r\n  width: 32px;\r\n  height: 32px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-weight: bold;\r\n  font-size: 14px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.step-item.active .step-circle {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  color: white;\r\n}\r\n\r\n.step-item:not(.active) .step-circle {\r\n  background: #dee2e6;\r\n  color: #6c757d;\r\n}\r\n\r\n.step-text h4 {\r\n  margin: 0 0 4px 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.step-text p {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  opacity: 0.8;\r\n}\r\n\r\n/* 安全提示 */\r\n.security-tips {\r\n  background: #f8f9fa;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  border-left: 4px solid #3498db;\r\n}\r\n\r\n.security-tips h4 {\r\n  margin: 0 0 12px 0;\r\n  color: #2c3e50;\r\n  font-size: 16px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.security-tips ul {\r\n  margin: 0;\r\n  padding-left: 20px;\r\n  color: #6c757d;\r\n}\r\n\r\n.security-tips li {\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n  line-height: 1.4;\r\n}\r\n\r\n/* 右侧表单面板 */\r\n.form-panel {\r\n  flex: 1;\r\n  background: #fff;\r\n  border-radius: 16px;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n/* 进度指示器 */\r\n.progress-indicator {\r\n  background: #f8f9fa;\r\n  padding: 24px 32px;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.progress-bar {\r\n  height: 6px;\r\n  background: #e9ecef;\r\n  border-radius: 3px;\r\n  overflow: hidden;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.progress-fill {\r\n  height: 100%;\r\n  background: linear-gradient(90deg, #3498db, #2980b9);\r\n  border-radius: 3px;\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.progress-text {\r\n  text-align: center;\r\n  color: #6c757d;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 表单内容 */\r\n.form-content {\r\n  padding: 32px;\r\n  max-height: calc(100vh - 300px);\r\n  overflow-y: auto;\r\n}\r\n\r\n.form-header {\r\n  text-align: center;\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.form-header h3 {\r\n  color: #2c3e50;\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  margin-bottom: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 12px;\r\n}\r\n\r\n.form-header p {\r\n  color: #7f8c8d;\r\n  margin: 0;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 验证方式网格 */\r\n.verify-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 20px;\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.verify-card {\r\n  position: relative;\r\n  background: #f8f9fa;\r\n  border: 2px solid #e9ecef;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.verify-card:hover {\r\n  border-color: #3498db;\r\n  background: #fff;\r\n  box-shadow: 0 4px 16px rgba(52, 152, 219, 0.15);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.verify-card.selected {\r\n  border-color: #3498db;\r\n  background: linear-gradient(135deg, #3498db, #2980b9);\r\n  color: white;\r\n  box-shadow: 0 8px 24px rgba(52, 152, 219, 0.3);\r\n}\r\n\r\n.verify-icon {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.verify-icon i {\r\n  font-size: 48px;\r\n  color: #3498db;\r\n}\r\n\r\n.verify-card.selected .verify-icon i {\r\n  color: white;\r\n}\r\n\r\n.verify-card h4 {\r\n  margin: 0 0 8px 0;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.verify-card p {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  opacity: 0.8;\r\n}\r\n\r\n.verify-status {\r\n  position: absolute;\r\n  top: 12px;\r\n  right: 12px;\r\n  width: 24px;\r\n  height: 24px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n/* 验证操作区域 */\r\n.verify-operation {\r\n  margin-top: 32px;\r\n  padding: 24px;\r\n  background: #f8f9fa;\r\n  border-radius: 12px;\r\n}\r\n\r\n.operation-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.operation-header h4 {\r\n  margin: 0;\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n/* 身份表单 */\r\n.identity-form {\r\n  background: white;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n}\r\n\r\n/* 身份证读卡器 */\r\n.id-card-reader {\r\n  text-align: center;\r\n}\r\n\r\n.reader-visual {\r\n  background: white;\r\n  padding: 40px;\r\n  border-radius: 12px;\r\n}\r\n\r\n.reader-animation {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.rotating {\r\n  animation: rotate 2s linear infinite;\r\n}\r\n\r\n@keyframes rotate {\r\n  from { transform: rotate(0deg); }\r\n  to { transform: rotate(360deg); }\r\n}\r\n\r\n.reader-animation i {\r\n  font-size: 64px;\r\n  color: #3498db;\r\n}\r\n\r\n.reader-visual h4 {\r\n  color: #2c3e50;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.reader-visual p {\r\n  color: #7f8c8d;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.reader-tips {\r\n  margin: 24px 0;\r\n}\r\n\r\n/* 人脸识别 */\r\n.face-recognition {\r\n  text-align: center;\r\n}\r\n\r\n.camera-container {\r\n  background: white;\r\n  padding: 40px;\r\n  border-radius: 12px;\r\n}\r\n\r\n.camera-frame {\r\n  position: relative;\r\n  width: 280px;\r\n  height: 210px;\r\n  margin: 0 auto 24px;\r\n  border: 3px solid #3498db;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #f8f9fa;\r\n  overflow: hidden;\r\n}\r\n\r\n.camera-overlay {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 16px;\r\n}\r\n\r\n.face-outline {\r\n  width: 120px;\r\n  height: 150px;\r\n  border: 2px dashed #3498db;\r\n  border-radius: 60px;\r\n  opacity: 0.6;\r\n}\r\n\r\n.camera-icon {\r\n  font-size: 32px;\r\n  color: #3498db;\r\n}\r\n\r\n.scanning-line {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 2px;\r\n  background: linear-gradient(90deg, transparent, #3498db, transparent);\r\n  animation: scan 2s ease-in-out infinite;\r\n}\r\n\r\n@keyframes scan {\r\n  0% { transform: translateY(0); }\r\n  50% { transform: translateY(206px); }\r\n  100% { transform: translateY(0); }\r\n}\r\n\r\n/* 表单分组 */\r\n.form-section {\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.section-title {\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin-bottom: 16px;\r\n  padding-bottom: 8px;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n/* 同行人员 */\r\n.no-companions {\r\n  text-align: center;\r\n  padding: 40px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.no-companions i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  color: #bdc3c7;\r\n}\r\n\r\n.companions-table {\r\n  background: #f8f9fa;\r\n  padding: 16px;\r\n  border-radius: 8px;\r\n}\r\n\r\n/* 成功页面 */\r\n.success-content {\r\n  text-align: center;\r\n}\r\n\r\n.success-icon {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.success-icon i {\r\n  font-size: 80px;\r\n  color: #27ae60;\r\n}\r\n\r\n.success-content h4 {\r\n  color: #2c3e50;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.success-message {\r\n  color: #7f8c8d;\r\n  font-size: 16px;\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.register-summary {\r\n  background: #f8f9fa;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n  margin-bottom: 32px;\r\n  text-align: left;\r\n}\r\n\r\n.register-summary h5 {\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin-bottom: 16px;\r\n  text-align: center;\r\n}\r\n\r\n.access-credential {\r\n  background: #f8f9fa;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n  text-align: left;\r\n}\r\n\r\n.access-credential h5 {\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin-bottom: 16px;\r\n  text-align: center;\r\n}\r\n\r\n.credential-card {\r\n  display: flex;\r\n  gap: 24px;\r\n  background: white;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n  border: 2px solid #e9ecef;\r\n}\r\n\r\n.qr-code-container {\r\n  flex: 0 0 120px;\r\n  text-align: center;\r\n}\r\n\r\n.qr-code {\r\n  width: 120px;\r\n  height: 120px;\r\n  border: 2px solid #e9ecef;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #f8f9fa;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.qr-code i {\r\n  font-size: 48px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.qr-code-id {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n  font-family: monospace;\r\n  margin: 0;\r\n}\r\n\r\n.credential-info {\r\n  flex: 1;\r\n}\r\n\r\n.credential-info h6 {\r\n  color: #2c3e50;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.credential-info ul {\r\n  margin: 0;\r\n  padding-left: 20px;\r\n  color: #6c757d;\r\n}\r\n\r\n.credential-info li {\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n  line-height: 1.4;\r\n}\r\n\r\n/* 底部操作按钮 */\r\n.form-actions {\r\n  padding: 24px 32px;\r\n  background: #f8f9fa;\r\n  border-top: 1px solid #e9ecef;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.final-actions {\r\n  display: flex;\r\n  gap: 16px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n  .main-content {\r\n    flex-direction: column;\r\n    gap: 24px;\r\n  }\r\n  \r\n  .info-panel {\r\n    flex: none;\r\n  }\r\n  \r\n  .welcome-card {\r\n    position: static;\r\n  }\r\n  \r\n  .verify-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .main-content {\r\n    padding: 20px;\r\n  }\r\n  \r\n  .nav-content {\r\n    padding: 0 20px;\r\n  }\r\n  \r\n  .company-name {\r\n    font-size: 16px;\r\n  }\r\n  \r\n  .form-content {\r\n    padding: 20px;\r\n  }\r\n  \r\n  .credential-card {\r\n    flex-direction: column;\r\n    text-align: center;\r\n  }\r\n  \r\n  .form-actions {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n}\r\n\r\n/* Element UI 样式覆盖 */\r\n.el-form-item {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.el-input__inner {\r\n  height: 44px;\r\n  border-radius: 8px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.el-input__inner:focus {\r\n  border-color: #3498db;\r\n  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.1);\r\n}\r\n\r\n.el-select {\r\n  width: 100%;\r\n}\r\n\r\n.el-date-editor {\r\n  width: 100%;\r\n}\r\n\r\n.el-button--large {\r\n  height: 44px;\r\n  padding: 12px 24px;\r\n  border-radius: 8px;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n}\r\n\r\n.el-button--primary {\r\n  background: linear-gradient(135deg, #3498db, #2980b9);\r\n  border: none;\r\n}\r\n\r\n.el-button--primary:hover {\r\n  background: linear-gradient(135deg, #2980b9, #1f4e79);\r\n}\r\n\r\n.el-descriptions {\r\n  border-radius: 8px;\r\n}\r\n\r\n.el-table {\r\n  border-radius: 8px;\r\n}\r\n\r\n.el-alert {\r\n  border-radius: 8px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAoRA,IAAAA,QAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,qBAAA;MAEA;MACAC,QAAA;QACA;QACAC,QAAA,GACA;UACAL,IAAA;UACAM,KAAA;UACAC,MAAA;UACAC,OAAA;UACAC,aAAA;QACA,EACA;QACA;QACAC,SAAA;UACAC,cAAA;UACAC,gBAAA;UACAC,iBAAA;UACAC,kBAAA;UACAC,oBAAA;UACAC,mBAAA;QACA;MACA;MAEA;MACAC,UAAA;QACA,6BACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACA,+BACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACA,gCACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACA,mCACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA,kCACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA;QACA,oBACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACA,qBACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAG,OAAA;UAAAJ,OAAA;UAAAC,OAAA;QAAA,EACA;QACA,sBACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MAEA;MACAI,cAAA;MAEA;MACAC,kBAAA;QACAC,SAAA;UACAC,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACAA,MAAA,CAAAC,KAAA,aAAAC,IAAA;UACA;QACA;UACAJ,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAG,IAAA,OAAAD,IAAA;YACAC,IAAA,CAAAC,OAAA,CAAAD,IAAA,CAAAE,OAAA;YACAL,MAAA,CAAAC,KAAA,SAAAE,IAAA;UACA;QACA;UACAL,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAG,IAAA,OAAAD,IAAA;YACAC,IAAA,CAAAG,OAAA,CAAAH,IAAA,CAAAI,OAAA;YACAJ,IAAA,CAAAK,QAAA;YACAR,MAAA,CAAAC,KAAA,SAAAE,IAAA;UACA;QACA;QACAM,YAAA,WAAAA,aAAAC,IAAA;UACA;UACA,IAAAC,aAAA,GAAAT,IAAA,CAAAU,GAAA;UACA,IAAAC,eAAA,GAAAX,IAAA,CAAAU,GAAA;UACA,OAAAF,IAAA,CAAAL,OAAA,KAAAM,aAAA,IAAAD,IAAA,CAAAL,OAAA,KAAAQ,eAAA;QACA;MACA;MAEAC,iBAAA;QACAjB,SAAA;UACAC,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAG,IAAA,OAAAD,IAAA;YACAC,IAAA,CAAAC,OAAA,CAAAD,IAAA,CAAAE,OAAA;YACAL,MAAA,CAAAC,KAAA,SAAAE,IAAA;UACA;QACA;UACAL,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAG,IAAA,OAAAD,IAAA;YACAC,IAAA,CAAAK,QAAA;YACA,IAAAL,IAAA,CAAAE,OAAA,KAAAH,IAAA,CAAAU,GAAA;cACAT,IAAA,CAAAG,OAAA,CAAAH,IAAA,CAAAI,OAAA;YACA;YACAP,MAAA,CAAAC,KAAA,SAAAE,IAAA;UACA;QACA;UACAL,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAG,IAAA,OAAAD,IAAA;YACAC,IAAA,CAAAG,OAAA,CAAAH,IAAA,CAAAI,OAAA;YACAJ,IAAA,CAAAK,QAAA;YACAR,MAAA,CAAAC,KAAA,SAAAE,IAAA;UACA;QACA;QACAM,YAAA,WAAAA,aAAAC,IAAA;UACA;UACA,IAAAG,eAAA,GAAAX,IAAA,CAAAU,GAAA;UACA,OAAAF,IAAA,CAAAL,OAAA,KAAAH,IAAA,CAAAU,GAAA,eAAAF,IAAA,CAAAL,OAAA,KAAAQ,eAAA;QACA;MACA;IACA;EACA;EAEAE,QAAA;IACA;IACAC,aAAA,WAAAA,cAAA;MACA,YAAAzC,QAAA,CAAAC,QAAA,CAAAyC,MAAA;IACA;IAEA;IACAC,WAAA,WAAAA,YAAA;MACA,YAAA3C,QAAA,CAAAC,QAAA;IACA;EACA;EAEA2C,OAAA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,SAAA7C,QAAA,CAAAC,QAAA,CAAAyC,MAAA;QACA,KAAAI,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,KAAA/C,QAAA,CAAAC,QAAA,CAAA+C,IAAA;QACApD,IAAA;QACAM,KAAA;QACAC,MAAA;QACAC,OAAA;QACAC,aAAA;MACA;MAEA,KAAAyC,QAAA,CAAAG,OAAA;IACA;IAEA;IACAC,aAAA,WAAAA,cAAAC,KAAA;MACA,IAAAA,KAAA;QACA,KAAAL,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,KAAA/C,QAAA,CAAAC,QAAA,CAAAmD,MAAA,CAAAD,KAAA;MACA,KAAAL,QAAA,CAAAG,OAAA;IACA;IAEA;IACAI,oBAAA,WAAAA,qBAAAC,OAAA;MACA,KAAAA,OAAA;MAEA,IAAA1B,IAAA,OAAAD,IAAA,CAAA2B,OAAA;MACA,IAAAC,KAAA,CAAA3B,IAAA,CAAAE,OAAA;MAEA,IAAA0B,IAAA,GAAA5B,IAAA,CAAA6B,WAAA;MACA,IAAAC,KAAA,GAAAC,MAAA,CAAA/B,IAAA,CAAAgC,QAAA,QAAAC,QAAA;MACA,IAAAC,GAAA,GAAAH,MAAA,CAAA/B,IAAA,CAAAI,OAAA,IAAA6B,QAAA;MACA,IAAAE,KAAA,GAAAJ,MAAA,CAAA/B,IAAA,CAAAoC,QAAA,IAAAH,QAAA;MACA,IAAAI,OAAA,GAAAN,MAAA,CAAA/B,IAAA,CAAAsC,UAAA,IAAAL,QAAA;MACA,IAAAM,OAAA,GAAAR,MAAA,CAAA/B,IAAA,CAAAwC,UAAA,IAAAP,QAAA;MAEA,UAAAQ,MAAA,CAAAb,IAAA,OAAAa,MAAA,CAAAX,KAAA,OAAAW,MAAA,CAAAP,GAAA,OAAAO,MAAA,CAAAN,KAAA,OAAAM,MAAA,CAAAJ,OAAA,OAAAI,MAAA,CAAAF,OAAA;IACA;IAEA,eACAG,mBAAA,WAAAA,oBAAAC,KAAA;MACAC,OAAA,CAAAC,GAAA,cAAAF,KAAA;MACA;MACA,IAAAA,KAAA,UAAAvE,QAAA,CAAAM,SAAA,CAAAM,mBAAA;QACA,IAAA8D,WAAA,OAAA/C,IAAA,CAAA4C,KAAA;QACA,IAAAI,aAAA,OAAAhD,IAAA,CAAA+C,WAAA,CAAA5C,OAAA;QAEA,IAAA0B,IAAA,GAAAmB,aAAA,CAAAlB,WAAA;QACA,IAAAC,KAAA,GAAAC,MAAA,CAAAgB,aAAA,CAAAf,QAAA,QAAAC,QAAA;QACA,IAAAC,GAAA,GAAAH,MAAA,CAAAgB,aAAA,CAAA3C,OAAA,IAAA6B,QAAA;QACA,IAAAE,KAAA,GAAAJ,MAAA,CAAAgB,aAAA,CAAAX,QAAA,IAAAH,QAAA;QACA,IAAAI,OAAA,GAAAN,MAAA,CAAAgB,aAAA,CAAAT,UAAA,IAAAL,QAAA;QACA,IAAAM,OAAA,GAAAR,MAAA,CAAAgB,aAAA,CAAAP,UAAA,IAAAP,QAAA;QAEA,KAAA7D,QAAA,CAAAM,SAAA,CAAAM,mBAAA,MAAAyD,MAAA,CAAAb,IAAA,OAAAa,MAAA,CAAAX,KAAA,OAAAW,MAAA,CAAAP,GAAA,OAAAO,MAAA,CAAAN,KAAA,OAAAM,MAAA,CAAAJ,OAAA,OAAAI,MAAA,CAAAF,OAAA;MACA;IACA;IAEA,eACAS,qBAAA,WAAAA,sBAAAL,KAAA;MACAC,OAAA,CAAAC,GAAA,cAAAF,KAAA;MACA;MACA,IAAAA,KAAA,SAAAvE,QAAA,CAAAM,SAAA,CAAAK,oBAAA;QACA,IAAA+D,WAAA,OAAA/C,IAAA,MAAA3B,QAAA,CAAAM,SAAA,CAAAK,oBAAA;QACA,IAAAgE,aAAA,OAAAhD,IAAA,CAAA4C,KAAA;QAEA,IAAAI,aAAA,IAAAD,WAAA;UACA,KAAA5B,QAAA,CAAAC,OAAA;UACA,KAAA/C,QAAA,CAAAM,SAAA,CAAAM,mBAAA;QACA;MACA;IACA;IAEA,WACAiE,UAAA,WAAAA,WAAA;MAAA,IAAAC,KAAA;MAAA,WAAAC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAC,QAAA;QAAA,IAAAxC,WAAA,EAAAyC,UAAA,EAAAC,QAAA,EAAAC,EAAA;QAAA,WAAAL,aAAA,CAAAD,OAAA,IAAAO,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAF,QAAA,CAAAC,CAAA;cAAA,OAGAX,KAAA,CAAAa,KAAA,CAAAC,SAAA,CAAAC,QAAA;YAAA;cAAA,IAGAf,KAAA,CAAAgB,gBAAA;gBAAAN,QAAA,CAAAC,CAAA;gBAAA;cAAA;cAAA,OAAAD,QAAA,CAAAO,CAAA;YAAA;cAAA,IAKAjB,KAAA,CAAAkB,aAAA;gBAAAR,QAAA,CAAAC,CAAA;gBAAA;cAAA;cAAA,OAAAD,QAAA,CAAAO,CAAA;YAAA;cAIAjB,KAAA,CAAAhF,UAAA;;cAEA;cACA6C,WAAA,GAAAmC,KAAA,CAAA9E,QAAA,CAAAC,QAAA;cAEAmF,UAAA;gBACA;gBACAa,YAAA;kBACAC,kBAAA,EAAAvD,WAAA,CAAA/C,IAAA,CAAAuG,IAAA;kBACAC,mBAAA,EAAAzD,WAAA,CAAAzC,KAAA,CAAAiG,IAAA;kBACA5F,cAAA,EAAAuE,KAAA,CAAA9E,QAAA,CAAAM,SAAA,CAAAC,cAAA;kBACAC,gBAAA,EAAAsE,KAAA,CAAA9E,QAAA,CAAAM,SAAA,CAAAE,gBAAA;kBACA6F,cAAA;kBAAA;kBACA5F,iBAAA,EAAAqE,KAAA,CAAA9E,QAAA,CAAAM,SAAA,CAAAG,iBAAA;kBACAE,oBAAA,EAAAmE,KAAA,CAAAzB,oBAAA,CAAAyB,KAAA,CAAA9E,QAAA,CAAAM,SAAA,CAAAK,oBAAA;kBACAC,mBAAA,EAAAkE,KAAA,CAAAzB,oBAAA,CAAAyB,KAAA,CAAA9E,QAAA,CAAAM,SAAA,CAAAM,mBAAA;kBACAF,kBAAA,EAAAoE,KAAA,CAAA9E,QAAA,CAAAM,SAAA,CAAAI,kBAAA;kBACA4F,eAAA,EAAAxB,KAAA,CAAA9E,QAAA,CAAAC,QAAA,CAAAyC,MAAA;gBACA;gBACA;gBACA6D,aAAA,EAAAzB,KAAA,CAAA9E,QAAA,CAAAC,QAAA,CAAAuG,GAAA,WAAAC,OAAA,EAAAtD,KAAA;kBAAA;oBACAuD,WAAA,EAAAD,OAAA,CAAA7G,IAAA,CAAAuG,IAAA;oBACAQ,YAAA,EAAAF,OAAA,CAAAvG,KAAA,CAAAiG,IAAA;oBACAS,aAAA,EAAAH,OAAA,CAAAtG,MAAA,CAAAgG,IAAA,GAAAU,WAAA;oBACAC,cAAA,GAAAL,OAAA,CAAArG,OAAA,QAAA+F,IAAA;oBACAY,SAAA,EAAA5D,KAAA;oBAAA;oBACA6D,kBAAA;kBACA;gBAAA;cACA;cAEAxC,OAAA,CAAAC,GAAA,YAAAwC,IAAA,CAAAC,SAAA,CAAA9B,UAAA;;cAEA;cAAAI,QAAA,CAAAC,CAAA;cAAA,OACA,IAAA0B,kCAAA,EAAA/B,UAAA;YAAA;cAAAC,QAAA,GAAAG,QAAA,CAAA4B,CAAA;cAEA,IAAA/B,QAAA,CAAAgC,IAAA;gBACAvC,KAAA,CAAA1D,cAAA,GAAAiE,QAAA,CAAAxF,IAAA,WAAA8B,IAAA,CAAAU,GAAA;gBACAyC,KAAA,CAAAhC,QAAA,CAAAG,OAAA,IAAAoB,MAAA,CAAAS,KAAA,CAAArC,aAAA;gBACAqC,KAAA,CAAA/E,qBAAA;cACA;gBACA+E,KAAA,CAAAhC,QAAA,CAAAwE,KAAA,CAAAjC,QAAA,CAAAkC,GAAA;cACA;cAAA/B,QAAA,CAAAC,CAAA;cAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAJ,EAAA,GAAAE,QAAA,CAAA4B,CAAA;cAEA5C,OAAA,CAAA8C,KAAA,YAAAhC,EAAA;cACAR,KAAA,CAAAhC,QAAA,CAAAwE,KAAA;YAAA;cAAA9B,QAAA,CAAAE,CAAA;cAEAZ,KAAA,CAAAhF,UAAA;cAAA,OAAA0F,QAAA,CAAAgC,CAAA;YAAA;cAAA,OAAAhC,QAAA,CAAAO,CAAA;UAAA;QAAA,GAAAZ,OAAA;MAAA;IAEA;IAEA;IACAW,gBAAA,WAAAA,iBAAA;MACA,SAAA2B,CAAA,MAAAA,CAAA,QAAAzH,QAAA,CAAAC,QAAA,CAAAyC,MAAA,EAAA+E,CAAA;QACA,IAAAhB,OAAA,QAAAzG,QAAA,CAAAC,QAAA,CAAAwH,CAAA;QACA,IAAAC,YAAA,GAAAD,CAAA,iCAAApD,MAAA,CAAAoD,CAAA;;QAEA;QACA,KAAAhB,OAAA,CAAA7G,IAAA,IAAA6G,OAAA,CAAA7G,IAAA,CAAAuG,IAAA;UACA,KAAArD,QAAA,CAAAwE,KAAA,sBAAAjD,MAAA,CAAAqD,YAAA;UACA;QACA;QAEA,IAAAjB,OAAA,CAAA7G,IAAA,CAAAuG,IAAA,GAAAzD,MAAA,QAAA+D,OAAA,CAAA7G,IAAA,CAAAuG,IAAA,GAAAzD,MAAA;UACA,KAAAI,QAAA,CAAAwE,KAAA,IAAAjD,MAAA,CAAAqD,YAAA;UACA;QACA;;QAEA;QACA,KAAAjB,OAAA,CAAAvG,KAAA,IAAAuG,OAAA,CAAAvG,KAAA,CAAAiG,IAAA;UACA,KAAArD,QAAA,CAAAwE,KAAA,sBAAAjD,MAAA,CAAAqD,YAAA;UACA;QACA;;QAEA;QACA,IAAAC,YAAA;QACA,KAAAA,YAAA,CAAAC,IAAA,CAAAnB,OAAA,CAAAvG,KAAA,CAAAiG,IAAA;UACA,KAAArD,QAAA,CAAAwE,KAAA,IAAAjD,MAAA,CAAAqD,YAAA;UACA;QACA;;QAEA;QACA,KAAAjB,OAAA,CAAAtG,MAAA,IAAAsG,OAAA,CAAAtG,MAAA,CAAAgG,IAAA;UACA,KAAArD,QAAA,CAAAwE,KAAA,sBAAAjD,MAAA,CAAAqD,YAAA;UACA;QACA;QAEA,IAAAvH,MAAA,GAAAsG,OAAA,CAAAtG,MAAA,CAAAgG,IAAA,GAAAU,WAAA;;QAEA;QACA,IAAA1G,MAAA,CAAAuC,MAAA;UACA,IAAAmF,SAAA;UACA,KAAAA,SAAA,CAAAD,IAAA,CAAAzH,MAAA;YACA,KAAA2C,QAAA,CAAAwE,KAAA,IAAAjD,MAAA,CAAAqD,YAAA;YACA;UACA;QACA;;QAEA;QACA,SAAAI,CAAA,MAAAA,CAAA,QAAA9H,QAAA,CAAAC,QAAA,CAAAyC,MAAA,EAAAoF,CAAA;UACA,IAAAL,CAAA,KAAAK,CAAA,IAAArB,OAAA,CAAAtG,MAAA,CAAAgG,IAAA,GAAAU,WAAA,YAAA7G,QAAA,CAAAC,QAAA,CAAA6H,CAAA,EAAA3H,MAAA,CAAAgG,IAAA,GAAAU,WAAA;YACA,KAAA/D,QAAA,CAAAwE,KAAA,IAAAjD,MAAA,CAAAqD,YAAA,wBAAArD,MAAA,CAAAyD,CAAA,kBAAAA,CAAA;YACA;UACA;QACA;;QAEA;QACA,SAAAA,EAAA,MAAAA,EAAA,QAAA9H,QAAA,CAAAC,QAAA,CAAAyC,MAAA,EAAAoF,EAAA;UACA,IAAAL,CAAA,KAAAK,EAAA,IAAArB,OAAA,CAAAvG,KAAA,CAAAiG,IAAA,YAAAnG,QAAA,CAAAC,QAAA,CAAA6H,EAAA,EAAA5H,KAAA,CAAAiG,IAAA;YACA,KAAArD,QAAA,CAAAwE,KAAA,IAAAjD,MAAA,CAAAqD,YAAA,wBAAArD,MAAA,CAAAyD,EAAA,kBAAAA,EAAA;YACA;UACA;QACA;;QAEA;QACArB,OAAA,CAAA7G,IAAA,GAAA6G,OAAA,CAAA7G,IAAA,CAAAuG,IAAA;QACAM,OAAA,CAAAvG,KAAA,GAAAuG,OAAA,CAAAvG,KAAA,CAAAiG,IAAA;QACAM,OAAA,CAAAtG,MAAA,GAAAsG,OAAA,CAAAtG,MAAA,CAAAgG,IAAA,GAAAU,WAAA;QACAJ,OAAA,CAAArG,OAAA,IAAAqG,OAAA,CAAArG,OAAA,QAAA+F,IAAA;MACA;MAEA;IACA;IAEA;IACAH,aAAA,WAAAA,cAAA;MACA,UAAAhG,QAAA,CAAAM,SAAA,CAAAK,oBAAA;QACA,KAAAmC,QAAA,CAAAwE,KAAA;QACA;MACA;MAEA,UAAAtH,QAAA,CAAAM,SAAA,CAAAM,mBAAA;QACA,KAAAkC,QAAA,CAAAwE,KAAA;QACA;MACA;MAEA,IAAAS,SAAA,OAAApG,IAAA,MAAA3B,QAAA,CAAAM,SAAA,CAAAK,oBAAA;MACA,IAAAqH,QAAA,OAAArG,IAAA,MAAA3B,QAAA,CAAAM,SAAA,CAAAM,mBAAA;MACA,IAAAyB,GAAA,OAAAV,IAAA;;MAEA;MACA,IAAAS,aAAA,OAAAT,IAAA,CAAAU,GAAA,CAAAP,OAAA;MACA,IAAAiG,SAAA,GAAA3F,aAAA;QACA,KAAAU,QAAA,CAAAwE,KAAA;QACA;MACA;;MAEA;MACA,IAAAU,QAAA,IAAAD,SAAA;QACA,KAAAjF,QAAA,CAAAwE,KAAA;QACA;MACA;;MAEA;MACA,IAAAW,aAAA,GAAAD,QAAA,CAAAlG,OAAA,KAAAiG,SAAA,CAAAjG,OAAA;MACA,IAAAoG,WAAA;MACA,IAAAD,aAAA,GAAAC,WAAA;QACA,KAAApF,QAAA,CAAAwE,KAAA;QACA;MACA;MAEA;IACA;IAEA;IACAa,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAC,IAAA;QACA;QACAL,MAAA,CAAArI,qBAAA;QAEAqI,MAAA,CAAApI,QAAA;UACAC,QAAA,GACA;YACAL,IAAA;YACAM,KAAA;YACAC,MAAA;YACAC,OAAA;YACAC,aAAA;UACA,EACA;UACAC,SAAA;YACAC,cAAA;YACAC,gBAAA;YACAC,iBAAA;YACAC,kBAAA;YACAC,oBAAA;YACAC,mBAAA;UACA;QACA;QAEAwH,MAAA,CAAAhH,cAAA;QAEAgH,MAAA,CAAAtF,QAAA,CAAAG,OAAA;MACA;IACA;IAEA;IACAyF,eAAA,WAAAA,gBAAA;MACA,KAAA5F,QAAA,CAAA6F,IAAA;IACA;IAEA;IACAC,SAAA,WAAAA,UAAAzG,IAAA;MACA,KAAAA,IAAA;MACA,IAAAP,IAAA,OAAAD,IAAA,CAAAQ,IAAA;MACA,IAAAoB,KAAA,CAAA3B,IAAA,CAAAE,OAAA;MAEA,IAAA0B,IAAA,GAAA5B,IAAA,CAAA6B,WAAA;MACA,IAAAC,KAAA,GAAAC,MAAA,CAAA/B,IAAA,CAAAgC,QAAA,QAAAC,QAAA;MACA,IAAAC,GAAA,GAAAH,MAAA,CAAA/B,IAAA,CAAAI,OAAA,IAAA6B,QAAA;MACA,IAAAE,KAAA,GAAAJ,MAAA,CAAA/B,IAAA,CAAAoC,QAAA,IAAAH,QAAA;MACA,IAAAI,OAAA,GAAAN,MAAA,CAAA/B,IAAA,CAAAsC,UAAA,IAAAL,QAAA;MAEA,UAAAQ,MAAA,CAAAb,IAAA,OAAAa,MAAA,CAAAX,KAAA,OAAAW,MAAA,CAAAP,GAAA,OAAAO,MAAA,CAAAN,KAAA,OAAAM,MAAA,CAAAJ,OAAA;IACA;EACA;AACA", "ignoreList": []}]}