{"remainingRequest": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\api\\archives\\material.js", "dependencies": [{"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\api\\archives\\material.js", "mtime": 1750833543328}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\babel.config.js", "mtime": 1750833543308}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750836929039}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750836940534}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750836931805}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9wcm9qZWN0cy9nYW95aS1wbGF0L3J1b3lpLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdC5qcyIpLmRlZmF1bHQ7Ck9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5hZGQgPSBhZGQ7CmV4cG9ydHMuZGVsID0gZGVsOwpleHBvcnRzLmdldERldGFpbCA9IGdldERldGFpbDsKZXhwb3J0cy5saXN0ID0gbGlzdDsKZXhwb3J0cy5saXN0QnlDbGFzc2lkID0gbGlzdEJ5Q2xhc3NpZDsKZXhwb3J0cy50cmVlU2VsZWN0ID0gdHJlZVNlbGVjdDsKZXhwb3J0cy51cGRhdGUgPSB1cGRhdGU7CnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwovLyDmn6Xor6LnianmlpnliJfooagKZnVuY3Rpb24gbGlzdChxdWVyeVBhcmFtKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICdhcmNoaXZlcy9tYXRlcmlhbC9saXN0JywKICAgIG1ldGhvZDogJ2dldCcsCiAgICBwYXJhbXM6IHF1ZXJ5UGFyYW0gLy/lnKhVUkzlkI7pnaLmi7zmjqUKICB9KTsKfQpmdW5jdGlvbiBnZXREZXRhaWwobWF0ZXJpYWxJZCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnYXJjaGl2ZXMvbWF0ZXJpYWwvJyArIG1hdGVyaWFsSWQsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn0KZnVuY3Rpb24gYWRkKGRhdGEpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJ2FyY2hpdmVzL21hdGVyaWFsJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YSAvL+WcqGJvZHnkuK0KICB9KTsKfQpmdW5jdGlvbiB1cGRhdGUoZGF0YSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnYXJjaGl2ZXMvbWF0ZXJpYWwnLAogICAgbWV0aG9kOiAncHV0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQpmdW5jdGlvbiBkZWwobWF0ZXJpYWxJZCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnYXJjaGl2ZXMvbWF0ZXJpYWwvJyArIG1hdGVyaWFsSWQsCiAgICBtZXRob2Q6ICdkZWxldGUnCiAgfSk7Cn0KZnVuY3Rpb24gdHJlZVNlbGVjdChkYXRhKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICdhcmNoaXZlcy9tYXRlcmlhbC90cmVlJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CmZ1bmN0aW9uIGxpc3RCeUNsYXNzaWQoY2xhc3NpZCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnYXJjaGl2ZXMvbWF0ZXJpYWwvbGlzdGJ5Y2xhc3NpZC8nICsgY2xhc3NpZCwKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQ=="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "list", "queryParam", "request", "url", "method", "params", "getDetail", "materialId", "add", "data", "update", "del", "treeSelect", "listByClassid", "classid"], "sources": ["D:/projects/gaoyi-plat/ruoyi-ui/src/api/archives/material.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询物料列表\r\nexport function list(queryParam) {\r\n  return request({\r\n    url: 'archives/material/list',\r\n    method: 'get',\r\n    params: queryParam   //在URL后面拼接\r\n  })\r\n}\r\n\r\nexport function getDetail(materialId) {\r\n  return request({\r\n    url: 'archives/material/' + materialId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\nexport function add(data) {\r\n  return request({\r\n    url: 'archives/material',\r\n    method: 'post',\r\n    data: data   //在body中\r\n  })\r\n}\r\n\r\nexport function update(data) {\r\n  return request({\r\n    url: 'archives/material',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\nexport function del(materialId) {\r\n  return request({\r\n    url: 'archives/material/' + materialId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\nexport function treeSelect(data) {\r\n  return request({\r\n    url: 'archives/material/tree',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\nexport function listByClassid(classid) {\r\n  return request({\r\n    url: 'archives/material/listbyclassid/' + classid,\r\n    method: 'get',\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,IAAIA,CAACC,UAAU,EAAE;EAC/B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ,UAAU,CAAG;EACvB,CAAC,CAAC;AACJ;AAEO,SAASK,SAASA,CAACC,UAAU,EAAE;EACpC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGI,UAAU;IACtCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEO,SAASI,GAAGA,CAACC,IAAI,EAAE;EACxB,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA,IAAI,CAAG;EACf,CAAC,CAAC;AACJ;AAEO,SAASC,MAAMA,CAACD,IAAI,EAAE;EAC3B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;AAEO,SAASE,GAAGA,CAACJ,UAAU,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGI,UAAU;IACtCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEO,SAASQ,UAAUA,CAACH,IAAI,EAAE;EAC/B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;AAEO,SAASI,aAAaA,CAACC,OAAO,EAAE;EACrC,OAAO,IAAAZ,gBAAO,EAAC;IACbC,GAAG,EAAE,kCAAkC,GAAGW,OAAO;IACjDV,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}