{"remainingRequest": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\components\\Screenfull\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\components\\Screenfull\\index.vue", "mtime": 1750833543374}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\babel.config.js", "mtime": 1750833543308}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750836929039}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750836940534}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750836929039}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750836957095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9wcm9qZWN0cy9nYW95aS1wbGF0L3J1b3lpLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdC5qcyIpLmRlZmF1bHQ7Ck9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5kZWZhdWx0ID0gdm9pZCAwOwp2YXIgX3NjcmVlbmZ1bGwgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoInNjcmVlbmZ1bGwiKSk7Ci8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAnU2NyZWVuZnVsbCcsCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGlzRnVsbHNjcmVlbjogZmFsc2UKICAgIH07CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgdGhpcy5pbml0KCk7CiAgfSwKICBiZWZvcmVEZXN0cm95OiBmdW5jdGlvbiBiZWZvcmVEZXN0cm95KCkgewogICAgdGhpcy5kZXN0cm95KCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICBjbGljazogZnVuY3Rpb24gY2xpY2soKSB7CiAgICAgIGlmICghX3NjcmVlbmZ1bGwuZGVmYXVsdC5pc0VuYWJsZWQpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgIG1lc3NhZ2U6ICfkvaDnmoTmtY/op4jlmajkuI3mlK/mjIHlhajlsY8nLAogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgICAgfSk7CiAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICB9CiAgICAgIF9zY3JlZW5mdWxsLmRlZmF1bHQudG9nZ2xlKCk7CiAgICB9LAogICAgY2hhbmdlOiBmdW5jdGlvbiBjaGFuZ2UoKSB7CiAgICAgIHRoaXMuaXNGdWxsc2NyZWVuID0gX3NjcmVlbmZ1bGwuZGVmYXVsdC5pc0Z1bGxzY3JlZW47CiAgICB9LAogICAgaW5pdDogZnVuY3Rpb24gaW5pdCgpIHsKICAgICAgaWYgKF9zY3JlZW5mdWxsLmRlZmF1bHQuaXNFbmFibGVkKSB7CiAgICAgICAgX3NjcmVlbmZ1bGwuZGVmYXVsdC5vbignY2hhbmdlJywgdGhpcy5jaGFuZ2UpOwogICAgICB9CiAgICB9LAogICAgZGVzdHJveTogZnVuY3Rpb24gZGVzdHJveSgpIHsKICAgICAgaWYgKF9zY3JlZW5mdWxsLmRlZmF1bHQuaXNFbmFibGVkKSB7CiAgICAgICAgX3NjcmVlbmZ1bGwuZGVmYXVsdC5vZmYoJ2NoYW5nZScsIHRoaXMuY2hhbmdlKTsKICAgICAgfQogICAgfQogIH0KfTs="}, {"version": 3, "names": ["_screenfull", "_interopRequireDefault", "require", "name", "data", "isFullscreen", "mounted", "init", "<PERSON><PERSON><PERSON><PERSON>", "destroy", "methods", "click", "screenfull", "isEnabled", "$message", "message", "type", "toggle", "change", "on", "off"], "sources": ["src/components/Screenfull/index.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <svg-icon :icon-class=\"isFullscreen?'exit-fullscreen':'fullscreen'\" @click=\"click\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport screenfull from 'screenfull'\r\n\r\nexport default {\r\n  name: 'Screenfull',\r\n  data() {\r\n    return {\r\n      isFullscreen: false\r\n    }\r\n  },\r\n  mounted() {\r\n    this.init()\r\n  },\r\n  beforeDestroy() {\r\n    this.destroy()\r\n  },\r\n  methods: {\r\n    click() {\r\n      if (!screenfull.isEnabled) {\r\n        this.$message({ message: '你的浏览器不支持全屏', type: 'warning' })\r\n        return false\r\n      }\r\n      screenfull.toggle()\r\n    },\r\n    change() {\r\n      this.isFullscreen = screenfull.isFullscreen\r\n    },\r\n    init() {\r\n      if (screenfull.isEnabled) {\r\n        screenfull.on('change', this.change)\r\n      }\r\n    },\r\n    destroy() {\r\n      if (screenfull.isEnabled) {\r\n        screenfull.off('change', this.change)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.screenfull-svg {\r\n  display: inline-block;\r\n  cursor: pointer;\r\n  fill: #5a5e66;;\r\n  width: 20px;\r\n  height: 20px;\r\n  vertical-align: 10px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;AAOA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,YAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAC,KAAA,WAAAA,MAAA;MACA,KAAAC,mBAAA,CAAAC,SAAA;QACA,KAAAC,QAAA;UAAAC,OAAA;UAAAC,IAAA;QAAA;QACA;MACA;MACAJ,mBAAA,CAAAK,MAAA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAb,YAAA,GAAAO,mBAAA,CAAAP,YAAA;IACA;IACAE,IAAA,WAAAA,KAAA;MACA,IAAAK,mBAAA,CAAAC,SAAA;QACAD,mBAAA,CAAAO,EAAA,gBAAAD,MAAA;MACA;IACA;IACAT,OAAA,WAAAA,QAAA;MACA,IAAAG,mBAAA,CAAAC,SAAA;QACAD,mBAAA,CAAAQ,GAAA,gBAAAF,MAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}