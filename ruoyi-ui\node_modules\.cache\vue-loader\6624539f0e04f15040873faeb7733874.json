{"remainingRequest": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\views\\asc\\visitor\\self-register.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\views\\asc\\visitor\\self-register.vue", "mtime": 1750985468795}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750836929039}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750836940534}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750836929039}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750836957095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBzdWJtaXRWaXNpdG9yUmVnaXN0cmF0aW9uIH0gZnJvbSAiQC9hcGkvYXNjL3Zpc2l0b3IiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJTZWxmUmVnaXN0ZXIiLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBjdXJyZW50U3RlcDogMCwNCiAgICAgIHZlcmlmeWluZzogZmFsc2UsDQogICAgICBzdWJtaXR0aW5nOiBmYWxzZSwNCiAgICAgIA0KICAgICAgLy8g6aqM6K+B5pa55byPDQogICAgICB2ZXJpZnlNZXRob2Q6ICcnLA0KICAgICAgc2hvd01hbnVhbElucHV0OiBmYWxzZSwNCiAgICAgIHNob3dJZENhcmRSZWFkZXI6IGZhbHNlLA0KICAgICAgc2hvd0ZhY2VSZWNvZ25pdGlvbjogZmFsc2UsDQogICAgICANCiAgICAgIC8vIOi6q+S7veS/oeaBrw0KICAgICAgaWRlbnRpdHlGb3JtOiB7DQogICAgICAgIG5hbWU6ICcnLA0KICAgICAgICBwaG9uZTogJycsDQogICAgICAgIGlkVHlwZTogJycsDQogICAgICAgIGlkTnVtYmVyOiAnJywNCiAgICAgICAgY29tcGFueTogJycNCiAgICAgIH0sDQogICAgICBpZGVudGl0eVJ1bGVzOiB7DQogICAgICAgIG5hbWU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5aeT5ZCNJywgdHJpZ2dlcjogJ2JsdXInIH0sDQogICAgICAgICAgeyBtaW46IDIsIG1heDogMjAsIG1lc3NhZ2U6ICflp5PlkI3plb/luqblupTlnKgyLTIw5Liq5a2X56ym5LmL6Ze0JywgdHJpZ2dlcjogJ2JsdXInIH0sDQogICAgICAgICAgeyBwYXR0ZXJuOiAvXltcdTRlMDAtXHU5ZmE1YS16QS1awrdcc10rJC8sIG1lc3NhZ2U6ICflp5PlkI3lj6rog73ljIXlkKvkuK3mlofjgIHoi7HmloflrZfmr43lkozluLjop4HnrKblj7cnLCB0cmlnZ2VyOiAnYmx1cicgfQ0KICAgICAgICBdLA0KICAgICAgICBwaG9uZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXmiYvmnLrlj7cnLCB0cmlnZ2VyOiAnYmx1cicgfSwNCiAgICAgICAgICB7IHBhdHRlcm46IC9eMVszLTldXGR7OX0kLywgbWVzc2FnZTogJ+ivt+i+k+WFpeato+ehrueahOaJi+acuuWPtycsIHRyaWdnZXI6ICdibHVyJyB9DQogICAgICAgIF0sDQogICAgICAgIGlkVHlwZTogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6nor4Hku7bnsbvlnosnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9XSwNCiAgICAgICAgaWROdW1iZXI6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl6K+B5Lu25Y+356CBJywgdHJpZ2dlcjogJ2JsdXInIH0sDQogICAgICAgICAgeyBtaW46IDE1LCBtYXg6IDE4LCBtZXNzYWdlOiAn6K+B5Lu25Y+356CB6ZW/5bqm5LiN5q2j56GuJywgdHJpZ2dlcjogJ2JsdXInIH0NCiAgICAgICAgXSwNCiAgICAgICAgY29tcGFueTogW3sgbWF4OiAxMDAsIG1lc3NhZ2U6ICflhazlj7jlkI3np7DkuI3og73otoXov4cxMDDkuKrlrZfnrKYnLCB0cmlnZ2VyOiAnYmx1cicgfV0NCiAgICAgIH0sDQogICAgICANCiAgICAgIC8vIOiuv+mXruS/oeaBrw0KICAgICAgdmlzaXRGb3JtOiB7DQogICAgICAgIHJlYXNvbkZvclZpc2l0OiAnJywNCiAgICAgICAgaG9zdEVtcGxveWVlTmFtZTogJycsDQogICAgICAgIGRlcGFydG1lbnRWaXNpdGVkOiAnJywNCiAgICAgICAgdmVoaWNsZVBsYXRlTnVtYmVyOiAnJywNCiAgICAgICAgcGxhbm5lZEVudHJ5RGF0ZXRpbWU6IG51bGwsDQogICAgICAgIHBsYW5uZWRFeGl0RGF0ZXRpbWU6IG51bGwNCiAgICAgIH0sDQogICAgICB2aXNpdFJ1bGVzOiB7DQogICAgICAgIHJlYXNvbkZvclZpc2l0OiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeadpeiuv+S6i+eUsScsIHRyaWdnZXI6ICdibHVyJyB9LA0KICAgICAgICAgIHsgbWluOiAyLCBtYXg6IDIwMCwgbWVzc2FnZTogJ+adpeiuv+S6i+eUsemVv+W6puW6lOWcqDItMjAw5Liq5a2X56ym5LmL6Ze0JywgdHJpZ2dlcjogJ2JsdXInIH0NCiAgICAgICAgXSwNCiAgICAgICAgaG9zdEVtcGxveWVlTmFtZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXooqvorr/kurrlp5PlkI0nLCB0cmlnZ2VyOiAnYmx1cicgfSwNCiAgICAgICAgICB7IG1pbjogMiwgbWF4OiAyMCwgbWVzc2FnZTogJ+iiq+iuv+S6uuWnk+WQjemVv+W6puW6lOWcqDItMjDkuKrlrZfnrKbkuYvpl7QnLCB0cmlnZ2VyOiAnYmx1cicgfQ0KICAgICAgICBdLA0KICAgICAgICBkZXBhcnRtZW50VmlzaXRlZDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXooqvorr/pg6jpl6gnLCB0cmlnZ2VyOiAnYmx1cicgfSwNCiAgICAgICAgICB7IG1pbjogMiwgbWF4OiA1MCwgbWVzc2FnZTogJ+mDqOmXqOWQjeensOmVv+W6puW6lOWcqDItNTDkuKrlrZfnrKbkuYvpl7QnLCB0cmlnZ2VyOiAnYmx1cicgfQ0KICAgICAgICBdLA0KICAgICAgICBwbGFubmVkRW50cnlEYXRldGltZTogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6npooTorqHliLDorr/ml7bpl7QnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9XSwNCiAgICAgICAgcGxhbm5lZEV4aXREYXRldGltZTogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6npooTorqHnprvlvIDml7bpl7QnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9XQ0KICAgICAgfSwNCiAgICAgIA0KICAgICAgLy8g5ZCM6KGM5Lq65ZGYDQogICAgICBjb21wYW5pb25MaXN0OiBbXSwNCiAgICAgIA0KICAgICAgLy8g55m76K6w57uT5p6cDQogICAgICByZWdpc3RyYXRpb25JZDogbnVsbCwNCg0KICAgICAgLy8g5pe26Ze06YCJ5oup5Zmo6YWN572uDQogICAgICBlbnRyeVBpY2tlck9wdGlvbnM6IHsNCiAgICAgICAgc2hvcnRjdXRzOiBbew0KICAgICAgICAgIHRleHQ6ICfnjrDlnKgnLA0KICAgICAgICAgIG9uQ2xpY2socGlja2VyKSB7DQogICAgICAgICAgICBwaWNrZXIuJGVtaXQoJ3BpY2snLCBuZXcgRGF0ZSgpKTsNCiAgICAgICAgICB9DQogICAgICAgIH0sIHsNCiAgICAgICAgICB0ZXh0OiAnMeWwj+aXtuWQjicsDQogICAgICAgICAgb25DbGljayhwaWNrZXIpIHsNCiAgICAgICAgICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZSgpOw0KICAgICAgICAgICAgZGF0ZS5zZXRUaW1lKGRhdGUuZ2V0VGltZSgpICsgMzYwMCAqIDEwMDApOw0KICAgICAgICAgICAgcGlja2VyLiRlbWl0KCdwaWNrJywgZGF0ZSk7DQogICAgICAgICAgfQ0KICAgICAgICB9LCB7DQogICAgICAgICAgdGV4dDogJ+aYjuWkqeS4iuWNiDnngrknLA0KICAgICAgICAgIG9uQ2xpY2socGlja2VyKSB7DQogICAgICAgICAgICBjb25zdCBkYXRlID0gbmV3IERhdGUoKTsNCiAgICAgICAgICAgIGRhdGUuc2V0RGF0ZShkYXRlLmdldERhdGUoKSArIDEpOw0KICAgICAgICAgICAgZGF0ZS5zZXRIb3Vycyg5LCAwLCAwLCAwKTsNCiAgICAgICAgICAgIHBpY2tlci4kZW1pdCgncGljaycsIGRhdGUpOw0KICAgICAgICAgIH0NCiAgICAgICAgfV0sDQogICAgICAgIGRpc2FibGVkRGF0ZSh0aW1lKSB7DQogICAgICAgICAgLy8g5YWB6K646YCJ5oup5b2T5YmN5pe26Ze05YmNMeWwj+aXtuWIsOacquadpTMw5aSpDQogICAgICAgICAgY29uc3Qgb25lSG91ckJlZm9yZSA9IERhdGUubm93KCkgLSAzNjAwICogMTAwMDsNCiAgICAgICAgICBjb25zdCB0aGlydHlEYXlzTGF0ZXIgPSBEYXRlLm5vdygpICsgMzAgKiAyNCAqIDM2MDAgKiAxMDAwOw0KICAgICAgICAgIHJldHVybiB0aW1lLmdldFRpbWUoKSA8IG9uZUhvdXJCZWZvcmUgfHwgdGltZS5nZXRUaW1lKCkgPiB0aGlydHlEYXlzTGF0ZXI7DQogICAgICAgIH0NCiAgICAgIH0sDQogICAgICANCiAgICAgIGV4aXRQaWNrZXJPcHRpb25zOiB7DQogICAgICAgIHNob3J0Y3V0czogW3sNCiAgICAgICAgICB0ZXh0OiAnMuWwj+aXtuWQjicsDQogICAgICAgICAgb25DbGljayhwaWNrZXIpIHsNCiAgICAgICAgICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZSgpOw0KICAgICAgICAgICAgZGF0ZS5zZXRUaW1lKGRhdGUuZ2V0VGltZSgpICsgMiAqIDM2MDAgKiAxMDAwKTsNCiAgICAgICAgICAgIHBpY2tlci4kZW1pdCgncGljaycsIGRhdGUpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSwgew0KICAgICAgICAgIHRleHQ6ICfku4rlpKnkuIvljYg254K5JywNCiAgICAgICAgICBvbkNsaWNrKHBpY2tlcikgew0KICAgICAgICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKCk7DQogICAgICAgICAgICBkYXRlLnNldEhvdXJzKDE4LCAwLCAwLCAwKTsNCiAgICAgICAgICAgIGlmIChkYXRlLmdldFRpbWUoKSA8IERhdGUubm93KCkpIHsNCiAgICAgICAgICAgICAgZGF0ZS5zZXREYXRlKGRhdGUuZ2V0RGF0ZSgpICsgMSk7DQogICAgICAgICAgICB9DQogICAgICAgICAgICBwaWNrZXIuJGVtaXQoJ3BpY2snLCBkYXRlKTsNCiAgICAgICAgICB9DQogICAgICAgIH0sIHsNCiAgICAgICAgICB0ZXh0OiAn5piO5aSp5LiL5Y2INueCuScsDQogICAgICAgICAgb25DbGljayhwaWNrZXIpIHsNCiAgICAgICAgICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZSgpOw0KICAgICAgICAgICAgZGF0ZS5zZXREYXRlKGRhdGUuZ2V0RGF0ZSgpICsgMSk7DQogICAgICAgICAgICBkYXRlLnNldEhvdXJzKDE4LCAwLCAwLCAwKTsNCiAgICAgICAgICAgIHBpY2tlci4kZW1pdCgncGljaycsIGRhdGUpOw0KICAgICAgICAgIH0NCiAgICAgICAgfV0sDQogICAgICAgIGRpc2FibGVkRGF0ZSh0aW1lKSB7DQogICAgICAgICAgLy8g5YWB6K646YCJ5oup5b2T5YmN5pe26Ze05Yiw5pyq5p2lMzDlpKkNCiAgICAgICAgICBjb25zdCB0aGlydHlEYXlzTGF0ZXIgPSBEYXRlLm5vdygpICsgMzAgKiAyNCAqIDM2MDAgKiAxMDAwOw0KICAgICAgICAgIHJldHVybiB0aW1lLmdldFRpbWUoKSA8IERhdGUubm93KCkgLSA4LjY0ZTcgfHwgdGltZS5nZXRUaW1lKCkgPiB0aGlydHlEYXlzTGF0ZXI7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9Ow0KICB9LA0KICANCiAgY29tcHV0ZWQ6IHsNCiAgICBwcm9ncmVzc1dpZHRoKCkgew0KICAgICAgcmV0dXJuICgodGhpcy5jdXJyZW50U3RlcCArIDEpIC8gMykgKiAxMDAgKyAnJSc7DQogICAgfSwNCiAgICANCiAgICBzaG93VmVyaWZ5T3BlcmF0aW9uKCkgew0KICAgICAgcmV0dXJuIHRoaXMuc2hvd01hbnVhbElucHV0IHx8IHRoaXMuc2hvd0lkQ2FyZFJlYWRlciB8fCB0aGlzLnNob3dGYWNlUmVjb2duaXRpb247DQogICAgfSwNCg0KICAgIC8vIOaAu+iuv+WuouS6uuaVsO+8iOS4u+iuv+WuoiArIOWQjOihjOS6uuWRmO+8iQ0KICAgIHRvdGFsVmlzaXRvcnMoKSB7DQogICAgICByZXR1cm4gMSArIHRoaXMuY29tcGFuaW9uTGlzdC5sZW5ndGg7DQogICAgfQ0KICB9LA0KICANCiAgbWV0aG9kczogew0KICAgIC8qKiDpgInmi6npqozor4HmlrnlvI8gKi8NCiAgICBzZWxlY3RWZXJpZnlNZXRob2QobWV0aG9kKSB7DQogICAgICB0aGlzLnZlcmlmeU1ldGhvZCA9IG1ldGhvZDsNCiAgICAgIHRoaXMucmVzZXRWZXJpZnlEaXNwbGF5KCk7DQogICAgICANCiAgICAgIHN3aXRjaCAobWV0aG9kKSB7DQogICAgICAgIGNhc2UgJ21hbnVhbCc6DQogICAgICAgICAgdGhpcy5zaG93TWFudWFsSW5wdXQgPSB0cnVlOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlICdpZF9jYXJkJzoNCiAgICAgICAgICB0aGlzLnNob3dJZENhcmRSZWFkZXIgPSB0cnVlOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlICdmYWNlJzoNCiAgICAgICAgICB0aGlzLnNob3dGYWNlUmVjb2duaXRpb24gPSB0cnVlOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlICdwYXNzcG9ydCc6DQogICAgICAgICAgdGhpcy5zaG93TWFudWFsSW5wdXQgPSB0cnVlOw0KICAgICAgICAgIHRoaXMuaWRlbnRpdHlGb3JtLmlkVHlwZSA9ICdwYXNzcG9ydCc7DQogICAgICAgICAgYnJlYWs7DQogICAgICB9DQogICAgfSwNCiAgICANCiAgICAvKiog6YeN572u6aqM6K+B5pi+56S6ICovDQogICAgcmVzZXRWZXJpZnlEaXNwbGF5KCkgew0KICAgICAgdGhpcy5zaG93TWFudWFsSW5wdXQgPSBmYWxzZTsNCiAgICAgIHRoaXMuc2hvd0lkQ2FyZFJlYWRlciA9IGZhbHNlOw0KICAgICAgdGhpcy5zaG93RmFjZVJlY29nbml0aW9uID0gZmFsc2U7DQogICAgfSwNCiAgICANCiAgICAvKiog6L+U5Zue6aqM6K+B5pa55byP6YCJ5oupICovDQogICAgYmFja1RvVmVyaWZ5TWV0aG9kKCkgew0KICAgICAgdGhpcy5yZXNldFZlcmlmeURpc3BsYXkoKTsNCiAgICAgIHRoaXMudmVyaWZ5TWV0aG9kID0gJyc7DQogICAgfSwNCg0KICAgIC8qKiDlpITnkIbpqozor4HmlrnlvI/kuIvkuIDmraUgKi8NCiAgICBoYW5kbGVWZXJpZnlOZXh0KCkgew0KICAgICAgaWYgKHRoaXMudmVyaWZ5TWV0aG9kID09PSAnbWFudWFsJykgew0KICAgICAgICB0aGlzLnNob3dNYW51YWxJbnB1dCA9IHRydWU7DQogICAgICB9IGVsc2UgaWYgKHRoaXMudmVyaWZ5TWV0aG9kID09PSAnaWRfY2FyZCcpIHsNCiAgICAgICAgdGhpcy5zaG93SWRDYXJkUmVhZGVyID0gdHJ1ZTsNCiAgICAgIH0gZWxzZSBpZiAodGhpcy52ZXJpZnlNZXRob2QgPT09ICdmYWNlJykgew0KICAgICAgICB0aGlzLnNob3dGYWNlUmVjb2duaXRpb24gPSB0cnVlOw0KICAgICAgfSBlbHNlIGlmICh0aGlzLnZlcmlmeU1ldGhvZCA9PT0gJ3Bhc3Nwb3J0Jykgew0KICAgICAgICB0aGlzLnNob3dNYW51YWxJbnB1dCA9IHRydWU7DQogICAgICAgIHRoaXMuaWRlbnRpdHlGb3JtLmlkVHlwZSA9ICdwYXNzcG9ydCc7DQogICAgICB9DQogICAgfSwNCiAgICANCiAgICAvKiog56Gu6K6k6Lqr5Lu9ICovDQogICAgY29uZmlybUlkZW50aXR5KCkgew0KICAgICAgdGhpcy4kcmVmcy5pZGVudGl0eUZvcm0udmFsaWRhdGUodmFsaWQgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICB0aGlzLnZlcmlmeWluZyA9IHRydWU7DQogICAgICAgICAgLy8g5qih5ouf6aqM6K+B6L+H56iLDQogICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgICAgICB0aGlzLnZlcmlmeWluZyA9IGZhbHNlOw0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfouqvku73pqozor4HmiJDlip8nKTsNCiAgICAgICAgICAgIHRoaXMubmV4dFN0ZXAoKTsNCiAgICAgICAgICB9LCAxMDAwKTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICANCiAgICAvKiog5qih5ouf6Lqr5Lu96K+B6K+75Y+WICovDQogICAgc2ltdWxhdGVJZENhcmRSZWFkKCkgew0KICAgICAgdGhpcy5pZGVudGl0eUZvcm0gPSB7DQogICAgICAgIG5hbWU6ICflvKDkuIknLA0KICAgICAgICBwaG9uZTogJzEzODAwMTM4MDAwJywNCiAgICAgICAgaWRUeXBlOiAnaWRfY2FyZCcsDQogICAgICAgIGlkTnVtYmVyOiAnMTEwMTAxMTk5MDAxMDExMjM0JywNCiAgICAgICAgY29tcGFueTogJ+afkOafkOenkeaKgOaciemZkOWFrOWPuCcNCiAgICAgIH07DQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+i6q+S7veivgeS/oeaBr+ivu+WPluaIkOWKnycpOw0KICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgIHRoaXMubmV4dFN0ZXAoKTsNCiAgICAgIH0sIDEwMDApOw0KICAgIH0sDQogICAgDQogICAgLyoqIOaooeaLn+S6uuiEuOivhuWIqyAqLw0KICAgIHNpbXVsYXRlRmFjZVJlY29nbml0aW9uKCkgew0KICAgICAgdGhpcy5pZGVudGl0eUZvcm0gPSB7DQogICAgICAgIG5hbWU6ICfmnY7lm5snLA0KICAgICAgICBwaG9uZTogJzEzOTAwMTM5MDAwJywNCiAgICAgICAgaWRUeXBlOiAnaWRfY2FyZCcsDQogICAgICAgIGlkTnVtYmVyOiAnMTEwMTAxMTk5MDAyMDIxMjM0JywNCiAgICAgICAgY29tcGFueTogJ+afkOafkOi0uOaYk+aciemZkOWFrOWPuCcNCiAgICAgIH07DQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+S6uuiEuOivhuWIq+aIkOWKnycpOw0KICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgIHRoaXMubmV4dFN0ZXAoKTsNCiAgICAgIH0sIDEwMDApOw0KICAgIH0sDQogICAgDQogICAgLyoqIOS4i+S4gOatpSAqLw0KICAgIG5leHRTdGVwKCkgew0KICAgICAgaWYgKHRoaXMuY3VycmVudFN0ZXAgPT09IDEpIHsNCiAgICAgICAgdGhpcy5zdWJtaXRSZWdpc3RyYXRpb24oKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuY3VycmVudFN0ZXArKzsNCiAgICAgIH0NCiAgICB9LA0KICAgIA0KICAgIC8qKiDkuIrkuIDmraUgKi8NCiAgICBwcmV2U3RlcCgpIHsNCiAgICAgIHRoaXMuY3VycmVudFN0ZXAtLTsNCiAgICAgIGlmICh0aGlzLmN1cnJlbnRTdGVwID09PSAwKSB7DQogICAgICAgIHRoaXMucmVzZXRWZXJpZnlEaXNwbGF5KCk7DQogICAgICB9DQogICAgfSwNCiAgICANCiAgICAvKiog5re75Yqg5ZCM6KGM5Lq65ZGYICovDQogICAgYWRkQ29tcGFuaW9uKCkgew0KICAgICAgaWYgKHRoaXMuY29tcGFuaW9uTGlzdC5sZW5ndGggPj0gOSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+acgOWkmuWPquiDvea3u+WKoDnlkI3lkIzooYzkurrlkZjvvIjmgLvorqExMOWQjeiuv+Wuou+8iScpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIHRoaXMuY29tcGFuaW9uTGlzdC5wdXNoKHsNCiAgICAgICAgbmFtZTogJycsDQogICAgICAgIHBob25lOiAnJywNCiAgICAgICAgaWROdW1iZXI6ICcnLA0KICAgICAgICBjb21wYW55OiAnJw0KICAgICAgfSk7DQogICAgICANCiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5bey5re75Yqg6K6/5a6iJyk7DQogICAgfSwNCiAgICANCiAgICAvKiog5p2l6K6/5pe26Ze05Y+Y5pu05LqL5Lu2ICovDQogICAgb25BcnJpdmFsVGltZUNoYW5nZSh2YWx1ZSkgew0KICAgICAgY29uc29sZS5sb2coJ+mihOiuoeadpeiuv+aXtumXtOWPmOabtDonLCB2YWx1ZSk7DQogICAgICAvLyDoh6rliqjorr7nva7nprvlvIDml7bpl7TkuLrmnaXorr/ml7bpl7TlkI405bCP5pe2DQogICAgICBpZiAodmFsdWUgJiYgIXRoaXMudmlzaXRGb3JtLnBsYW5uZWRFeGl0RGF0ZXRpbWUpIHsNCiAgICAgICAgY29uc3QgYXJyaXZhbFRpbWUgPSBuZXcgRGF0ZSh2YWx1ZSk7DQogICAgICAgIGNvbnN0IGRlcGFydHVyZVRpbWUgPSBuZXcgRGF0ZShhcnJpdmFsVGltZS5nZXRUaW1lKCkgKyA0ICogNjAgKiA2MCAqIDEwMDApOw0KICAgICAgICANCiAgICAgICAgY29uc3QgeWVhciA9IGRlcGFydHVyZVRpbWUuZ2V0RnVsbFllYXIoKTsNCiAgICAgICAgY29uc3QgbW9udGggPSBTdHJpbmcoZGVwYXJ0dXJlVGltZS5nZXRNb250aCgpICsgMSkucGFkU3RhcnQoMiwgJzAnKTsNCiAgICAgICAgY29uc3QgZGF5ID0gU3RyaW5nKGRlcGFydHVyZVRpbWUuZ2V0RGF0ZSgpKS5wYWRTdGFydCgyLCAnMCcpOw0KICAgICAgICBjb25zdCBob3VycyA9IFN0cmluZyhkZXBhcnR1cmVUaW1lLmdldEhvdXJzKCkpLnBhZFN0YXJ0KDIsICcwJyk7DQogICAgICAgIGNvbnN0IG1pbnV0ZXMgPSBTdHJpbmcoZGVwYXJ0dXJlVGltZS5nZXRNaW51dGVzKCkpLnBhZFN0YXJ0KDIsICcwJyk7DQogICAgICAgIGNvbnN0IHNlY29uZHMgPSBTdHJpbmcoZGVwYXJ0dXJlVGltZS5nZXRTZWNvbmRzKCkpLnBhZFN0YXJ0KDIsICcwJyk7DQogICAgICAgIA0KICAgICAgICB0aGlzLnZpc2l0Rm9ybS5wbGFubmVkRXhpdERhdGV0aW1lID0gYCR7eWVhcn0tJHttb250aH0tJHtkYXl9ICR7aG91cnN9OiR7bWludXRlc306JHtzZWNvbmRzfWA7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDnprvlvIDml7bpl7Tlj5jmm7Tkuovku7YgKi8NCiAgICBvbkRlcGFydHVyZVRpbWVDaGFuZ2UodmFsdWUpIHsNCiAgICAgIGNvbnNvbGUubG9nKCfpooTorqHnprvlvIDml7bpl7Tlj5jmm7Q6JywgdmFsdWUpOw0KICAgICAgLy8g6aqM6K+B56a75byA5pe26Ze05LiN6IO95pep5LqO5p2l6K6/5pe26Ze0DQogICAgICBpZiAodmFsdWUgJiYgdGhpcy52aXNpdEZvcm0ucGxhbm5lZEVudHJ5RGF0ZXRpbWUpIHsNCiAgICAgICAgY29uc3QgYXJyaXZhbFRpbWUgPSBuZXcgRGF0ZSh0aGlzLnZpc2l0Rm9ybS5wbGFubmVkRW50cnlEYXRldGltZSk7DQogICAgICAgIGNvbnN0IGRlcGFydHVyZVRpbWUgPSBuZXcgRGF0ZSh2YWx1ZSk7DQoNCiAgICAgICAgaWYgKGRlcGFydHVyZVRpbWUgPD0gYXJyaXZhbFRpbWUpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+mihOiuoeemu+W8gOaXtumXtOS4jeiDveaXqeS6juaIluetieS6juadpeiuv+aXtumXtCcpOw0KICAgICAgICAgIHRoaXMudmlzaXRGb3JtLnBsYW5uZWRFeGl0RGF0ZXRpbWUgPSAnJzsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgDQogICAgLyoqIOaPkOS6pOeZu+iusCAqLw0KICAgIHN1Ym1pdFJlZ2lzdHJhdGlvbigpIHsNCiAgICAgIHRoaXMuJHJlZnMudmlzaXRGb3JtLnZhbGlkYXRlKHZhbGlkID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgLy8g6aqM6K+B5ZCM6KGM5Lq65ZGY5L+h5oGvDQogICAgICAgICAgaWYgKCF0aGlzLnZhbGlkYXRlVmlzaXRvcnMoKSkgew0KICAgICAgICAgICAgcmV0dXJuOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC8vIOmqjOivgeaXtumXtA0KICAgICAgICAgIGlmICghdGhpcy52YWxpZGF0ZVRpbWVzKCkpIHsNCiAgICAgICAgICAgIHJldHVybjsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICB0aGlzLnN1Ym1pdHRpbmcgPSB0cnVlOw0KICAgICAgICAgIA0KICAgICAgICAgIC8vIOaehOW7uuaPkOS6pOaVsOaNriAtIOaMieeFp+W+ruS/oeerr+ebuOWQjOeahOaVsOaNrue7k+aehA0KICAgICAgICAgIGNvbnN0IHN1Ym1pdERhdGEgPSB7DQogICAgICAgICAgICAvLyBWaXNpdFJlZ2lzdHJhdGlvbnMg5a+56LGhDQogICAgICAgICAgICByZWdpc3RyYXRpb246IHsNCiAgICAgICAgICAgICAgcHJpbWFyeUNvbnRhY3ROYW1lOiB0aGlzLmlkZW50aXR5Rm9ybS5uYW1lLnRyaW0oKSwNCiAgICAgICAgICAgICAgcHJpbWFyeUNvbnRhY3RQaG9uZTogdGhpcy5pZGVudGl0eUZvcm0ucGhvbmUudHJpbSgpLA0KICAgICAgICAgICAgICByZWFzb25Gb3JWaXNpdDogdGhpcy52aXNpdEZvcm0ucmVhc29uRm9yVmlzaXQudHJpbSgpLA0KICAgICAgICAgICAgICBob3N0RW1wbG95ZWVOYW1lOiB0aGlzLnZpc2l0Rm9ybS5ob3N0RW1wbG95ZWVOYW1lLnRyaW0oKSwNCiAgICAgICAgICAgICAgaG9zdEVtcGxveWVlSWQ6IG51bGwsIC8vIOWJjeerr+aaguaXtuayoeacieiiq+iuv+S6uklE77yM5ZCO56uv5Y+v6IO95Lya6Ieq5Yqo5aSE55CGDQogICAgICAgICAgICAgIGRlcGFydG1lbnRWaXNpdGVkOiB0aGlzLnZpc2l0Rm9ybS5kZXBhcnRtZW50VmlzaXRlZC50cmltKCksDQogICAgICAgICAgICAgIHBsYW5uZWRFbnRyeURhdGV0aW1lOiB0aGlzLmZvcm1hdERhdGVGb3JCYWNrZW5kKHRoaXMudmlzaXRGb3JtLnBsYW5uZWRFbnRyeURhdGV0aW1lKSwNCiAgICAgICAgICAgICAgcGxhbm5lZEV4aXREYXRldGltZTogdGhpcy5mb3JtYXREYXRlRm9yQmFja2VuZCh0aGlzLnZpc2l0Rm9ybS5wbGFubmVkRXhpdERhdGV0aW1lKSwNCiAgICAgICAgICAgICAgdmVoaWNsZVBsYXRlTnVtYmVyOiB0aGlzLnZpc2l0Rm9ybS52ZWhpY2xlUGxhdGVOdW1iZXIgPyB0aGlzLnZpc2l0Rm9ybS52ZWhpY2xlUGxhdGVOdW1iZXIudHJpbSgpIDogJycsDQogICAgICAgICAgICAgIHRvdGFsQ29tcGFuaW9uczogdGhpcy5jb21wYW5pb25MaXN0Lmxlbmd0aA0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIC8vIFJlZ2lzdHJhdGlvbkF0dGVuZGVlcyDmlbDnu4QgLSDkvb/nlKjlkI7nq6/mnJ/mnJvnmoTkuLTml7blrZfmrrUNCiAgICAgICAgICAgIGF0dGVuZGVlc0xpc3Q6IFsNCiAgICAgICAgICAgICAgLy8g5Li76K6/5a6iDQogICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICB2aXNpdG9yTmFtZTogdGhpcy5pZGVudGl0eUZvcm0ubmFtZS50cmltKCksDQogICAgICAgICAgICAgICAgdmlzaXRvclBob25lOiB0aGlzLmlkZW50aXR5Rm9ybS5waG9uZS50cmltKCksDQogICAgICAgICAgICAgICAgdmlzaXRvcklkQ2FyZDogdGhpcy5pZGVudGl0eUZvcm0uaWROdW1iZXIudHJpbSgpLnRvVXBwZXJDYXNlKCksDQogICAgICAgICAgICAgICAgdmlzaXRvckNvbXBhbnk6IHRoaXMuaWRlbnRpdHlGb3JtLmNvbXBhbnkgPyB0aGlzLmlkZW50aXR5Rm9ybS5jb21wYW55LnRyaW0oKSA6ICcnLA0KICAgICAgICAgICAgICAgIGlzUHJpbWFyeTogIjEiLCAvLyDnrKzkuIDkuKrorr/lrqLlv4XpobvmmK/kuLvogZTns7vkuroNCiAgICAgICAgICAgICAgICB2aXNpdG9yQXZhdGFyUGhvdG86IG51bGwgLy8g5pqC5pe25LiN5pSv5oyB5aS05YOP5LiK5LygDQogICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgIC8vIOWQjOihjOS6uuWRmA0KICAgICAgICAgICAgICAuLi50aGlzLmNvbXBhbmlvbkxpc3QNCiAgICAgICAgICAgICAgICAuZmlsdGVyKGl0ZW0gPT4gaXRlbS5uYW1lICYmIGl0ZW0ucGhvbmUgJiYgaXRlbS5pZE51bWJlcikNCiAgICAgICAgICAgICAgICAubWFwKGl0ZW0gPT4gKHsNCiAgICAgICAgICAgICAgICAgIHZpc2l0b3JOYW1lOiBpdGVtLm5hbWUudHJpbSgpLA0KICAgICAgICAgICAgICAgICAgdmlzaXRvclBob25lOiBpdGVtLnBob25lLnRyaW0oKSwNCiAgICAgICAgICAgICAgICAgIHZpc2l0b3JJZENhcmQ6IGl0ZW0uaWROdW1iZXIudHJpbSgpLnRvVXBwZXJDYXNlKCksDQogICAgICAgICAgICAgICAgICB2aXNpdG9yQ29tcGFueTogaXRlbS5jb21wYW55ID8gaXRlbS5jb21wYW55LnRyaW0oKSA6ICcnLA0KICAgICAgICAgICAgICAgICAgaXNQcmltYXJ5OiAiMCIsDQogICAgICAgICAgICAgICAgICB2aXNpdG9yQXZhdGFyUGhvdG86IG51bGwNCiAgICAgICAgICAgICAgICB9KSkNCiAgICAgICAgICAgIF0NCiAgICAgICAgICB9Ow0KDQogICAgICAgICAgY29uc29sZS5sb2coJ+aPkOS6pOaVsOaNrue7k+aehDonLCBKU09OLnN0cmluZ2lmeShzdWJtaXREYXRhLCBudWxsLCAyKSk7DQogICAgICAgICAgDQogICAgICAgICAgc3VibWl0VmlzaXRvclJlZ2lzdHJhdGlvbihzdWJtaXREYXRhKQ0KICAgICAgICAgICAgLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICB0aGlzLnJlZ2lzdHJhdGlvbklkID0gcmVzcG9uc2UuZGF0YSB8fCAnVlInICsgRGF0ZS5ub3coKTsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGAke3RoaXMudG90YWxWaXNpdG9yc33lkI3orr/lrqLnmbvorrDmiJDlip/vvIFgKTsNCiAgICAgICAgICAgICAgdGhpcy5jdXJyZW50U3RlcCsrOw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIC5jYXRjaChlcnJvciA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoZXJyb3IubXNnIHx8ICfnmbvorrDlpLHotKXvvIzor7fph43or5UnKTsNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgICAuZmluYWxseSgoKSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuc3VibWl0dGluZyA9IGZhbHNlOw0KICAgICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvKiog6aqM6K+B6K6/5a6i5L+h5oGvICovDQogICAgdmFsaWRhdGVWaXNpdG9ycygpIHsNCiAgICAgIC8vIOmqjOivgeS4u+iuv+WuouS/oeaBr++8iOW3suWcqOi6q+S7vemqjOivgeatpemqpOS4remqjOivge+8jOi/memHjOWGjeasoeehruiupO+8iQ0KICAgICAgaWYgKCF0aGlzLmlkZW50aXR5Rm9ybS5uYW1lIHx8IHRoaXMuaWRlbnRpdHlGb3JtLm5hbWUudHJpbSgpLmxlbmd0aCA8IDIpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Li76K6/5a6i5aeT5ZCN5LiN6IO95Li656m65LiU6ZW/5bqm5LiN6IO95bCR5LqOMuS4quWtl+espicpOw0KICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICB9DQoNCiAgICAgIGlmICghdGhpcy5pZGVudGl0eUZvcm0ucGhvbmUgfHwgIS9eMVszLTldXGR7OX0kLy50ZXN0KHRoaXMuaWRlbnRpdHlGb3JtLnBob25lLnRyaW0oKSkpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Li76K6/5a6i5omL5py65Y+35qC85byP5LiN5q2j56GuJyk7DQogICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgIH0NCg0KICAgICAgaWYgKCF0aGlzLmlkZW50aXR5Rm9ybS5pZE51bWJlciB8fCB0aGlzLmlkZW50aXR5Rm9ybS5pZE51bWJlci50cmltKCkubGVuZ3RoIDwgMTUpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Li76K6/5a6i6Lqr5Lu96K+B5Y+35LiN6IO95Li656m6Jyk7DQogICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgIH0NCg0KICAgICAgLy8g6aqM6K+B5ZCM6KGM5Lq65ZGY5L+h5oGvDQogICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRoaXMuY29tcGFuaW9uTGlzdC5sZW5ndGg7IGkrKykgew0KICAgICAgICBjb25zdCB2aXNpdG9yID0gdGhpcy5jb21wYW5pb25MaXN0W2ldOw0KICAgICAgICBjb25zdCB2aXNpdG9yVGl0bGUgPSBg6K6/5a6iJHtpICsgMn1gOw0KDQogICAgICAgIC8vIOmqjOivgeWnk+WQjQ0KICAgICAgICBpZiAoIXZpc2l0b3IubmFtZSB8fCB2aXNpdG9yLm5hbWUudHJpbSgpID09PSAnJykgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoYOivt+i+k+WFpSR7dmlzaXRvclRpdGxlfeeahOWnk+WQjWApOw0KICAgICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgICAgfQ0KICAgICAgICANCiAgICAgICAgaWYgKHZpc2l0b3IubmFtZS50cmltKCkubGVuZ3RoIDwgMiB8fCB2aXNpdG9yLm5hbWUudHJpbSgpLmxlbmd0aCA+IDIwKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihgJHt2aXNpdG9yVGl0bGV955qE5aeT5ZCN6ZW/5bqm5bqU5ZyoMi0yMOS4quWtl+espuS5i+mXtGApOw0KICAgICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOmqjOivgeWnk+WQjeagvOW8jw0KICAgICAgICBjb25zdCBuYW1lUGF0dGVybiA9IC9eW1x1NGUwMC1cdTlmYTVhLXpBLVrCt1xzXSskLzsNCiAgICAgICAgaWYgKCFuYW1lUGF0dGVybi50ZXN0KHZpc2l0b3IubmFtZS50cmltKCkpKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihgJHt2aXNpdG9yVGl0bGV955qE5aeT5ZCN5Y+q6IO95YyF5ZCr5Lit5paH44CB6Iux5paH5a2X5q+N5ZKM5bi46KeB56ym5Y+3YCk7DQogICAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g6aqM6K+B5omL5py65Y+3DQogICAgICAgIGlmICghdmlzaXRvci5waG9uZSB8fCB2aXNpdG9yLnBob25lLnRyaW0oKSA9PT0gJycpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGDor7fovpPlhaUke3Zpc2l0b3JUaXRsZX3nmoTmiYvmnLrlj7dgKTsNCiAgICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICAgIH0NCg0KICAgICAgICBjb25zdCBwaG9uZVBhdHRlcm4gPSAvXjFbMy05XVxkezl9JC87DQogICAgICAgIGlmICghcGhvbmVQYXR0ZXJuLnRlc3QodmlzaXRvci5waG9uZS50cmltKCkpKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihgJHt2aXNpdG9yVGl0bGV955qE5omL5py65Y+35qC85byP5LiN5q2j56GuYCk7DQogICAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g6aqM6K+B6Lqr5Lu96K+B5Y+3DQogICAgICAgIGlmICghdmlzaXRvci5pZE51bWJlciB8fCB2aXNpdG9yLmlkTnVtYmVyLnRyaW0oKSA9PT0gJycpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGDor7fovpPlhaUke3Zpc2l0b3JUaXRsZX3nmoTouqvku73or4Hlj7dgKTsNCiAgICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICAgIH0NCiAgICAgICAgDQogICAgICAgIGNvbnN0IGlkQ2FyZCA9IHZpc2l0b3IuaWROdW1iZXIudHJpbSgpLnRvVXBwZXJDYXNlKCk7DQogICAgICAgIA0KICAgICAgICAvLyDlpoLmnpzmmK8xOOS9jei6q+S7veivgeWPt++8jOmqjOivgeagvOW8jw0KICAgICAgICBpZiAoaWRDYXJkLmxlbmd0aCA9PT0gMTgpIHsNCiAgICAgICAgICBjb25zdCBpZFBhdHRlcm4gPSAvXlsxLTldXGR7NX0oMTh8MTl8MjApXGR7Mn0oKDBbMS05XSl8KDFbMC0yXSkpKChbMC0yXVsxLTldKXwxMHwyMHwzMHwzMSlcZHszfVswLTlYeF0kLzsNCiAgICAgICAgICBpZiAoIWlkUGF0dGVybi50ZXN0KGlkQ2FyZCkpIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoYCR7dmlzaXRvclRpdGxlfeeahOi6q+S7veivgeWPt+agvOW8j+S4jeato+ehrmApOw0KICAgICAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOajgOafpei6q+S7veivgeWPt+aYr+WQpuS4juS4u+iuv+WuoumHjeWkjQ0KICAgICAgICBpZiAodmlzaXRvci5pZE51bWJlci50cmltKCkudG9VcHBlckNhc2UoKSA9PT0gdGhpcy5pZGVudGl0eUZvcm0uaWROdW1iZXIudHJpbSgpLnRvVXBwZXJDYXNlKCkpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGAke3Zpc2l0b3JUaXRsZX3kuI7kuLvorr/lrqLnmoTouqvku73or4Hlj7fph43lpI1gKTsNCiAgICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICAgIH0NCg0KICAgICAgICAvLyDmo4Dmn6Xouqvku73or4Hlj7fmmK/lkKbkuI7lhbbku5blkIzooYzkurrlkZjph43lpI0NCiAgICAgICAgZm9yIChsZXQgaiA9IDA7IGogPCB0aGlzLmNvbXBhbmlvbkxpc3QubGVuZ3RoOyBqKyspIHsNCiAgICAgICAgICBpZiAoaSAhPT0gaiAmJiB2aXNpdG9yLmlkTnVtYmVyLnRyaW0oKS50b1VwcGVyQ2FzZSgpID09PSB0aGlzLmNvbXBhbmlvbkxpc3Rbal0uaWROdW1iZXIudHJpbSgpLnRvVXBwZXJDYXNlKCkpIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoYCR7dmlzaXRvclRpdGxlfeS4juiuv+WuoiR7aiArIDJ955qE6Lqr5Lu96K+B5Y+36YeN5aSNYCk7DQogICAgICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICAgICAgfQ0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5qOA5p+l5omL5py65Y+35piv5ZCm5LiO5Li76K6/5a6i6YeN5aSNDQogICAgICAgIGlmICh2aXNpdG9yLnBob25lLnRyaW0oKSA9PT0gdGhpcy5pZGVudGl0eUZvcm0ucGhvbmUudHJpbSgpKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihgJHt2aXNpdG9yVGl0bGV95LiO5Li76K6/5a6i55qE5omL5py65Y+36YeN5aSN77yM6K+356Gu6K6k5piv5ZCm5Li65ZCM5LiA5Lq6YCk7DQogICAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5qOA5p+l5omL5py65Y+35piv5ZCm5LiO5YW25LuW5ZCM6KGM5Lq65ZGY6YeN5aSNDQogICAgICAgIGZvciAobGV0IGogPSAwOyBqIDwgdGhpcy5jb21wYW5pb25MaXN0Lmxlbmd0aDsgaisrKSB7DQogICAgICAgICAgaWYgKGkgIT09IGogJiYgdmlzaXRvci5waG9uZS50cmltKCkgPT09IHRoaXMuY29tcGFuaW9uTGlzdFtqXS5waG9uZS50cmltKCkpIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoYCR7dmlzaXRvclRpdGxlfeS4juiuv+WuoiR7aiArIDJ955qE5omL5py65Y+36YeN5aSN77yM6K+356Gu6K6k5piv5ZCm5Li65ZCM5LiA5Lq6YCk7DQogICAgICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIA0KICAgICAgICAvLyDmuIXnkIblkozmoIflh4bljJbmlbDmja4NCiAgICAgICAgdmlzaXRvci5uYW1lID0gdmlzaXRvci5uYW1lLnRyaW0oKTsNCiAgICAgICAgdmlzaXRvci5waG9uZSA9IHZpc2l0b3IucGhvbmUudHJpbSgpOw0KICAgICAgICB2aXNpdG9yLmlkTnVtYmVyID0gdmlzaXRvci5pZE51bWJlci50cmltKCkudG9VcHBlckNhc2UoKTsNCiAgICAgICAgdmlzaXRvci5jb21wYW55ID0gKHZpc2l0b3IuY29tcGFueSB8fCAnJykudHJpbSgpOw0KICAgICAgfQ0KDQogICAgICByZXR1cm4gdHJ1ZTsNCiAgICB9LA0KDQogICAgLyoqIOmqjOivgeaXtumXtCAqLw0KICAgIHZhbGlkYXRlVGltZXMoKSB7DQogICAgICBpZiAoIXRoaXMudmlzaXRGb3JtLnBsYW5uZWRFbnRyeURhdGV0aW1lKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+ivt+mAieaLqemihOiuoeadpeiuv+aXtumXtCcpOw0KICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICB9DQoNCiAgICAgIGlmICghdGhpcy52aXNpdEZvcm0ucGxhbm5lZEV4aXREYXRldGltZSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfor7fpgInmi6npooTorqHnprvlvIDml7bpl7QnKTsNCiAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgfQ0KDQogICAgICBjb25zdCBhcnJpdmFsVGltZSA9IG5ldyBEYXRlKHRoaXMudmlzaXRGb3JtLnBsYW5uZWRFbnRyeURhdGV0aW1lKTsNCiAgICAgIGNvbnN0IGRlcGFydHVyZVRpbWUgPSBuZXcgRGF0ZSh0aGlzLnZpc2l0Rm9ybS5wbGFubmVkRXhpdERhdGV0aW1lKTsNCiAgICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7DQoNCiAgICAgIC8vIOmqjOivgeadpeiuv+aXtumXtOS4jeiDveaYr+i/h+WOu+aXtumXtO+8iOWFgeiuuOW9k+WJjeaXtumXtOWJjTHlsI/ml7bnmoTor6/lt67vvIkNCiAgICAgIGNvbnN0IG9uZUhvdXJCZWZvcmUgPSBuZXcgRGF0ZShub3cuZ2V0VGltZSgpIC0gNjAgKiA2MCAqIDEwMDApOw0KICAgICAgaWYgKGFycml2YWxUaW1lIDwgb25lSG91ckJlZm9yZSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfpooTorqHmnaXorr/ml7bpl7TkuI3og73mmK/ov4fljrvml7bpl7QnKTsNCiAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgfQ0KDQogICAgICAvLyDpqozor4HnprvlvIDml7bpl7Tlv4XpobvmmZrkuo7mnaXorr/ml7bpl7QNCiAgICAgIGlmIChkZXBhcnR1cmVUaW1lIDw9IGFycml2YWxUaW1lKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+mihOiuoeemu+W8gOaXtumXtOW/hemhu+aZmuS6juadpeiuv+aXtumXtCcpOw0KICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICB9DQoNCiAgICAgIHJldHVybiB0cnVlOw0KICAgIH0sDQoNCiAgICAvKiog5qC85byP5YyW5pel5pyf5Li65ZCO56uv5pyf5pyb55qE5qC85byPICovDQogICAgZm9ybWF0RGF0ZUZvckJhY2tlbmQoZGF0ZVN0cikgew0KICAgICAgaWYgKCFkYXRlU3RyKSByZXR1cm4gJyc7DQogICAgICANCiAgICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZShkYXRlU3RyKTsNCiAgICAgIGlmIChpc05hTihkYXRlLmdldFRpbWUoKSkpIHJldHVybiAnJzsNCiAgICAgIA0KICAgICAgY29uc3QgeWVhciA9IGRhdGUuZ2V0RnVsbFllYXIoKTsNCiAgICAgIGNvbnN0IG1vbnRoID0gU3RyaW5nKGRhdGUuZ2V0TW9udGgoKSArIDEpLnBhZFN0YXJ0KDIsICcwJyk7DQogICAgICBjb25zdCBkYXkgPSBTdHJpbmcoZGF0ZS5nZXREYXRlKCkpLnBhZFN0YXJ0KDIsICcwJyk7DQogICAgICBjb25zdCBob3VycyA9IFN0cmluZyhkYXRlLmdldEhvdXJzKCkpLnBhZFN0YXJ0KDIsICcwJyk7DQogICAgICBjb25zdCBtaW51dGVzID0gU3RyaW5nKGRhdGUuZ2V0TWludXRlcygpKS5wYWRTdGFydCgyLCAnMCcpOw0KICAgICAgY29uc3Qgc2Vjb25kcyA9IFN0cmluZyhkYXRlLmdldFNlY29uZHMoKSkucGFkU3RhcnQoMiwgJzAnKTsNCiAgICAgIA0KICAgICAgcmV0dXJuIGAke3llYXJ9LSR7bW9udGh9LSR7ZGF5fSAke2hvdXJzfToke21pbnV0ZXN9OiR7c2Vjb25kc31gOw0KICAgIH0sDQogICAgDQogICAgLyoqIOmHjeaWsOeZu+iusCAqLw0KICAgIHJlc2V0UmVnaXN0ZXIoKSB7DQogICAgICB0aGlzLmN1cnJlbnRTdGVwID0gMDsNCiAgICAgIHRoaXMucmVzZXRWZXJpZnlEaXNwbGF5KCk7DQogICAgICB0aGlzLnZlcmlmeU1ldGhvZCA9ICcnOw0KICAgICAgdGhpcy5pZGVudGl0eUZvcm0gPSB7DQogICAgICAgIG5hbWU6ICcnLA0KICAgICAgICBwaG9uZTogJycsDQogICAgICAgIGlkVHlwZTogJycsDQogICAgICAgIGlkTnVtYmVyOiAnJywNCiAgICAgICAgY29tcGFueTogJycNCiAgICAgIH07DQogICAgICB0aGlzLnZpc2l0Rm9ybSA9IHsNCiAgICAgICAgcmVhc29uRm9yVmlzaXQ6ICcnLA0KICAgICAgICBob3N0RW1wbG95ZWVOYW1lOiAnJywNCiAgICAgICAgZGVwYXJ0bWVudFZpc2l0ZWQ6ICcnLA0KICAgICAgICB2ZWhpY2xlUGxhdGVOdW1iZXI6ICcnLA0KICAgICAgICBwbGFubmVkRW50cnlEYXRldGltZTogbnVsbCwNCiAgICAgICAgcGxhbm5lZEV4aXREYXRldGltZTogbnVsbA0KICAgICAgfTsNCiAgICAgIHRoaXMuY29tcGFuaW9uTGlzdCA9IFtdOw0KICAgICAgdGhpcy5yZWdpc3RyYXRpb25JZCA9IG51bGw7DQogICAgfSwNCiAgICANCiAgICAvKiog5omT5Y2w5Yet6K+BICovDQogICAgcHJpbnRDcmVkZW50aWFsKCkgew0KICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCfmraPlnKjnlJ/miJDmiZPljbDmlofku7YuLi4nKTsNCiAgICAgIC8vIFRPRE86IOWunueOsOaJk+WNsOWKn+iDvQ0KICAgIH0sDQoNCiAgICAvKiog5pi+56S65biu5YqpICovDQogICAgc2hvd0hlbHAoKSB7DQogICAgICB0aGlzLiRhbGVydCgn5aaC6ZyA5biu5Yqp77yM6K+36IGU57O75Zut5Yy65YmN5Y+w5oiW5ouo5omT5pyN5Yqh54Ot57q/JywgJ+S9v+eUqOW4ruWKqScsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponDQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLyoqIOiBlOezu+WuouacjSAqLw0KICAgIGNvbnRhY3RTZXJ2aWNlKCkgew0KICAgICAgdGhpcy4kYWxlcnQoJ+acjeWKoeeDree6v++8mjQwMC0xMjM0LTU2N1xu5pyN5Yqh5pe26Ze077ya5ZGo5LiA6Iez5ZGo5LqUIDg6MDAtMTg6MDAnLCAn6IGU57O75a6i5pyNJywgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicNCiAgICAgIH0pOw0KICAgIH0NCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["self-register.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAydA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "self-register.vue", "sourceRoot": "src/views/asc/visitor", "sourcesContent": ["<template>\r\n  <div class=\"pc-register-container\">\r\n    <!-- 顶部导航栏 -->\r\n    <div class=\"top-nav\">\r\n      <div class=\"nav-content\">\r\n        <div class=\"logo-section\">\r\n          <img src=\"@/assets/logo/logo.png\" alt=\"公司logo\" class=\"nav-logo\" />\r\n          <span class=\"company-name\">智慧园区访客登记系统</span>\r\n        </div>\r\n        <div class=\"nav-actions\">\r\n          <el-button type=\"text\" @click=\"showHelp\">使用帮助</el-button>\r\n          <el-button type=\"text\" @click=\"contactService\">联系客服</el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 主要内容区域 -->\r\n    <div class=\"main-content\">\r\n      <!-- 左侧信息面板 -->\r\n      <div class=\"info-panel\">\r\n        <div class=\"welcome-card\">\r\n          <div class=\"welcome-icon\">\r\n            <i class=\"el-icon-office-building\"></i>\r\n          </div>\r\n          <h2>欢迎访问智慧园区</h2>\r\n          <p class=\"welcome-desc\">为了您的安全和便利，请按照以下步骤完成访客登记</p>\r\n          \r\n          <div class=\"process-steps\">\r\n            <div class=\"step-item active\">\r\n              <div class=\"step-circle\">1</div>\r\n              <div class=\"step-text\">\r\n                <h4>填写信息</h4>\r\n                <p>完善访问详细信息</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"step-item\" :class=\"{ active: registrationCompleted }\">\r\n              <div class=\"step-circle\">2</div>\r\n              <div class=\"step-text\">\r\n                <h4>获取凭证</h4>\r\n                <p>生成访问凭证二维码</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"security-tips\">\r\n            <h4><i class=\"el-icon-lock\"></i> 安全提示</h4>\r\n            <ul>\r\n              <li>请确保提供真实有效的身份信息</li>\r\n              <li>您的个人信息将被严格保密</li>\r\n              <li>访问凭证仅限当次使用</li>\r\n            </ul>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 右侧表单区域 -->\r\n      <div class=\"form-panel\">\r\n        <!-- 进度指示器 -->\r\n        <div class=\"progress-indicator\">\r\n          <div class=\"progress-bar\">\r\n            <div class=\"progress-fill\" :style=\"{ width: progressWidth }\"></div>\r\n          </div>\r\n          <div class=\"progress-text\">\r\n            步骤 {{ currentStep + 1 }} / 3\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 表单内容 -->\r\n        <div class=\"form-content\">\r\n          <!-- 步骤1: 身份验证 -->\r\n          <div v-show=\"currentStep === 0\" class=\"step-form\">\r\n            <div class=\"form-header\">\r\n              <h3><i class=\"el-icon-user\"></i> 身份验证</h3>\r\n              <p>请选择合适的验证方式进行身份确认</p>\r\n            </div>\r\n            \r\n            <div class=\"verify-grid\">\r\n              <div class=\"verify-card\" \r\n                   :class=\"{ selected: verifyMethod === 'id_card' }\"\r\n                   @click=\"selectVerifyMethod('id_card')\">\r\n                <div class=\"verify-icon\">\r\n                  <i class=\"el-icon-postcard\"></i>\r\n                </div>\r\n                <h4>身份证验证</h4>\r\n                <p>使用身份证读卡器快速验证</p>\r\n                <div class=\"verify-status\" v-if=\"verifyMethod === 'id_card'\">\r\n                  <i class=\"el-icon-check\"></i>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"verify-card\"\r\n                   :class=\"{ selected: verifyMethod === 'face' }\"\r\n                   @click=\"selectVerifyMethod('face')\">\r\n                <div class=\"verify-icon\">\r\n                  <i class=\"el-icon-camera-solid\"></i>\r\n                </div>\r\n                <h4>人脸识别</h4>\r\n                <p>使用摄像头进行人脸识别</p>\r\n                <div class=\"verify-status\" v-if=\"verifyMethod === 'face'\">\r\n                  <i class=\"el-icon-check\"></i>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"verify-card\"\r\n                   :class=\"{ selected: verifyMethod === 'manual' }\"\r\n                   @click=\"selectVerifyMethod('manual')\">\r\n                <div class=\"verify-icon\">\r\n                  <i class=\"el-icon-edit-outline\"></i>\r\n                </div>\r\n                <h4>手动输入</h4>\r\n                <p>手动填写身份信息</p>\r\n                <div class=\"verify-status\" v-if=\"verifyMethod === 'manual'\">\r\n                  <i class=\"el-icon-check\"></i>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"verify-card\"\r\n                   :class=\"{ selected: verifyMethod === 'passport' }\"\r\n                   @click=\"selectVerifyMethod('passport')\">\r\n                <div class=\"verify-icon\">\r\n                  <i class=\"el-icon-document\"></i>\r\n                </div>\r\n                <h4>护照验证</h4>\r\n                <p>使用护照等其他证件</p>\r\n                <div class=\"verify-status\" v-if=\"verifyMethod === 'passport'\">\r\n                  <i class=\"el-icon-check\"></i>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 验证方式具体操作区域 -->\r\n            <div v-if=\"showManualInput\" class=\"verify-operation\">\r\n              <div class=\"operation-header\">\r\n                <h4>请填写身份信息</h4>\r\n                <el-button type=\"text\" @click=\"backToVerifyMethod\">\r\n                  <i class=\"el-icon-arrow-left\"></i> 重新选择验证方式\r\n                </el-button>\r\n              </div>\r\n              \r\n              <el-form ref=\"identityForm\" :model=\"identityForm\" :rules=\"identityRules\" \r\n                       label-width=\"100px\" class=\"identity-form\">\r\n                <el-row :gutter=\"20\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"姓名\" prop=\"name\">\r\n                      <el-input v-model=\"identityForm.name\" placeholder=\"请输入真实姓名\" \r\n                                prefix-icon=\"el-icon-user\" />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"手机号\" prop=\"phone\">\r\n                      <el-input v-model=\"identityForm.phone\" placeholder=\"请输入手机号码\"\r\n                                prefix-icon=\"el-icon-phone\" />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                \r\n                <el-row :gutter=\"20\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"证件类型\" prop=\"idType\">\r\n                      <el-select v-model=\"identityForm.idType\" placeholder=\"请选择证件类型\" style=\"width: 100%\">\r\n                        <el-option label=\"身份证\" value=\"id_card\" />\r\n                        <el-option label=\"护照\" value=\"passport\" />\r\n                        <el-option label=\"港澳通行证\" value=\"hk_mo_pass\" />\r\n                        <el-option label=\"台湾通行证\" value=\"tw_pass\" />\r\n                        <el-option label=\"其他\" value=\"other\" />\r\n                      </el-select>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"证件号码\" prop=\"idNumber\">\r\n                      <el-input v-model=\"identityForm.idNumber\" placeholder=\"请输入证件号码\"\r\n                                prefix-icon=\"el-icon-postcard\" />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                \r\n                <el-form-item label=\"所属公司\" prop=\"company\">\r\n                  <el-input v-model=\"identityForm.company\" placeholder=\"请输入公司名称\"\r\n                            prefix-icon=\"el-icon-office-building\" />\r\n                </el-form-item>\r\n              </el-form>\r\n            </div>\r\n\r\n            <!-- 身份证读卡器界面 -->\r\n            <div v-if=\"showIdCardReader\" class=\"verify-operation\">\r\n              <div class=\"operation-header\">\r\n                <h4>身份证读卡验证</h4>\r\n                <el-button type=\"text\" @click=\"backToVerifyMethod\">\r\n                  <i class=\"el-icon-arrow-left\"></i> 重新选择验证方式\r\n                </el-button>\r\n              </div>\r\n              \r\n              <div class=\"id-card-reader\">\r\n                <div class=\"reader-visual\">\r\n                  <div class=\"reader-animation\">\r\n                    <i class=\"el-icon-loading rotating\"></i>\r\n                  </div>\r\n                  <h4>请将身份证放置在读卡器上</h4>\r\n                  <p>确保身份证平放在读卡器感应区域</p>\r\n                  <div class=\"reader-tips\">\r\n                    <el-alert title=\"提示\" type=\"info\" :closable=\"false\">\r\n                      如果读卡失败，请检查身份证是否放置正确，或选择其他验证方式\r\n                    </el-alert>\r\n                  </div>\r\n                  <el-button type=\"primary\" @click=\"simulateIdCardRead\" style=\"margin-top: 20px;\">\r\n                    模拟读取成功\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 人脸识别界面 -->\r\n            <div v-if=\"showFaceRecognition\" class=\"verify-operation\">\r\n              <div class=\"operation-header\">\r\n                <h4>人脸识别验证</h4>\r\n                <el-button type=\"text\" @click=\"backToVerifyMethod\">\r\n                  <i class=\"el-icon-arrow-left\"></i> 重新选择验证方式\r\n                </el-button>\r\n              </div>\r\n              \r\n              <div class=\"face-recognition\">\r\n                <div class=\"camera-container\">\r\n                  <div class=\"camera-frame\">\r\n                    <div class=\"camera-overlay\">\r\n                      <div class=\"face-outline\"></div>\r\n                      <i class=\"el-icon-camera-solid camera-icon\"></i>\r\n                    </div>\r\n                    <div class=\"scanning-line\"></div>\r\n                  </div>\r\n                  <h4>请面向摄像头</h4>\r\n                  <p>保持面部清晰可见，等待识别完成</p>\r\n                  <el-button type=\"primary\" @click=\"simulateFaceRecognition\" style=\"margin-top: 20px;\">\r\n                    模拟识别成功\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 步骤2: 填写访问信息 -->\r\n          <div v-show=\"currentStep === 1\" class=\"step-form\">\r\n            <div class=\"form-header\">\r\n              <h3><i class=\"el-icon-edit\"></i> 完善访问信息</h3>\r\n              <p>请填写详细的访问信息</p>\r\n            </div>\r\n\r\n            <el-form ref=\"visitForm\" :model=\"visitForm\" :rules=\"visitRules\" \r\n                     label-width=\"120px\" class=\"visit-form\">\r\n              \r\n              <!-- 基本访问信息 -->\r\n              <div class=\"form-section\">\r\n                <h4 class=\"section-title\">基本访问信息</h4>\r\n                <el-row :gutter=\"20\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"来访事由\" prop=\"reasonForVisit\">\r\n                      <el-input v-model=\"visitForm.reasonForVisit\" placeholder=\"请输入来访事由\"\r\n                                prefix-icon=\"el-icon-tickets\" type=\"textarea\" :rows=\"3\" maxlength=\"200\" show-word-limit />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"被访人姓名\" prop=\"hostEmployeeName\">\r\n                      <el-input v-model=\"visitForm.hostEmployeeName\" placeholder=\"请输入被访人姓名\"\r\n                                prefix-icon=\"el-icon-user\" maxlength=\"20\" />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n\r\n                <el-row :gutter=\"20\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"被访部门\" prop=\"departmentVisited\">\r\n                      <el-input v-model=\"visitForm.departmentVisited\" placeholder=\"请输入被访部门\"\r\n                                prefix-icon=\"el-icon-office-building\" maxlength=\"50\" />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"车牌号\">\r\n                      <el-input v-model=\"visitForm.vehiclePlateNumber\" placeholder=\"如有车辆请填写车牌号，多个用逗号分隔\"\r\n                                prefix-icon=\"el-icon-truck\" />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n              </div>\r\n\r\n              <!-- 时间信息 -->\r\n              <div class=\"form-section\">\r\n                <h4 class=\"section-title\">访问时间</h4>\r\n                <el-row :gutter=\"20\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"预计到访时间\" prop=\"plannedEntryDatetime\">\r\n                      <el-date-picker\r\n                        v-model=\"visitForm.plannedEntryDatetime\"\r\n                        type=\"datetime\"\r\n                        placeholder=\"选择到访时间\"\r\n                        style=\"width: 100%\"\r\n                        value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                        :picker-options=\"entryPickerOptions\"\r\n                        @change=\"onArrivalTimeChange\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"预计离开时间\" prop=\"plannedExitDatetime\">\r\n                      <el-date-picker\r\n                        v-model=\"visitForm.plannedExitDatetime\"\r\n                        type=\"datetime\"\r\n                        placeholder=\"选择离开时间\"\r\n                        style=\"width: 100%\"\r\n                        value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                        :picker-options=\"exitPickerOptions\"\r\n                        @change=\"onDepartureTimeChange\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n              </div>\r\n\r\n              <!-- 同行人员信息 -->\r\n              <div class=\"form-section\">\r\n                <div class=\"section-header\">\r\n                  <h4 class=\"section-title\">访客信息（第一位为主联系人）共 {{ totalVisitors }} 人</h4>\r\n                  <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"addCompanion\" :disabled=\"companionList.length >= 9\">\r\n                    添加访客\r\n                  </el-button>\r\n                </div>\r\n                \r\n                <div v-if=\"companionList.length === 0\" class=\"no-companions\">\r\n                  <i class=\"el-icon-user-solid\"></i>\r\n                  <p>暂无同行人员，主访客信息已在身份验证步骤中填写</p>\r\n                </div>\r\n                \r\n                <div v-else class=\"companions-table\">\r\n                  <el-table :data=\"companionList\" border style=\"width: 100%;\">\r\n                    <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        访客{{ scope.$index + 2 }}\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"姓名\" width=\"150\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-input v-model=\"scope.row.name\" placeholder=\"请输入姓名\" size=\"small\" maxlength=\"20\" />\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"手机号\" width=\"150\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-input v-model=\"scope.row.phone\" placeholder=\"请输入手机号\" size=\"small\" />\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"身份证号\" min-width=\"180\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-input v-model=\"scope.row.idNumber\" placeholder=\"请输入身份证号\" size=\"small\" />\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"公司\" width=\"150\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-input v-model=\"scope.row.company\" placeholder=\"请输入公司（可选）\" size=\"small\" maxlength=\"100\" />\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"操作\" width=\"80\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-button size=\"mini\" type=\"danger\" icon=\"el-icon-delete\" \r\n                                   @click=\"removeCompanion(scope.$index)\" circle />\r\n                      </template>\r\n                    </el-table-column>\r\n                  </el-table>\r\n                </div>\r\n              </div>\r\n            </el-form>\r\n          </div>\r\n\r\n          <!-- 步骤3: 完成登记 -->\r\n          <div v-show=\"currentStep === 2\" class=\"step-form\">\r\n            <div class=\"form-header\">\r\n              <h3><i class=\"el-icon-circle-check\"></i> 登记完成</h3>\r\n              <p>您的访客登记已成功提交</p>\r\n            </div>\r\n\r\n            <div class=\"success-content\">\r\n              <div class=\"success-icon\">\r\n                <i class=\"el-icon-circle-check\"></i>\r\n              </div>\r\n              \r\n              <h4>登记成功！</h4>\r\n              <p class=\"success-message\">您的访客登记已提交，请等待审核通过</p>\r\n              \r\n              <!-- 登记信息摘要 -->\r\n              <div class=\"register-summary\">\r\n                <h5>登记信息摘要</h5>\r\n                <el-descriptions :column=\"2\" border>\r\n                  <el-descriptions-item label=\"访客姓名\">\r\n                    {{ identityForm.name }}\r\n                  </el-descriptions-item>\r\n                  <el-descriptions-item label=\"联系电话\">\r\n                    {{ identityForm.phone }}\r\n                  </el-descriptions-item>\r\n                  <el-descriptions-item label=\"被访人\">\r\n                    {{ visitForm.hostEmployeeName }}\r\n                  </el-descriptions-item>\r\n                  <el-descriptions-item label=\"被访部门\">\r\n                    {{ visitForm.departmentVisited }}\r\n                  </el-descriptions-item>\r\n                  <el-descriptions-item label=\"来访事由\" span=\"2\">\r\n                    {{ visitForm.reasonForVisit }}\r\n                  </el-descriptions-item>\r\n                  <el-descriptions-item label=\"预计到访时间\">\r\n                    {{ parseTime(visitForm.plannedEntryDatetime) }}\r\n                  </el-descriptions-item>\r\n                  <el-descriptions-item label=\"预计离开时间\">\r\n                    {{ parseTime(visitForm.plannedExitDatetime) }}\r\n                  </el-descriptions-item>\r\n                </el-descriptions>\r\n              </div>\r\n\r\n              <!-- 访问凭证 -->\r\n              <div class=\"access-credential\" v-if=\"registrationId\">\r\n                <h5>访问凭证</h5>\r\n                <div class=\"credential-card\">\r\n                  <div class=\"qr-code-container\">\r\n                    <div class=\"qr-code\">\r\n                      <i class=\"el-icon-qrcode\"></i>\r\n                    </div>\r\n                    <p class=\"qr-code-id\">{{ registrationId }}</p>\r\n                  </div>\r\n                  <div class=\"credential-info\">\r\n                    <h6>使用说明</h6>\r\n                    <ul>\r\n                      <li>请保存此二维码截图</li>\r\n                      <li>审核通过后可用于园区门禁</li>\r\n                      <li>凭证仅限当次访问使用</li>\r\n                      <li>如有疑问请联系园区前台</li>\r\n                    </ul>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 底部操作按钮 -->\r\n        <div class=\"form-actions\">\r\n          <el-button v-if=\"currentStep > 0\" @click=\"prevStep\" size=\"large\">\r\n            <i class=\"el-icon-arrow-left\"></i> 上一步\r\n          </el-button>\r\n          \r\n          <el-button v-if=\"currentStep === 0 && verifyMethod && !showVerifyOperation\" \r\n                     type=\"primary\" @click=\"handleVerifyNext\" size=\"large\" :loading=\"verifying\">\r\n            下一步 <i class=\"el-icon-arrow-right\"></i>\r\n          </el-button>\r\n          \r\n          <el-button v-if=\"currentStep === 0 && showManualInput\" \r\n                     type=\"primary\" @click=\"confirmIdentity\" size=\"large\" :loading=\"verifying\">\r\n            确认身份 <i class=\"el-icon-arrow-right\"></i>\r\n          </el-button>\r\n          \r\n          <el-button v-if=\"currentStep === 1\" \r\n                     type=\"primary\" @click=\"nextStep\" size=\"large\" :loading=\"submitting\">\r\n            提交登记 <i class=\"el-icon-arrow-right\"></i>\r\n          </el-button>\r\n          \r\n          <div v-if=\"currentStep === 2\" class=\"final-actions\">\r\n            <el-button type=\"primary\" @click=\"printCredential\" size=\"large\">\r\n              <i class=\"el-icon-printer\"></i> 打印凭证\r\n            </el-button>\r\n            <el-button @click=\"resetRegister\" size=\"large\">\r\n              <i class=\"el-icon-refresh\"></i> 重新登记\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { submitVisitorRegistration } from \"@/api/asc/visitor\";\r\n\r\nexport default {\r\n  name: \"SelfRegister\",\r\n  data() {\r\n    return {\r\n      currentStep: 0,\r\n      verifying: false,\r\n      submitting: false,\r\n      \r\n      // 验证方式\r\n      verifyMethod: '',\r\n      showManualInput: false,\r\n      showIdCardReader: false,\r\n      showFaceRecognition: false,\r\n      \r\n      // 身份信息\r\n      identityForm: {\r\n        name: '',\r\n        phone: '',\r\n        idType: '',\r\n        idNumber: '',\r\n        company: ''\r\n      },\r\n      identityRules: {\r\n        name: [\r\n          { required: true, message: '请输入姓名', trigger: 'blur' },\r\n          { min: 2, max: 20, message: '姓名长度应在2-20个字符之间', trigger: 'blur' },\r\n          { pattern: /^[\\u4e00-\\u9fa5a-zA-Z·\\s]+$/, message: '姓名只能包含中文、英文字母和常见符号', trigger: 'blur' }\r\n        ],\r\n        phone: [\r\n          { required: true, message: '请输入手机号', trigger: 'blur' },\r\n          { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }\r\n        ],\r\n        idType: [{ required: true, message: '请选择证件类型', trigger: 'change' }],\r\n        idNumber: [\r\n          { required: true, message: '请输入证件号码', trigger: 'blur' },\r\n          { min: 15, max: 18, message: '证件号码长度不正确', trigger: 'blur' }\r\n        ],\r\n        company: [{ max: 100, message: '公司名称不能超过100个字符', trigger: 'blur' }]\r\n      },\r\n      \r\n      // 访问信息\r\n      visitForm: {\r\n        reasonForVisit: '',\r\n        hostEmployeeName: '',\r\n        departmentVisited: '',\r\n        vehiclePlateNumber: '',\r\n        plannedEntryDatetime: null,\r\n        plannedExitDatetime: null\r\n      },\r\n      visitRules: {\r\n        reasonForVisit: [\r\n          { required: true, message: '请输入来访事由', trigger: 'blur' },\r\n          { min: 2, max: 200, message: '来访事由长度应在2-200个字符之间', trigger: 'blur' }\r\n        ],\r\n        hostEmployeeName: [\r\n          { required: true, message: '请输入被访人姓名', trigger: 'blur' },\r\n          { min: 2, max: 20, message: '被访人姓名长度应在2-20个字符之间', trigger: 'blur' }\r\n        ],\r\n        departmentVisited: [\r\n          { required: true, message: '请输入被访部门', trigger: 'blur' },\r\n          { min: 2, max: 50, message: '部门名称长度应在2-50个字符之间', trigger: 'blur' }\r\n        ],\r\n        plannedEntryDatetime: [{ required: true, message: '请选择预计到访时间', trigger: 'change' }],\r\n        plannedExitDatetime: [{ required: true, message: '请选择预计离开时间', trigger: 'change' }]\r\n      },\r\n      \r\n      // 同行人员\r\n      companionList: [],\r\n      \r\n      // 登记结果\r\n      registrationId: null,\r\n\r\n      // 时间选择器配置\r\n      entryPickerOptions: {\r\n        shortcuts: [{\r\n          text: '现在',\r\n          onClick(picker) {\r\n            picker.$emit('pick', new Date());\r\n          }\r\n        }, {\r\n          text: '1小时后',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setTime(date.getTime() + 3600 * 1000);\r\n            picker.$emit('pick', date);\r\n          }\r\n        }, {\r\n          text: '明天上午9点',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setDate(date.getDate() + 1);\r\n            date.setHours(9, 0, 0, 0);\r\n            picker.$emit('pick', date);\r\n          }\r\n        }],\r\n        disabledDate(time) {\r\n          // 允许选择当前时间前1小时到未来30天\r\n          const oneHourBefore = Date.now() - 3600 * 1000;\r\n          const thirtyDaysLater = Date.now() + 30 * 24 * 3600 * 1000;\r\n          return time.getTime() < oneHourBefore || time.getTime() > thirtyDaysLater;\r\n        }\r\n      },\r\n      \r\n      exitPickerOptions: {\r\n        shortcuts: [{\r\n          text: '2小时后',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setTime(date.getTime() + 2 * 3600 * 1000);\r\n            picker.$emit('pick', date);\r\n          }\r\n        }, {\r\n          text: '今天下午6点',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setHours(18, 0, 0, 0);\r\n            if (date.getTime() < Date.now()) {\r\n              date.setDate(date.getDate() + 1);\r\n            }\r\n            picker.$emit('pick', date);\r\n          }\r\n        }, {\r\n          text: '明天下午6点',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setDate(date.getDate() + 1);\r\n            date.setHours(18, 0, 0, 0);\r\n            picker.$emit('pick', date);\r\n          }\r\n        }],\r\n        disabledDate(time) {\r\n          // 允许选择当前时间到未来30天\r\n          const thirtyDaysLater = Date.now() + 30 * 24 * 3600 * 1000;\r\n          return time.getTime() < Date.now() - 8.64e7 || time.getTime() > thirtyDaysLater;\r\n        }\r\n      }\r\n    };\r\n  },\r\n  \r\n  computed: {\r\n    progressWidth() {\r\n      return ((this.currentStep + 1) / 3) * 100 + '%';\r\n    },\r\n    \r\n    showVerifyOperation() {\r\n      return this.showManualInput || this.showIdCardReader || this.showFaceRecognition;\r\n    },\r\n\r\n    // 总访客人数（主访客 + 同行人员）\r\n    totalVisitors() {\r\n      return 1 + this.companionList.length;\r\n    }\r\n  },\r\n  \r\n  methods: {\r\n    /** 选择验证方式 */\r\n    selectVerifyMethod(method) {\r\n      this.verifyMethod = method;\r\n      this.resetVerifyDisplay();\r\n      \r\n      switch (method) {\r\n        case 'manual':\r\n          this.showManualInput = true;\r\n          break;\r\n        case 'id_card':\r\n          this.showIdCardReader = true;\r\n          break;\r\n        case 'face':\r\n          this.showFaceRecognition = true;\r\n          break;\r\n        case 'passport':\r\n          this.showManualInput = true;\r\n          this.identityForm.idType = 'passport';\r\n          break;\r\n      }\r\n    },\r\n    \r\n    /** 重置验证显示 */\r\n    resetVerifyDisplay() {\r\n      this.showManualInput = false;\r\n      this.showIdCardReader = false;\r\n      this.showFaceRecognition = false;\r\n    },\r\n    \r\n    /** 返回验证方式选择 */\r\n    backToVerifyMethod() {\r\n      this.resetVerifyDisplay();\r\n      this.verifyMethod = '';\r\n    },\r\n\r\n    /** 处理验证方式下一步 */\r\n    handleVerifyNext() {\r\n      if (this.verifyMethod === 'manual') {\r\n        this.showManualInput = true;\r\n      } else if (this.verifyMethod === 'id_card') {\r\n        this.showIdCardReader = true;\r\n      } else if (this.verifyMethod === 'face') {\r\n        this.showFaceRecognition = true;\r\n      } else if (this.verifyMethod === 'passport') {\r\n        this.showManualInput = true;\r\n        this.identityForm.idType = 'passport';\r\n      }\r\n    },\r\n    \r\n    /** 确认身份 */\r\n    confirmIdentity() {\r\n      this.$refs.identityForm.validate(valid => {\r\n        if (valid) {\r\n          this.verifying = true;\r\n          // 模拟验证过程\r\n          setTimeout(() => {\r\n            this.verifying = false;\r\n            this.$message.success('身份验证成功');\r\n            this.nextStep();\r\n          }, 1000);\r\n        }\r\n      });\r\n    },\r\n    \r\n    /** 模拟身份证读取 */\r\n    simulateIdCardRead() {\r\n      this.identityForm = {\r\n        name: '张三',\r\n        phone: '13800138000',\r\n        idType: 'id_card',\r\n        idNumber: '110101199001011234',\r\n        company: '某某科技有限公司'\r\n      };\r\n      this.$message.success('身份证信息读取成功');\r\n      setTimeout(() => {\r\n        this.nextStep();\r\n      }, 1000);\r\n    },\r\n    \r\n    /** 模拟人脸识别 */\r\n    simulateFaceRecognition() {\r\n      this.identityForm = {\r\n        name: '李四',\r\n        phone: '13900139000',\r\n        idType: 'id_card',\r\n        idNumber: '110101199002021234',\r\n        company: '某某贸易有限公司'\r\n      };\r\n      this.$message.success('人脸识别成功');\r\n      setTimeout(() => {\r\n        this.nextStep();\r\n      }, 1000);\r\n    },\r\n    \r\n    /** 下一步 */\r\n    nextStep() {\r\n      if (this.currentStep === 1) {\r\n        this.submitRegistration();\r\n      } else {\r\n        this.currentStep++;\r\n      }\r\n    },\r\n    \r\n    /** 上一步 */\r\n    prevStep() {\r\n      this.currentStep--;\r\n      if (this.currentStep === 0) {\r\n        this.resetVerifyDisplay();\r\n      }\r\n    },\r\n    \r\n    /** 添加同行人员 */\r\n    addCompanion() {\r\n      if (this.companionList.length >= 9) {\r\n        this.$message.warning('最多只能添加9名同行人员（总计10名访客）');\r\n        return;\r\n      }\r\n\r\n      this.companionList.push({\r\n        name: '',\r\n        phone: '',\r\n        idNumber: '',\r\n        company: ''\r\n      });\r\n      \r\n      this.$message.success('已添加访客');\r\n    },\r\n    \r\n    /** 来访时间变更事件 */\r\n    onArrivalTimeChange(value) {\r\n      console.log('预计来访时间变更:', value);\r\n      // 自动设置离开时间为来访时间后4小时\r\n      if (value && !this.visitForm.plannedExitDatetime) {\r\n        const arrivalTime = new Date(value);\r\n        const departureTime = new Date(arrivalTime.getTime() + 4 * 60 * 60 * 1000);\r\n        \r\n        const year = departureTime.getFullYear();\r\n        const month = String(departureTime.getMonth() + 1).padStart(2, '0');\r\n        const day = String(departureTime.getDate()).padStart(2, '0');\r\n        const hours = String(departureTime.getHours()).padStart(2, '0');\r\n        const minutes = String(departureTime.getMinutes()).padStart(2, '0');\r\n        const seconds = String(departureTime.getSeconds()).padStart(2, '0');\r\n        \r\n        this.visitForm.plannedExitDatetime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n      }\r\n    },\r\n\r\n    /** 离开时间变更事件 */\r\n    onDepartureTimeChange(value) {\r\n      console.log('预计离开时间变更:', value);\r\n      // 验证离开时间不能早于来访时间\r\n      if (value && this.visitForm.plannedEntryDatetime) {\r\n        const arrivalTime = new Date(this.visitForm.plannedEntryDatetime);\r\n        const departureTime = new Date(value);\r\n\r\n        if (departureTime <= arrivalTime) {\r\n          this.$message.warning('预计离开时间不能早于或等于来访时间');\r\n          this.visitForm.plannedExitDatetime = '';\r\n        }\r\n      }\r\n    },\r\n    \r\n    /** 提交登记 */\r\n    submitRegistration() {\r\n      this.$refs.visitForm.validate(valid => {\r\n        if (valid) {\r\n          // 验证同行人员信息\r\n          if (!this.validateVisitors()) {\r\n            return;\r\n          }\r\n\r\n          // 验证时间\r\n          if (!this.validateTimes()) {\r\n            return;\r\n          }\r\n\r\n          this.submitting = true;\r\n          \r\n          // 构建提交数据 - 按照微信端相同的数据结构\r\n          const submitData = {\r\n            // VisitRegistrations 对象\r\n            registration: {\r\n              primaryContactName: this.identityForm.name.trim(),\r\n              primaryContactPhone: this.identityForm.phone.trim(),\r\n              reasonForVisit: this.visitForm.reasonForVisit.trim(),\r\n              hostEmployeeName: this.visitForm.hostEmployeeName.trim(),\r\n              hostEmployeeId: null, // 前端暂时没有被访人ID，后端可能会自动处理\r\n              departmentVisited: this.visitForm.departmentVisited.trim(),\r\n              plannedEntryDatetime: this.formatDateForBackend(this.visitForm.plannedEntryDatetime),\r\n              plannedExitDatetime: this.formatDateForBackend(this.visitForm.plannedExitDatetime),\r\n              vehiclePlateNumber: this.visitForm.vehiclePlateNumber ? this.visitForm.vehiclePlateNumber.trim() : '',\r\n              totalCompanions: this.companionList.length\r\n            },\r\n            // RegistrationAttendees 数组 - 使用后端期望的临时字段\r\n            attendeesList: [\r\n              // 主访客\r\n              {\r\n                visitorName: this.identityForm.name.trim(),\r\n                visitorPhone: this.identityForm.phone.trim(),\r\n                visitorIdCard: this.identityForm.idNumber.trim().toUpperCase(),\r\n                visitorCompany: this.identityForm.company ? this.identityForm.company.trim() : '',\r\n                isPrimary: \"1\", // 第一个访客必须是主联系人\r\n                visitorAvatarPhoto: null // 暂时不支持头像上传\r\n              },\r\n              // 同行人员\r\n              ...this.companionList\r\n                .filter(item => item.name && item.phone && item.idNumber)\r\n                .map(item => ({\r\n                  visitorName: item.name.trim(),\r\n                  visitorPhone: item.phone.trim(),\r\n                  visitorIdCard: item.idNumber.trim().toUpperCase(),\r\n                  visitorCompany: item.company ? item.company.trim() : '',\r\n                  isPrimary: \"0\",\r\n                  visitorAvatarPhoto: null\r\n                }))\r\n            ]\r\n          };\r\n\r\n          console.log('提交数据结构:', JSON.stringify(submitData, null, 2));\r\n          \r\n          submitVisitorRegistration(submitData)\r\n            .then(response => {\r\n              this.registrationId = response.data || 'VR' + Date.now();\r\n              this.$message.success(`${this.totalVisitors}名访客登记成功！`);\r\n              this.currentStep++;\r\n            })\r\n            .catch(error => {\r\n              this.$message.error(error.msg || '登记失败，请重试');\r\n            })\r\n            .finally(() => {\r\n              this.submitting = false;\r\n            });\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 验证访客信息 */\r\n    validateVisitors() {\r\n      // 验证主访客信息（已在身份验证步骤中验证，这里再次确认）\r\n      if (!this.identityForm.name || this.identityForm.name.trim().length < 2) {\r\n        this.$message.error('主访客姓名不能为空且长度不能少于2个字符');\r\n        return false;\r\n      }\r\n\r\n      if (!this.identityForm.phone || !/^1[3-9]\\d{9}$/.test(this.identityForm.phone.trim())) {\r\n        this.$message.error('主访客手机号格式不正确');\r\n        return false;\r\n      }\r\n\r\n      if (!this.identityForm.idNumber || this.identityForm.idNumber.trim().length < 15) {\r\n        this.$message.error('主访客身份证号不能为空');\r\n        return false;\r\n      }\r\n\r\n      // 验证同行人员信息\r\n      for (let i = 0; i < this.companionList.length; i++) {\r\n        const visitor = this.companionList[i];\r\n        const visitorTitle = `访客${i + 2}`;\r\n\r\n        // 验证姓名\r\n        if (!visitor.name || visitor.name.trim() === '') {\r\n          this.$message.error(`请输入${visitorTitle}的姓名`);\r\n          return false;\r\n        }\r\n        \r\n        if (visitor.name.trim().length < 2 || visitor.name.trim().length > 20) {\r\n          this.$message.error(`${visitorTitle}的姓名长度应在2-20个字符之间`);\r\n          return false;\r\n        }\r\n\r\n        // 验证姓名格式\r\n        const namePattern = /^[\\u4e00-\\u9fa5a-zA-Z·\\s]+$/;\r\n        if (!namePattern.test(visitor.name.trim())) {\r\n          this.$message.error(`${visitorTitle}的姓名只能包含中文、英文字母和常见符号`);\r\n          return false;\r\n        }\r\n\r\n        // 验证手机号\r\n        if (!visitor.phone || visitor.phone.trim() === '') {\r\n          this.$message.error(`请输入${visitorTitle}的手机号`);\r\n          return false;\r\n        }\r\n\r\n        const phonePattern = /^1[3-9]\\d{9}$/;\r\n        if (!phonePattern.test(visitor.phone.trim())) {\r\n          this.$message.error(`${visitorTitle}的手机号格式不正确`);\r\n          return false;\r\n        }\r\n\r\n        // 验证身份证号\r\n        if (!visitor.idNumber || visitor.idNumber.trim() === '') {\r\n          this.$message.error(`请输入${visitorTitle}的身份证号`);\r\n          return false;\r\n        }\r\n        \r\n        const idCard = visitor.idNumber.trim().toUpperCase();\r\n        \r\n        // 如果是18位身份证号，验证格式\r\n        if (idCard.length === 18) {\r\n          const idPattern = /^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$/;\r\n          if (!idPattern.test(idCard)) {\r\n            this.$message.error(`${visitorTitle}的身份证号格式不正确`);\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 检查身份证号是否与主访客重复\r\n        if (visitor.idNumber.trim().toUpperCase() === this.identityForm.idNumber.trim().toUpperCase()) {\r\n          this.$message.error(`${visitorTitle}与主访客的身份证号重复`);\r\n          return false;\r\n        }\r\n\r\n        // 检查身份证号是否与其他同行人员重复\r\n        for (let j = 0; j < this.companionList.length; j++) {\r\n          if (i !== j && visitor.idNumber.trim().toUpperCase() === this.companionList[j].idNumber.trim().toUpperCase()) {\r\n            this.$message.error(`${visitorTitle}与访客${j + 2}的身份证号重复`);\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 检查手机号是否与主访客重复\r\n        if (visitor.phone.trim() === this.identityForm.phone.trim()) {\r\n          this.$message.error(`${visitorTitle}与主访客的手机号重复，请确认是否为同一人`);\r\n          return false;\r\n        }\r\n\r\n        // 检查手机号是否与其他同行人员重复\r\n        for (let j = 0; j < this.companionList.length; j++) {\r\n          if (i !== j && visitor.phone.trim() === this.companionList[j].phone.trim()) {\r\n            this.$message.error(`${visitorTitle}与访客${j + 2}的手机号重复，请确认是否为同一人`);\r\n            return false;\r\n          }\r\n        }\r\n        \r\n        // 清理和标准化数据\r\n        visitor.name = visitor.name.trim();\r\n        visitor.phone = visitor.phone.trim();\r\n        visitor.idNumber = visitor.idNumber.trim().toUpperCase();\r\n        visitor.company = (visitor.company || '').trim();\r\n      }\r\n\r\n      return true;\r\n    },\r\n\r\n    /** 验证时间 */\r\n    validateTimes() {\r\n      if (!this.visitForm.plannedEntryDatetime) {\r\n        this.$message.error('请选择预计来访时间');\r\n        return false;\r\n      }\r\n\r\n      if (!this.visitForm.plannedExitDatetime) {\r\n        this.$message.error('请选择预计离开时间');\r\n        return false;\r\n      }\r\n\r\n      const arrivalTime = new Date(this.visitForm.plannedEntryDatetime);\r\n      const departureTime = new Date(this.visitForm.plannedExitDatetime);\r\n      const now = new Date();\r\n\r\n      // 验证来访时间不能是过去时间（允许当前时间前1小时的误差）\r\n      const oneHourBefore = new Date(now.getTime() - 60 * 60 * 1000);\r\n      if (arrivalTime < oneHourBefore) {\r\n        this.$message.error('预计来访时间不能是过去时间');\r\n        return false;\r\n      }\r\n\r\n      // 验证离开时间必须晚于来访时间\r\n      if (departureTime <= arrivalTime) {\r\n        this.$message.error('预计离开时间必须晚于来访时间');\r\n        return false;\r\n      }\r\n\r\n      return true;\r\n    },\r\n\r\n    /** 格式化日期为后端期望的格式 */\r\n    formatDateForBackend(dateStr) {\r\n      if (!dateStr) return '';\r\n      \r\n      const date = new Date(dateStr);\r\n      if (isNaN(date.getTime())) return '';\r\n      \r\n      const year = date.getFullYear();\r\n      const month = String(date.getMonth() + 1).padStart(2, '0');\r\n      const day = String(date.getDate()).padStart(2, '0');\r\n      const hours = String(date.getHours()).padStart(2, '0');\r\n      const minutes = String(date.getMinutes()).padStart(2, '0');\r\n      const seconds = String(date.getSeconds()).padStart(2, '0');\r\n      \r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n    },\r\n    \r\n    /** 重新登记 */\r\n    resetRegister() {\r\n      this.currentStep = 0;\r\n      this.resetVerifyDisplay();\r\n      this.verifyMethod = '';\r\n      this.identityForm = {\r\n        name: '',\r\n        phone: '',\r\n        idType: '',\r\n        idNumber: '',\r\n        company: ''\r\n      };\r\n      this.visitForm = {\r\n        reasonForVisit: '',\r\n        hostEmployeeName: '',\r\n        departmentVisited: '',\r\n        vehiclePlateNumber: '',\r\n        plannedEntryDatetime: null,\r\n        plannedExitDatetime: null\r\n      };\r\n      this.companionList = [];\r\n      this.registrationId = null;\r\n    },\r\n    \r\n    /** 打印凭证 */\r\n    printCredential() {\r\n      this.$message.info('正在生成打印文件...');\r\n      // TODO: 实现打印功能\r\n    },\r\n\r\n    /** 显示帮助 */\r\n    showHelp() {\r\n      this.$alert('如需帮助，请联系园区前台或拨打服务热线', '使用帮助', {\r\n        confirmButtonText: '确定'\r\n      });\r\n    },\r\n\r\n    /** 联系客服 */\r\n    contactService() {\r\n      this.$alert('服务热线：400-1234-567\\n服务时间：周一至周五 8:00-18:00', '联系客服', {\r\n        confirmButtonText: '确定'\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 主容器 */\r\n.pc-register-container {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n  font-family: 'Helvetica Neue', Arial, sans-serif;\r\n}\r\n\r\n/* 顶部导航 */\r\n.top-nav {\r\n  background: #fff;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  padding: 0 40px;\r\n  position: sticky;\r\n  top: 0;\r\n  z-index: 100;\r\n}\r\n\r\n.nav-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  height: 70px;\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.logo-section {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n}\r\n\r\n.nav-logo {\r\n  height: 40px;\r\n}\r\n\r\n.company-name {\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n.nav-actions {\r\n  display: flex;\r\n  gap: 20px;\r\n}\r\n\r\n/* 主内容区域 */\r\n.main-content {\r\n  display: flex;\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n  padding: 40px;\r\n  gap: 40px;\r\n  min-height: calc(100vh - 110px);\r\n}\r\n\r\n/* 左侧信息面板 */\r\n.info-panel {\r\n  flex: 0 0 400px;\r\n}\r\n\r\n.welcome-card {\r\n  background: #fff;\r\n  border-radius: 16px;\r\n  padding: 32px;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\r\n  height: fit-content;\r\n  position: sticky;\r\n  top: 110px;\r\n}\r\n\r\n.welcome-icon {\r\n  text-align: center;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.welcome-icon i {\r\n  font-size: 64px;\r\n  color: #3498db;\r\n}\r\n\r\n.welcome-card h2 {\r\n  text-align: center;\r\n  color: #2c3e50;\r\n  margin-bottom: 16px;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n}\r\n\r\n.welcome-desc {\r\n  text-align: center;\r\n  color: #7f8c8d;\r\n  margin-bottom: 32px;\r\n  font-size: 16px;\r\n  line-height: 1.6;\r\n}\r\n\r\n/* 流程步骤 */\r\n.process-steps {\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.step-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n  margin-bottom: 24px;\r\n  padding: 16px;\r\n  border-radius: 12px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.step-item.active {\r\n  background: linear-gradient(135deg, #3498db, #2980b9);\r\n  color: white;\r\n  transform: translateX(8px);\r\n}\r\n\r\n.step-item:not(.active) {\r\n  background: #f8f9fa;\r\n  color: #6c757d;\r\n}\r\n\r\n.step-circle {\r\n  width: 32px;\r\n  height: 32px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-weight: bold;\r\n  font-size: 14px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.step-item.active .step-circle {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  color: white;\r\n}\r\n\r\n.step-item:not(.active) .step-circle {\r\n  background: #dee2e6;\r\n  color: #6c757d;\r\n}\r\n\r\n.step-text h4 {\r\n  margin: 0 0 4px 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.step-text p {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  opacity: 0.8;\r\n}\r\n\r\n/* 安全提示 */\r\n.security-tips {\r\n  background: #f8f9fa;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  border-left: 4px solid #3498db;\r\n}\r\n\r\n.security-tips h4 {\r\n  margin: 0 0 12px 0;\r\n  color: #2c3e50;\r\n  font-size: 16px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.security-tips ul {\r\n  margin: 0;\r\n  padding-left: 20px;\r\n  color: #6c757d;\r\n}\r\n\r\n.security-tips li {\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n  line-height: 1.4;\r\n}\r\n\r\n/* 右侧表单面板 */\r\n.form-panel {\r\n  flex: 1;\r\n  background: #fff;\r\n  border-radius: 16px;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n/* 进度指示器 */\r\n.progress-indicator {\r\n  background: #f8f9fa;\r\n  padding: 24px 32px;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.progress-bar {\r\n  height: 6px;\r\n  background: #e9ecef;\r\n  border-radius: 3px;\r\n  overflow: hidden;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.progress-fill {\r\n  height: 100%;\r\n  background: linear-gradient(90deg, #3498db, #2980b9);\r\n  border-radius: 3px;\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.progress-text {\r\n  text-align: center;\r\n  color: #6c757d;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 表单内容 */\r\n.form-content {\r\n  padding: 32px;\r\n  max-height: calc(100vh - 300px);\r\n  overflow-y: auto;\r\n}\r\n\r\n.form-header {\r\n  text-align: center;\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.form-header h3 {\r\n  color: #2c3e50;\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  margin-bottom: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 12px;\r\n}\r\n\r\n.form-header p {\r\n  color: #7f8c8d;\r\n  margin: 0;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 验证方式网格 */\r\n.verify-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 20px;\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.verify-card {\r\n  position: relative;\r\n  background: #f8f9fa;\r\n  border: 2px solid #e9ecef;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.verify-card:hover {\r\n  border-color: #3498db;\r\n  background: #fff;\r\n  box-shadow: 0 4px 16px rgba(52, 152, 219, 0.15);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.verify-card.selected {\r\n  border-color: #3498db;\r\n  background: linear-gradient(135deg, #3498db, #2980b9);\r\n  color: white;\r\n  box-shadow: 0 8px 24px rgba(52, 152, 219, 0.3);\r\n}\r\n\r\n.verify-icon {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.verify-icon i {\r\n  font-size: 48px;\r\n  color: #3498db;\r\n}\r\n\r\n.verify-card.selected .verify-icon i {\r\n  color: white;\r\n}\r\n\r\n.verify-card h4 {\r\n  margin: 0 0 8px 0;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.verify-card p {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  opacity: 0.8;\r\n}\r\n\r\n.verify-status {\r\n  position: absolute;\r\n  top: 12px;\r\n  right: 12px;\r\n  width: 24px;\r\n  height: 24px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n/* 验证操作区域 */\r\n.verify-operation {\r\n  margin-top: 32px;\r\n  padding: 24px;\r\n  background: #f8f9fa;\r\n  border-radius: 12px;\r\n}\r\n\r\n.operation-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.operation-header h4 {\r\n  margin: 0;\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n/* 身份表单 */\r\n.identity-form {\r\n  background: white;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n}\r\n\r\n/* 身份证读卡器 */\r\n.id-card-reader {\r\n  text-align: center;\r\n}\r\n\r\n.reader-visual {\r\n  background: white;\r\n  padding: 40px;\r\n  border-radius: 12px;\r\n}\r\n\r\n.reader-animation {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.rotating {\r\n  animation: rotate 2s linear infinite;\r\n}\r\n\r\n@keyframes rotate {\r\n  from { transform: rotate(0deg); }\r\n  to { transform: rotate(360deg); }\r\n}\r\n\r\n.reader-animation i {\r\n  font-size: 64px;\r\n  color: #3498db;\r\n}\r\n\r\n.reader-visual h4 {\r\n  color: #2c3e50;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.reader-visual p {\r\n  color: #7f8c8d;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.reader-tips {\r\n  margin: 24px 0;\r\n}\r\n\r\n/* 人脸识别 */\r\n.face-recognition {\r\n  text-align: center;\r\n}\r\n\r\n.camera-container {\r\n  background: white;\r\n  padding: 40px;\r\n  border-radius: 12px;\r\n}\r\n\r\n.camera-frame {\r\n  position: relative;\r\n  width: 280px;\r\n  height: 210px;\r\n  margin: 0 auto 24px;\r\n  border: 3px solid #3498db;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #f8f9fa;\r\n  overflow: hidden;\r\n}\r\n\r\n.camera-overlay {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 16px;\r\n}\r\n\r\n.face-outline {\r\n  width: 120px;\r\n  height: 150px;\r\n  border: 2px dashed #3498db;\r\n  border-radius: 60px;\r\n  opacity: 0.6;\r\n}\r\n\r\n.camera-icon {\r\n  font-size: 32px;\r\n  color: #3498db;\r\n}\r\n\r\n.scanning-line {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 2px;\r\n  background: linear-gradient(90deg, transparent, #3498db, transparent);\r\n  animation: scan 2s ease-in-out infinite;\r\n}\r\n\r\n@keyframes scan {\r\n  0% { transform: translateY(0); }\r\n  50% { transform: translateY(206px); }\r\n  100% { transform: translateY(0); }\r\n}\r\n\r\n/* 表单分组 */\r\n.form-section {\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.section-title {\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin-bottom: 16px;\r\n  padding-bottom: 8px;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n/* 同行人员 */\r\n.no-companions {\r\n  text-align: center;\r\n  padding: 40px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.no-companions i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  color: #bdc3c7;\r\n}\r\n\r\n.companions-table {\r\n  background: #f8f9fa;\r\n  padding: 16px;\r\n  border-radius: 8px;\r\n}\r\n\r\n/* 成功页面 */\r\n.success-content {\r\n  text-align: center;\r\n}\r\n\r\n.success-icon {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.success-icon i {\r\n  font-size: 80px;\r\n  color: #27ae60;\r\n}\r\n\r\n.success-content h4 {\r\n  color: #2c3e50;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.success-message {\r\n  color: #7f8c8d;\r\n  font-size: 16px;\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.register-summary {\r\n  background: #f8f9fa;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n  margin-bottom: 32px;\r\n  text-align: left;\r\n}\r\n\r\n.register-summary h5 {\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin-bottom: 16px;\r\n  text-align: center;\r\n}\r\n\r\n.access-credential {\r\n  background: #f8f9fa;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n  text-align: left;\r\n}\r\n\r\n.access-credential h5 {\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin-bottom: 16px;\r\n  text-align: center;\r\n}\r\n\r\n.credential-card {\r\n  display: flex;\r\n  gap: 24px;\r\n  background: white;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n  border: 2px solid #e9ecef;\r\n}\r\n\r\n.qr-code-container {\r\n  flex: 0 0 120px;\r\n  text-align: center;\r\n}\r\n\r\n.qr-code {\r\n  width: 120px;\r\n  height: 120px;\r\n  border: 2px solid #e9ecef;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #f8f9fa;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.qr-code i {\r\n  font-size: 48px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.qr-code-id {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n  font-family: monospace;\r\n  margin: 0;\r\n}\r\n\r\n.credential-info {\r\n  flex: 1;\r\n}\r\n\r\n.credential-info h6 {\r\n  color: #2c3e50;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.credential-info ul {\r\n  margin: 0;\r\n  padding-left: 20px;\r\n  color: #6c757d;\r\n}\r\n\r\n.credential-info li {\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n  line-height: 1.4;\r\n}\r\n\r\n/* 底部操作按钮 */\r\n.form-actions {\r\n  padding: 24px 32px;\r\n  background: #f8f9fa;\r\n  border-top: 1px solid #e9ecef;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.final-actions {\r\n  display: flex;\r\n  gap: 16px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n  .main-content {\r\n    flex-direction: column;\r\n    gap: 24px;\r\n  }\r\n  \r\n  .info-panel {\r\n    flex: none;\r\n  }\r\n  \r\n  .welcome-card {\r\n    position: static;\r\n  }\r\n  \r\n  .verify-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .main-content {\r\n    padding: 20px;\r\n  }\r\n  \r\n  .nav-content {\r\n    padding: 0 20px;\r\n  }\r\n  \r\n  .company-name {\r\n    font-size: 16px;\r\n  }\r\n  \r\n  .form-content {\r\n    padding: 20px;\r\n  }\r\n  \r\n  .credential-card {\r\n    flex-direction: column;\r\n    text-align: center;\r\n  }\r\n  \r\n  .form-actions {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n}\r\n\r\n/* Element UI 样式覆盖 */\r\n.el-form-item {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.el-input__inner {\r\n  height: 44px;\r\n  border-radius: 8px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.el-input__inner:focus {\r\n  border-color: #3498db;\r\n  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.1);\r\n}\r\n\r\n.el-select {\r\n  width: 100%;\r\n}\r\n\r\n.el-date-editor {\r\n  width: 100%;\r\n}\r\n\r\n.el-button--large {\r\n  height: 44px;\r\n  padding: 12px 24px;\r\n  border-radius: 8px;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n}\r\n\r\n.el-button--primary {\r\n  background: linear-gradient(135deg, #3498db, #2980b9);\r\n  border: none;\r\n}\r\n\r\n.el-button--primary:hover {\r\n  background: linear-gradient(135deg, #2980b9, #1f4e79);\r\n}\r\n\r\n.el-descriptions {\r\n  border-radius: 8px;\r\n}\r\n\r\n.el-table {\r\n  border-radius: 8px;\r\n}\r\n\r\n.el-alert {\r\n  border-radius: 8px;\r\n}\r\n</style>\r\n"]}]}