{"remainingRequest": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\views\\asc\\visitor\\self-register.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\views\\asc\\visitor\\self-register.vue", "mtime": 1750986040997}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750836929039}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750836940534}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750836929039}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750836957095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBzdWJtaXRWaXNpdG9yUmVnaXN0cmF0aW9uIH0gZnJvbSAiQC9hcGkvYXNjL3Zpc2l0b3IiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJTZWxmUmVnaXN0ZXIiLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBzdWJtaXR0aW5nOiBmYWxzZSwNCiAgICAgIHJlZ2lzdHJhdGlvbkNvbXBsZXRlZDogZmFsc2UsDQoNCiAgICAgIC8vIOihqOWNleaVsOaNriAtIOS4juW+ruS/oeerr+S/neaMgeS4gOiHtOeahOe7k+aehA0KICAgICAgZm9ybURhdGE6IHsNCiAgICAgICAgLy8g6K6/5a6i5YiX6KGo77yI56ys5LiA5L2N5Li65Li76IGU57O75Lq677yJDQogICAgICAgIHZpc2l0b3JzOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgbmFtZTogJycsDQogICAgICAgICAgICBwaG9uZTogJycsDQogICAgICAgICAgICBpZENhcmQ6ICcnLA0KICAgICAgICAgICAgY29tcGFueTogJycsDQogICAgICAgICAgICBpc01haW5Db250YWN0OiB0cnVlDQogICAgICAgICAgfQ0KICAgICAgICBdLA0KICAgICAgICAvLyDmnaXorr/kv6Hmga8NCiAgICAgICAgdmlzaXRJbmZvOiB7DQogICAgICAgICAgcmVhc29uRm9yVmlzaXQ6ICcnLA0KICAgICAgICAgIGhvc3RFbXBsb3llZU5hbWU6ICcnLA0KICAgICAgICAgIGRlcGFydG1lbnRWaXNpdGVkOiAnJywNCiAgICAgICAgICB2ZWhpY2xlUGxhdGVOdW1iZXI6ICcnLA0KICAgICAgICAgIHBsYW5uZWRFbnRyeURhdGV0aW1lOiAnJywNCiAgICAgICAgICBwbGFubmVkRXhpdERhdGV0aW1lOiAnJw0KICAgICAgICB9DQogICAgICB9LA0KDQogICAgICAvLyDooajljZXpqozor4Hop4TliJkgLSDkuI7lvq7kv6Hnq6/kv53mjIHkuIDoh7QNCiAgICAgIHZpc2l0UnVsZXM6IHsNCiAgICAgICAgJ3Zpc2l0SW5mby5yZWFzb25Gb3JWaXNpdCc6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5p2l6K6/5LqL55SxJywgdHJpZ2dlcjogJ2JsdXInIH0sDQogICAgICAgICAgeyBtaW46IDIsIG1heDogMjAwLCBtZXNzYWdlOiAn5p2l6K6/5LqL55Sx6ZW/5bqm5bqU5ZyoMi0yMDDkuKrlrZfnrKbkuYvpl7QnLCB0cmlnZ2VyOiAnYmx1cicgfQ0KICAgICAgICBdLA0KICAgICAgICAndmlzaXRJbmZvLmhvc3RFbXBsb3llZU5hbWUnOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeiiq+iuv+S6uuWnk+WQjScsIHRyaWdnZXI6ICdibHVyJyB9LA0KICAgICAgICAgIHsgbWluOiAyLCBtYXg6IDIwLCBtZXNzYWdlOiAn6KKr6K6/5Lq65aeT5ZCN6ZW/5bqm5bqU5ZyoMi0yMOS4quWtl+espuS5i+mXtCcsIHRyaWdnZXI6ICdibHVyJyB9DQogICAgICAgIF0sDQogICAgICAgICd2aXNpdEluZm8uZGVwYXJ0bWVudFZpc2l0ZWQnOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeiiq+iuv+mDqOmXqCcsIHRyaWdnZXI6ICdibHVyJyB9LA0KICAgICAgICAgIHsgbWluOiAyLCBtYXg6IDUwLCBtZXNzYWdlOiAn6YOo6Zeo5ZCN56ew6ZW/5bqm5bqU5ZyoMi01MOS4quWtl+espuS5i+mXtCcsIHRyaWdnZXI6ICdibHVyJyB9DQogICAgICAgIF0sDQogICAgICAgICd2aXNpdEluZm8ucGxhbm5lZEVudHJ5RGF0ZXRpbWUnOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqemihOiuoeadpeiuv+aXtumXtCcsIHRyaWdnZXI6ICdjaGFuZ2UnIH0NCiAgICAgICAgXSwNCiAgICAgICAgJ3Zpc2l0SW5mby5wbGFubmVkRXhpdERhdGV0aW1lJzogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6npooTorqHnprvlvIDml7bpl7QnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9DQogICAgICAgIF0sDQogICAgICAgIC8vIOWKqOaAgeiuv+WuoumqjOivgeinhOWImQ0KICAgICAgICAndmlzaXRvcnMuMC5uYW1lJzogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXkuLvogZTns7vkurrlp5PlkI0nLCB0cmlnZ2VyOiAnYmx1cicgfSwNCiAgICAgICAgICB7IG1pbjogMiwgbWF4OiAyMCwgbWVzc2FnZTogJ+Wnk+WQjemVv+W6puW6lOWcqDItMjDkuKrlrZfnrKbkuYvpl7QnLCB0cmlnZ2VyOiAnYmx1cicgfQ0KICAgICAgICBdLA0KICAgICAgICAndmlzaXRvcnMuMC5waG9uZSc6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5Li76IGU57O75Lq65omL5py65Y+3JywgdHJpZ2dlcjogJ2JsdXInIH0sDQogICAgICAgICAgeyBwYXR0ZXJuOiAvXjFbMy05XVxkezl9JC8sIG1lc3NhZ2U6ICfor7fovpPlhaXmraPnoa7nmoTmiYvmnLrlj7cnLCB0cmlnZ2VyOiAnYmx1cicgfQ0KICAgICAgICBdLA0KICAgICAgICAndmlzaXRvcnMuMC5pZENhcmQnOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeS4u+iBlOezu+S6uui6q+S7veivgeWPtycsIHRyaWdnZXI6ICdibHVyJyB9DQogICAgICAgIF0NCiAgICAgIH0sDQoNCiAgICAgIC8vIOeZu+iusOe7k+aenA0KICAgICAgcmVnaXN0cmF0aW9uSWQ6IG51bGwsDQoNCiAgICAgIC8vIOaXtumXtOmAieaLqeWZqOmFjee9rg0KICAgICAgZW50cnlQaWNrZXJPcHRpb25zOiB7DQogICAgICAgIHNob3J0Y3V0czogW3sNCiAgICAgICAgICB0ZXh0OiAn546w5ZyoJywNCiAgICAgICAgICBvbkNsaWNrKHBpY2tlcikgew0KICAgICAgICAgICAgcGlja2VyLiRlbWl0KCdwaWNrJywgbmV3IERhdGUoKSk7DQogICAgICAgICAgfQ0KICAgICAgICB9LCB7DQogICAgICAgICAgdGV4dDogJzHlsI/ml7blkI4nLA0KICAgICAgICAgIG9uQ2xpY2socGlja2VyKSB7DQogICAgICAgICAgICBjb25zdCBkYXRlID0gbmV3IERhdGUoKTsNCiAgICAgICAgICAgIGRhdGUuc2V0VGltZShkYXRlLmdldFRpbWUoKSArIDM2MDAgKiAxMDAwKTsNCiAgICAgICAgICAgIHBpY2tlci4kZW1pdCgncGljaycsIGRhdGUpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSwgew0KICAgICAgICAgIHRleHQ6ICfmmI7lpKnkuIrljYg554K5JywNCiAgICAgICAgICBvbkNsaWNrKHBpY2tlcikgew0KICAgICAgICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKCk7DQogICAgICAgICAgICBkYXRlLnNldERhdGUoZGF0ZS5nZXREYXRlKCkgKyAxKTsNCiAgICAgICAgICAgIGRhdGUuc2V0SG91cnMoOSwgMCwgMCwgMCk7DQogICAgICAgICAgICBwaWNrZXIuJGVtaXQoJ3BpY2snLCBkYXRlKTsNCiAgICAgICAgICB9DQogICAgICAgIH1dLA0KICAgICAgICBkaXNhYmxlZERhdGUodGltZSkgew0KICAgICAgICAgIC8vIOWFgeiuuOmAieaLqeW9k+WJjeaXtumXtOWJjTHlsI/ml7bliLDmnKrmnaUzMOWkqQ0KICAgICAgICAgIGNvbnN0IG9uZUhvdXJCZWZvcmUgPSBEYXRlLm5vdygpIC0gMzYwMCAqIDEwMDA7DQogICAgICAgICAgY29uc3QgdGhpcnR5RGF5c0xhdGVyID0gRGF0ZS5ub3coKSArIDMwICogMjQgKiAzNjAwICogMTAwMDsNCiAgICAgICAgICByZXR1cm4gdGltZS5nZXRUaW1lKCkgPCBvbmVIb3VyQmVmb3JlIHx8IHRpbWUuZ2V0VGltZSgpID4gdGhpcnR5RGF5c0xhdGVyOw0KICAgICAgICB9DQogICAgICB9LA0KDQogICAgICBleGl0UGlja2VyT3B0aW9uczogew0KICAgICAgICBzaG9ydGN1dHM6IFt7DQogICAgICAgICAgdGV4dDogJzLlsI/ml7blkI4nLA0KICAgICAgICAgIG9uQ2xpY2socGlja2VyKSB7DQogICAgICAgICAgICBjb25zdCBkYXRlID0gbmV3IERhdGUoKTsNCiAgICAgICAgICAgIGRhdGUuc2V0VGltZShkYXRlLmdldFRpbWUoKSArIDIgKiAzNjAwICogMTAwMCk7DQogICAgICAgICAgICBwaWNrZXIuJGVtaXQoJ3BpY2snLCBkYXRlKTsNCiAgICAgICAgICB9DQogICAgICAgIH0sIHsNCiAgICAgICAgICB0ZXh0OiAn5LuK5aSp5LiL5Y2INueCuScsDQogICAgICAgICAgb25DbGljayhwaWNrZXIpIHsNCiAgICAgICAgICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZSgpOw0KICAgICAgICAgICAgZGF0ZS5zZXRIb3VycygxOCwgMCwgMCwgMCk7DQogICAgICAgICAgICBpZiAoZGF0ZS5nZXRUaW1lKCkgPCBEYXRlLm5vdygpKSB7DQogICAgICAgICAgICAgIGRhdGUuc2V0RGF0ZShkYXRlLmdldERhdGUoKSArIDEpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgcGlja2VyLiRlbWl0KCdwaWNrJywgZGF0ZSk7DQogICAgICAgICAgfQ0KICAgICAgICB9LCB7DQogICAgICAgICAgdGV4dDogJ+aYjuWkqeS4i+WNiDbngrknLA0KICAgICAgICAgIG9uQ2xpY2socGlja2VyKSB7DQogICAgICAgICAgICBjb25zdCBkYXRlID0gbmV3IERhdGUoKTsNCiAgICAgICAgICAgIGRhdGUuc2V0RGF0ZShkYXRlLmdldERhdGUoKSArIDEpOw0KICAgICAgICAgICAgZGF0ZS5zZXRIb3VycygxOCwgMCwgMCwgMCk7DQogICAgICAgICAgICBwaWNrZXIuJGVtaXQoJ3BpY2snLCBkYXRlKTsNCiAgICAgICAgICB9DQogICAgICAgIH1dLA0KICAgICAgICBkaXNhYmxlZERhdGUodGltZSkgew0KICAgICAgICAgIC8vIOWFgeiuuOmAieaLqeW9k+WJjeaXtumXtOWIsOacquadpTMw5aSpDQogICAgICAgICAgY29uc3QgdGhpcnR5RGF5c0xhdGVyID0gRGF0ZS5ub3coKSArIDMwICogMjQgKiAzNjAwICogMTAwMDsNCiAgICAgICAgICByZXR1cm4gdGltZS5nZXRUaW1lKCkgPCBEYXRlLm5vdygpIC0gOC42NGU3IHx8IHRpbWUuZ2V0VGltZSgpID4gdGhpcnR5RGF5c0xhdGVyOw0KICAgICAgICB9DQogICAgICB9DQogICAgfTsNCiAgfSwNCg0KICBjb21wdXRlZDogew0KICAgIC8vIOaAu+iuv+WuouS6uuaVsA0KICAgIHRvdGFsVmlzaXRvcnMoKSB7DQogICAgICByZXR1cm4gdGhpcy5mb3JtRGF0YS52aXNpdG9ycy5sZW5ndGg7DQogICAgfSwNCg0KICAgIC8vIOS4u+iBlOezu+S6uuS/oeaBrw0KICAgIG1haW5Db250YWN0KCkgew0KICAgICAgcmV0dXJuIHRoaXMuZm9ybURhdGEudmlzaXRvcnNbMF0gfHwge307DQogICAgfQ0KICB9LA0KDQogIG1ldGhvZHM6IHsNCiAgICAvLyDmt7vliqDorr/lrqINCiAgICBhZGRWaXNpdG9yKCkgew0KICAgICAgaWYgKHRoaXMuZm9ybURhdGEudmlzaXRvcnMubGVuZ3RoID49IDEwKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn5pyA5aSa5Y+q6IO95re75YqgMTDlkI3orr/lrqInKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICB0aGlzLmZvcm1EYXRhLnZpc2l0b3JzLnB1c2goew0KICAgICAgICBuYW1lOiAnJywNCiAgICAgICAgcGhvbmU6ICcnLA0KICAgICAgICBpZENhcmQ6ICcnLA0KICAgICAgICBjb21wYW55OiAnJywNCiAgICAgICAgaXNNYWluQ29udGFjdDogZmFsc2UNCiAgICAgIH0pOw0KDQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+W3sua3u+WKoOiuv+WuoicpOw0KICAgIH0sDQoNCiAgICAvLyDnp7vpmaTorr/lrqINCiAgICByZW1vdmVWaXNpdG9yKGluZGV4KSB7DQogICAgICBpZiAoaW5kZXggPT09IDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfkuI3og73liKDpmaTkuLvogZTns7vkuronKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICB0aGlzLmZvcm1EYXRhLnZpc2l0b3JzLnNwbGljZShpbmRleCwgMSk7DQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+W3suenu+mZpOiuv+WuoicpOw0KICAgIH0sDQoNCiAgICAvLyDmoLzlvI/ljJbml6XmnJ/kuLrlkI7nq6/mnJ/mnJvnmoTmoLzlvI8NCiAgICBmb3JtYXREYXRlRm9yQmFja2VuZChkYXRlU3RyKSB7DQogICAgICBpZiAoIWRhdGVTdHIpIHJldHVybiAnJzsNCg0KICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKGRhdGVTdHIpOw0KICAgICAgaWYgKGlzTmFOKGRhdGUuZ2V0VGltZSgpKSkgcmV0dXJuICcnOw0KDQogICAgICBjb25zdCB5ZWFyID0gZGF0ZS5nZXRGdWxsWWVhcigpOw0KICAgICAgY29uc3QgbW9udGggPSBTdHJpbmcoZGF0ZS5nZXRNb250aCgpICsgMSkucGFkU3RhcnQoMiwgJzAnKTsNCiAgICAgIGNvbnN0IGRheSA9IFN0cmluZyhkYXRlLmdldERhdGUoKSkucGFkU3RhcnQoMiwgJzAnKTsNCiAgICAgIGNvbnN0IGhvdXJzID0gU3RyaW5nKGRhdGUuZ2V0SG91cnMoKSkucGFkU3RhcnQoMiwgJzAnKTsNCiAgICAgIGNvbnN0IG1pbnV0ZXMgPSBTdHJpbmcoZGF0ZS5nZXRNaW51dGVzKCkpLnBhZFN0YXJ0KDIsICcwJyk7DQogICAgICBjb25zdCBzZWNvbmRzID0gU3RyaW5nKGRhdGUuZ2V0U2Vjb25kcygpKS5wYWRTdGFydCgyLCAnMCcpOw0KDQogICAgICByZXR1cm4gYCR7eWVhcn0tJHttb250aH0tJHtkYXl9ICR7aG91cnN9OiR7bWludXRlc306JHtzZWNvbmRzfWA7DQogICAgfSwNCg0KICAgIC8qKiDmnaXorr/ml7bpl7Tlj5jmm7Tkuovku7YgKi8NCiAgICBvbkFycml2YWxUaW1lQ2hhbmdlKHZhbHVlKSB7DQogICAgICBjb25zb2xlLmxvZygn6aKE6K6h5p2l6K6/5pe26Ze05Y+Y5pu0OicsIHZhbHVlKTsNCiAgICAgIC8vIOiHquWKqOiuvue9ruemu+W8gOaXtumXtOS4uuadpeiuv+aXtumXtOWQjjTlsI/ml7YNCiAgICAgIGlmICh2YWx1ZSAmJiAhdGhpcy5mb3JtRGF0YS52aXNpdEluZm8ucGxhbm5lZEV4aXREYXRldGltZSkgew0KICAgICAgICBjb25zdCBhcnJpdmFsVGltZSA9IG5ldyBEYXRlKHZhbHVlKTsNCiAgICAgICAgY29uc3QgZGVwYXJ0dXJlVGltZSA9IG5ldyBEYXRlKGFycml2YWxUaW1lLmdldFRpbWUoKSArIDQgKiA2MCAqIDYwICogMTAwMCk7DQoNCiAgICAgICAgY29uc3QgeWVhciA9IGRlcGFydHVyZVRpbWUuZ2V0RnVsbFllYXIoKTsNCiAgICAgICAgY29uc3QgbW9udGggPSBTdHJpbmcoZGVwYXJ0dXJlVGltZS5nZXRNb250aCgpICsgMSkucGFkU3RhcnQoMiwgJzAnKTsNCiAgICAgICAgY29uc3QgZGF5ID0gU3RyaW5nKGRlcGFydHVyZVRpbWUuZ2V0RGF0ZSgpKS5wYWRTdGFydCgyLCAnMCcpOw0KICAgICAgICBjb25zdCBob3VycyA9IFN0cmluZyhkZXBhcnR1cmVUaW1lLmdldEhvdXJzKCkpLnBhZFN0YXJ0KDIsICcwJyk7DQogICAgICAgIGNvbnN0IG1pbnV0ZXMgPSBTdHJpbmcoZGVwYXJ0dXJlVGltZS5nZXRNaW51dGVzKCkpLnBhZFN0YXJ0KDIsICcwJyk7DQogICAgICAgIGNvbnN0IHNlY29uZHMgPSBTdHJpbmcoZGVwYXJ0dXJlVGltZS5nZXRTZWNvbmRzKCkpLnBhZFN0YXJ0KDIsICcwJyk7DQoNCiAgICAgICAgdGhpcy5mb3JtRGF0YS52aXNpdEluZm8ucGxhbm5lZEV4aXREYXRldGltZSA9IGAke3llYXJ9LSR7bW9udGh9LSR7ZGF5fSAke2hvdXJzfToke21pbnV0ZXN9OiR7c2Vjb25kc31gOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog56a75byA5pe26Ze05Y+Y5pu05LqL5Lu2ICovDQogICAgb25EZXBhcnR1cmVUaW1lQ2hhbmdlKHZhbHVlKSB7DQogICAgICBjb25zb2xlLmxvZygn6aKE6K6h56a75byA5pe26Ze05Y+Y5pu0OicsIHZhbHVlKTsNCiAgICAgIC8vIOmqjOivgeemu+W8gOaXtumXtOS4jeiDveaXqeS6juadpeiuv+aXtumXtA0KICAgICAgaWYgKHZhbHVlICYmIHRoaXMuZm9ybURhdGEudmlzaXRJbmZvLnBsYW5uZWRFbnRyeURhdGV0aW1lKSB7DQogICAgICAgIGNvbnN0IGFycml2YWxUaW1lID0gbmV3IERhdGUodGhpcy5mb3JtRGF0YS52aXNpdEluZm8ucGxhbm5lZEVudHJ5RGF0ZXRpbWUpOw0KICAgICAgICBjb25zdCBkZXBhcnR1cmVUaW1lID0gbmV3IERhdGUodmFsdWUpOw0KDQogICAgICAgIGlmIChkZXBhcnR1cmVUaW1lIDw9IGFycml2YWxUaW1lKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfpooTorqHnprvlvIDml7bpl7TkuI3og73ml6nkuo7miJbnrYnkuo7mnaXorr/ml7bpl7QnKTsNCiAgICAgICAgICB0aGlzLmZvcm1EYXRhLnZpc2l0SW5mby5wbGFubmVkRXhpdERhdGV0aW1lID0gJyc7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOaPkOS6pOihqOWNlSAqLw0KICAgIGFzeW5jIHN1Ym1pdEZvcm0oKSB7DQogICAgICB0cnkgew0KICAgICAgICAvLyDpqozor4HooajljZUNCiAgICAgICAgYXdhaXQgdGhpcy4kcmVmcy52aXNpdEZvcm0udmFsaWRhdGUoKTsNCg0KICAgICAgICAvLyDpqozor4Horr/lrqLkv6Hmga8NCiAgICAgICAgaWYgKCF0aGlzLnZhbGlkYXRlVmlzaXRvcnMoKSkgew0KICAgICAgICAgIHJldHVybjsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOmqjOivgeaXtumXtA0KICAgICAgICBpZiAoIXRoaXMudmFsaWRhdGVUaW1lcygpKSB7DQogICAgICAgICAgcmV0dXJuOw0KICAgICAgICB9DQoNCiAgICAgICAgdGhpcy5zdWJtaXR0aW5nID0gdHJ1ZTsNCg0KICAgICAgICAvLyDojrflj5bkuLvogZTns7vkurrkv6Hmga8NCiAgICAgICAgY29uc3QgbWFpbkNvbnRhY3QgPSB0aGlzLmZvcm1EYXRhLnZpc2l0b3JzWzBdOw0KDQogICAgICAgIGNvbnN0IHN1Ym1pdERhdGEgPSB7DQogICAgICAgICAgLy8gVmlzaXRSZWdpc3RyYXRpb25zIOWvueixoQ0KICAgICAgICAgIHJlZ2lzdHJhdGlvbjogew0KICAgICAgICAgICAgcHJpbWFyeUNvbnRhY3ROYW1lOiBtYWluQ29udGFjdC5uYW1lLnRyaW0oKSwNCiAgICAgICAgICAgIHByaW1hcnlDb250YWN0UGhvbmU6IG1haW5Db250YWN0LnBob25lLnRyaW0oKSwNCiAgICAgICAgICAgIHJlYXNvbkZvclZpc2l0OiB0aGlzLmZvcm1EYXRhLnZpc2l0SW5mby5yZWFzb25Gb3JWaXNpdCwNCiAgICAgICAgICAgIGhvc3RFbXBsb3llZU5hbWU6IHRoaXMuZm9ybURhdGEudmlzaXRJbmZvLmhvc3RFbXBsb3llZU5hbWUsDQogICAgICAgICAgICBob3N0RW1wbG95ZWVJZDogbnVsbCwgLy8g5YmN56uv5pqC5pe25rKh5pyJ6KKr6K6/5Lq6SUTvvIzlkI7nq6/lj6/og73kvJroh6rliqjlpITnkIYNCiAgICAgICAgICAgIGRlcGFydG1lbnRWaXNpdGVkOiB0aGlzLmZvcm1EYXRhLnZpc2l0SW5mby5kZXBhcnRtZW50VmlzaXRlZCwNCiAgICAgICAgICAgIHBsYW5uZWRFbnRyeURhdGV0aW1lOiB0aGlzLmZvcm1hdERhdGVGb3JCYWNrZW5kKHRoaXMuZm9ybURhdGEudmlzaXRJbmZvLnBsYW5uZWRFbnRyeURhdGV0aW1lKSwNCiAgICAgICAgICAgIHBsYW5uZWRFeGl0RGF0ZXRpbWU6IHRoaXMuZm9ybWF0RGF0ZUZvckJhY2tlbmQodGhpcy5mb3JtRGF0YS52aXNpdEluZm8ucGxhbm5lZEV4aXREYXRldGltZSksDQogICAgICAgICAgICB2ZWhpY2xlUGxhdGVOdW1iZXI6IHRoaXMuZm9ybURhdGEudmlzaXRJbmZvLnZlaGljbGVQbGF0ZU51bWJlciB8fCAnJywNCiAgICAgICAgICAgIHRvdGFsQ29tcGFuaW9uczogdGhpcy5mb3JtRGF0YS52aXNpdG9ycy5sZW5ndGggLSAxDQogICAgICAgICAgfSwNCiAgICAgICAgICAvLyBSZWdpc3RyYXRpb25BdHRlbmRlZXMg5pWw57uEIC0g5L2/55So5ZCO56uv5pyf5pyb55qE5Li05pe25a2X5q61DQogICAgICAgICAgYXR0ZW5kZWVzTGlzdDogdGhpcy5mb3JtRGF0YS52aXNpdG9ycy5tYXAoKHZpc2l0b3IsIGluZGV4KSA9PiAoew0KICAgICAgICAgICAgdmlzaXRvck5hbWU6IHZpc2l0b3IubmFtZS50cmltKCksDQogICAgICAgICAgICB2aXNpdG9yUGhvbmU6IHZpc2l0b3IucGhvbmUudHJpbSgpLA0KICAgICAgICAgICAgdmlzaXRvcklkQ2FyZDogdmlzaXRvci5pZENhcmQudHJpbSgpLnRvVXBwZXJDYXNlKCksDQogICAgICAgICAgICB2aXNpdG9yQ29tcGFueTogKHZpc2l0b3IuY29tcGFueSB8fCAnJykudHJpbSgpLA0KICAgICAgICAgICAgaXNQcmltYXJ5OiBpbmRleCA9PT0gMCA/ICIxIiA6ICIwIiwgLy8g56ys5LiA5Liq6K6/5a6i5b+F6aG75piv5Li76IGU57O75Lq6DQogICAgICAgICAgICB2aXNpdG9yQXZhdGFyUGhvdG86IG51bGwgLy8g5pqC5pe25LiN5pSv5oyB5aS05YOP5LiK5LygDQogICAgICAgICAgfSkpDQogICAgICAgIH07DQoNCiAgICAgICAgY29uc29sZS5sb2coJ+aPkOS6pOaVsOaNrue7k+aehDonLCBKU09OLnN0cmluZ2lmeShzdWJtaXREYXRhLCBudWxsLCAyKSk7DQoNCiAgICAgICAgLy8g6LCD55SoQVBJ5o+Q5Lqk5pWw5o2uDQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgc3VibWl0VmlzaXRvclJlZ2lzdHJhdGlvbihzdWJtaXREYXRhKTsNCg0KICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgdGhpcy5yZWdpc3RyYXRpb25JZCA9IHJlc3BvbnNlLmRhdGEgfHwgJ1ZSJyArIERhdGUubm93KCk7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGAke3RoaXMudG90YWxWaXNpdG9yc33lkI3orr/lrqLnmbvorrDmiJDlip/vvIFgKTsNCiAgICAgICAgICB0aGlzLnJlZ2lzdHJhdGlvbkNvbXBsZXRlZCA9IHRydWU7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5tc2cgfHwgJ+eZu+iusOWksei0pe+8jOivt+mHjeivlScpOw0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfmj5DkuqTooajljZXlpLHotKU6JywgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfnmbvorrDlpLHotKXvvIzor7fmo4Dmn6XnvZHnu5zov57mjqUnKTsNCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIHRoaXMuc3VibWl0dGluZyA9IGZhbHNlOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDpqozor4Horr/lrqLkv6Hmga8NCiAgICB2YWxpZGF0ZVZpc2l0b3JzKCkgew0KICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0aGlzLmZvcm1EYXRhLnZpc2l0b3JzLmxlbmd0aDsgaSsrKSB7DQogICAgICAgIGNvbnN0IHZpc2l0b3IgPSB0aGlzLmZvcm1EYXRhLnZpc2l0b3JzW2ldOw0KICAgICAgICBjb25zdCB2aXNpdG9yVGl0bGUgPSBpID09PSAwID8gJ+S4u+iBlOezu+S6uicgOiBg6K6/5a6iJHtpICsgMX1gOw0KDQogICAgICAgIC8vIOmqjOivgeWnk+WQjQ0KICAgICAgICBpZiAoIXZpc2l0b3IubmFtZSB8fCB2aXNpdG9yLm5hbWUudHJpbSgpID09PSAnJykgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoYOivt+i+k+WFpSR7dmlzaXRvclRpdGxlfeeahOWnk+WQjWApOw0KICAgICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgICAgfQ0KDQogICAgICAgIGlmICh2aXNpdG9yLm5hbWUudHJpbSgpLmxlbmd0aCA8IDIgfHwgdmlzaXRvci5uYW1lLnRyaW0oKS5sZW5ndGggPiAyMCkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoYCR7dmlzaXRvclRpdGxlfeeahOWnk+WQjemVv+W6puW6lOWcqDItMjDkuKrlrZfnrKbkuYvpl7RgKTsNCiAgICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICAgIH0NCg0KICAgICAgICAvLyDpqozor4HmiYvmnLrlj7cNCiAgICAgICAgaWYgKCF2aXNpdG9yLnBob25lIHx8IHZpc2l0b3IucGhvbmUudHJpbSgpID09PSAnJykgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoYOivt+i+k+WFpSR7dmlzaXRvclRpdGxlfeeahOaJi+acuuWPt2ApOw0KICAgICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOmqjOivgeaJi+acuuWPt+agvOW8j++8iOS4juWQjuerr+S/neaMgeS4gOiHtO+8iQ0KICAgICAgICBjb25zdCBwaG9uZVBhdHRlcm4gPSAvXjFbMy05XVxkezl9JC87DQogICAgICAgIGlmICghcGhvbmVQYXR0ZXJuLnRlc3QodmlzaXRvci5waG9uZS50cmltKCkpKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihgJHt2aXNpdG9yVGl0bGV955qE5omL5py65Y+35qC85byP5LiN5q2j56GuYCk7DQogICAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g6aqM6K+B6Lqr5Lu96K+B5Y+3DQogICAgICAgIGlmICghdmlzaXRvci5pZENhcmQgfHwgdmlzaXRvci5pZENhcmQudHJpbSgpID09PSAnJykgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoYOivt+i+k+WFpSR7dmlzaXRvclRpdGxlfeeahOi6q+S7veivgeWPt2ApOw0KICAgICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgICAgfQ0KDQogICAgICAgIGNvbnN0IGlkQ2FyZCA9IHZpc2l0b3IuaWRDYXJkLnRyaW0oKS50b1VwcGVyQ2FzZSgpOw0KDQogICAgICAgIC8vIOWmguaenOaYrzE45L2N6Lqr5Lu96K+B5Y+377yM6aqM6K+B5qC85byPDQogICAgICAgIGlmIChpZENhcmQubGVuZ3RoID09PSAxOCkgew0KICAgICAgICAgIGNvbnN0IGlkUGF0dGVybiA9IC9eWzEtOV1cZHs1fSgxOHwxOXwyMClcZHsyfSgoMFsxLTldKXwoMVswLTJdKSkoKFswLTJdWzEtOV0pfDEwfDIwfDMwfDMxKVxkezN9WzAtOVh4XSQvOw0KICAgICAgICAgIGlmICghaWRQYXR0ZXJuLnRlc3QoaWRDYXJkKSkgew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihgJHt2aXNpdG9yVGl0bGV955qE6Lqr5Lu96K+B5Y+35qC85byP5LiN5q2j56GuYCk7DQogICAgICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICAgICAgfQ0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5qOA5p+l6Lqr5Lu96K+B5Y+35piv5ZCm6YeN5aSNDQogICAgICAgIGZvciAobGV0IGogPSAwOyBqIDwgdGhpcy5mb3JtRGF0YS52aXNpdG9ycy5sZW5ndGg7IGorKykgew0KICAgICAgICAgIGlmIChpICE9PSBqICYmIHZpc2l0b3IuaWRDYXJkLnRyaW0oKS50b1VwcGVyQ2FzZSgpID09PSB0aGlzLmZvcm1EYXRhLnZpc2l0b3JzW2pdLmlkQ2FyZC50cmltKCkudG9VcHBlckNhc2UoKSkgew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihgJHt2aXNpdG9yVGl0bGV95LiO6K6/5a6iJHtqID09PSAwID8gJ+S4u+iBlOezu+S6uicgOiBqICsgMX3nmoTouqvku73or4Hlj7fph43lpI1gKTsNCiAgICAgICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCg0KICAgICAgICAvLyDmo4Dmn6XmiYvmnLrlj7fmmK/lkKbph43lpI0NCiAgICAgICAgZm9yIChsZXQgaiA9IDA7IGogPCB0aGlzLmZvcm1EYXRhLnZpc2l0b3JzLmxlbmd0aDsgaisrKSB7DQogICAgICAgICAgaWYgKGkgIT09IGogJiYgdmlzaXRvci5waG9uZS50cmltKCkgPT09IHRoaXMuZm9ybURhdGEudmlzaXRvcnNbal0ucGhvbmUudHJpbSgpKSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGAke3Zpc2l0b3JUaXRsZX3kuI7orr/lrqIke2ogPT09IDAgPyAn5Li76IGU57O75Lq6JyA6IGogKyAxfeeahOaJi+acuuWPt+mHjeWkje+8jOivt+ehruiupOaYr+WQpuS4uuWQjOS4gOS6umApOw0KICAgICAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOa4heeQhuWSjOagh+WHhuWMluaVsOaNrg0KICAgICAgICB2aXNpdG9yLm5hbWUgPSB2aXNpdG9yLm5hbWUudHJpbSgpOw0KICAgICAgICB2aXNpdG9yLnBob25lID0gdmlzaXRvci5waG9uZS50cmltKCk7DQogICAgICAgIHZpc2l0b3IuaWRDYXJkID0gdmlzaXRvci5pZENhcmQudHJpbSgpLnRvVXBwZXJDYXNlKCk7DQogICAgICAgIHZpc2l0b3IuY29tcGFueSA9ICh2aXNpdG9yLmNvbXBhbnkgfHwgJycpLnRyaW0oKTsNCiAgICAgIH0NCg0KICAgICAgcmV0dXJuIHRydWU7DQogICAgfSwNCg0KICAgIC8vIOmqjOivgeaXtumXtA0KICAgIHZhbGlkYXRlVGltZXMoKSB7DQogICAgICBpZiAoIXRoaXMuZm9ybURhdGEudmlzaXRJbmZvLnBsYW5uZWRFbnRyeURhdGV0aW1lKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+ivt+mAieaLqemihOiuoeadpeiuv+aXtumXtCcpOw0KICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICB9DQoNCiAgICAgIGlmICghdGhpcy5mb3JtRGF0YS52aXNpdEluZm8ucGxhbm5lZEV4aXREYXRldGltZSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfor7fpgInmi6npooTorqHnprvlvIDml7bpl7QnKTsNCiAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgfQ0KDQogICAgICBjb25zdCBlbnRyeVRpbWUgPSBuZXcgRGF0ZSh0aGlzLmZvcm1EYXRhLnZpc2l0SW5mby5wbGFubmVkRW50cnlEYXRldGltZSk7DQogICAgICBjb25zdCBleGl0VGltZSA9IG5ldyBEYXRlKHRoaXMuZm9ybURhdGEudmlzaXRJbmZvLnBsYW5uZWRFeGl0RGF0ZXRpbWUpOw0KICAgICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKTsNCg0KICAgICAgLy8g5qOA5p+l5p2l6K6/5pe26Ze05LiN6IO95pep5LqO5b2T5YmN5pe26Ze0MeWwj+aXtuWJjQ0KICAgICAgY29uc3Qgb25lSG91ckJlZm9yZSA9IG5ldyBEYXRlKG5vdy5nZXRUaW1lKCkgLSA2MCAqIDYwICogMTAwMCk7DQogICAgICBpZiAoZW50cnlUaW1lIDwgb25lSG91ckJlZm9yZSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfpooTorqHmnaXorr/ml7bpl7TkuI3og73ml6nkuo7lvZPliY3ml7bpl7Qx5bCP5pe25YmNJyk7DQogICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgIH0NCg0KICAgICAgLy8g5qOA5p+l56a75byA5pe26Ze05b+F6aG75pma5LqO5p2l6K6/5pe26Ze0DQogICAgICBpZiAoZXhpdFRpbWUgPD0gZW50cnlUaW1lKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+mihOiuoeemu+W8gOaXtumXtOW/hemhu+aZmuS6juadpeiuv+aXtumXtCcpOw0KICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICB9DQoNCiAgICAgIC8vIOajgOafpeiuv+mXruaXtumVv+S4jeiDvei2hei/hzI05bCP5pe2DQogICAgICBjb25zdCB2aXNpdER1cmF0aW9uID0gZXhpdFRpbWUuZ2V0VGltZSgpIC0gZW50cnlUaW1lLmdldFRpbWUoKTsNCiAgICAgIGNvbnN0IG1heER1cmF0aW9uID0gMjQgKiA2MCAqIDYwICogMTAwMDsgLy8gMjTlsI/ml7YNCiAgICAgIGlmICh2aXNpdER1cmF0aW9uID4gbWF4RHVyYXRpb24pIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Y2V5qyh6K6/6Zeu5pe26ZW/5LiN6IO96LaF6L+HMjTlsI/ml7YnKTsNCiAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgfQ0KDQogICAgICByZXR1cm4gdHJ1ZTsNCiAgICB9LA0KDQogICAgLy8g6YeN572u6KGo5Y2VDQogICAgcmVzZXRGb3JtKCkgew0KICAgICAgdGhpcy4kY29uZmlybSgn56Gu5a6a6KaB6YeN572u6KGo5Y2V5ZCX77yf5b2T5YmN5aGr5YaZ55qE5L+h5oGv5bCG5Lya5Lii5aSx44CCJywgJ+ehruiupOmHjee9ricsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLA0KICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywNCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgLy8g6YeN572u5omA5pyJ5pWw5o2uDQogICAgICAgIHRoaXMucmVnaXN0cmF0aW9uQ29tcGxldGVkID0gZmFsc2U7DQoNCiAgICAgICAgdGhpcy5mb3JtRGF0YSA9IHsNCiAgICAgICAgICB2aXNpdG9yczogWw0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBuYW1lOiAnJywNCiAgICAgICAgICAgICAgcGhvbmU6ICcnLA0KICAgICAgICAgICAgICBpZENhcmQ6ICcnLA0KICAgICAgICAgICAgICBjb21wYW55OiAnJywNCiAgICAgICAgICAgICAgaXNNYWluQ29udGFjdDogdHJ1ZQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIF0sDQogICAgICAgICAgdmlzaXRJbmZvOiB7DQogICAgICAgICAgICByZWFzb25Gb3JWaXNpdDogJycsDQogICAgICAgICAgICBob3N0RW1wbG95ZWVOYW1lOiAnJywNCiAgICAgICAgICAgIGRlcGFydG1lbnRWaXNpdGVkOiAnJywNCiAgICAgICAgICAgIHZlaGljbGVQbGF0ZU51bWJlcjogJycsDQogICAgICAgICAgICBwbGFubmVkRW50cnlEYXRldGltZTogJycsDQogICAgICAgICAgICBwbGFubmVkRXhpdERhdGV0aW1lOiAnJw0KICAgICAgICAgIH0NCiAgICAgICAgfTsNCg0KICAgICAgICB0aGlzLnJlZ2lzdHJhdGlvbklkID0gbnVsbDsNCg0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+ihqOWNleW3sumHjee9ricpOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8vIOaJk+WNsOWHreivgQ0KICAgIHByaW50Q3JlZGVudGlhbCgpIHsNCiAgICAgIHRoaXMuJG1lc3NhZ2UuaW5mbygn5omT5Y2w5Yqf6IO95byA5Y+R5LitLi4uJyk7DQogICAgfSwNCg0KICAgIC8vIOagvOW8j+WMluaXtumXtOaYvuekug0KICAgIHBhcnNlVGltZSh0aW1lKSB7DQogICAgICBpZiAoIXRpbWUpIHJldHVybiAnJzsNCiAgICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZSh0aW1lKTsNCiAgICAgIGlmIChpc05hTihkYXRlLmdldFRpbWUoKSkpIHJldHVybiAnJzsNCg0KICAgICAgY29uc3QgeWVhciA9IGRhdGUuZ2V0RnVsbFllYXIoKTsNCiAgICAgIGNvbnN0IG1vbnRoID0gU3RyaW5nKGRhdGUuZ2V0TW9udGgoKSArIDEpLnBhZFN0YXJ0KDIsICcwJyk7DQogICAgICBjb25zdCBkYXkgPSBTdHJpbmcoZGF0ZS5nZXREYXRlKCkpLnBhZFN0YXJ0KDIsICcwJyk7DQogICAgICBjb25zdCBob3VycyA9IFN0cmluZyhkYXRlLmdldEhvdXJzKCkpLnBhZFN0YXJ0KDIsICcwJyk7DQogICAgICBjb25zdCBtaW51dGVzID0gU3RyaW5nKGRhdGUuZ2V0TWludXRlcygpKS5wYWRTdGFydCgyLCAnMCcpOw0KDQogICAgICByZXR1cm4gYCR7eWVhcn0tJHttb250aH0tJHtkYXl9ICR7aG91cnN9OiR7bWludXRlc31gOw0KICAgIH0NCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["self-register.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAo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file": "self-register.vue", "sourceRoot": "src/views/asc/visitor", "sourcesContent": ["<template>\r\n  <div class=\"pc-register-container\">\r\n    <!-- 顶部导航栏 -->\r\n    <div class=\"top-nav\">\r\n      <div class=\"nav-content\">\r\n        <div class=\"logo-section\">\r\n          <img src=\"@/assets/logo/logo.png\" alt=\"公司logo\" class=\"nav-logo\" />\r\n          <span class=\"company-name\">智慧园区访客登记系统</span>\r\n        </div>\r\n        <div class=\"nav-actions\">\r\n          <el-button type=\"text\" @click=\"showHelp\">使用帮助</el-button>\r\n          <el-button type=\"text\" @click=\"contactService\">联系客服</el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 主要内容区域 -->\r\n    <div class=\"main-content\">\r\n      <!-- 左侧信息面板 -->\r\n      <div class=\"info-panel\">\r\n        <div class=\"welcome-card\">\r\n          <div class=\"welcome-icon\">\r\n            <i class=\"el-icon-office-building\"></i>\r\n          </div>\r\n          <h2>欢迎访问智慧园区</h2>\r\n          <p class=\"welcome-desc\">为了您的安全和便利，请填写以下信息完成访客登记</p>\r\n\r\n          <div class=\"process-steps\">\r\n            <div class=\"step-item\" :class=\"{ active: !registrationCompleted }\">\r\n              <div class=\"step-circle\">1</div>\r\n              <div class=\"step-text\">\r\n                <h4>填写信息</h4>\r\n                <p>完善访问详细信息</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"step-item\" :class=\"{ active: registrationCompleted }\">\r\n              <div class=\"step-circle\">2</div>\r\n              <div class=\"step-text\">\r\n                <h4>获取凭证</h4>\r\n                <p>生成访问凭证二维码</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"security-tips\">\r\n            <h4><i class=\"el-icon-lock\"></i> 安全提示</h4>\r\n            <ul>\r\n              <li>请确保提供真实有效的身份信息</li>\r\n              <li>您的个人信息将被严格保密</li>\r\n              <li>访问凭证仅限当次使用</li>\r\n            </ul>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 右侧表单区域 -->\r\n      <div class=\"form-panel\">\r\n        <!-- 表单标题 -->\r\n        <div class=\"form-header\">\r\n          <h3 v-if=\"!registrationCompleted\"><i class=\"el-icon-edit\"></i> 访客登记信息</h3>\r\n          <h3 v-else><i class=\"el-icon-circle-check\"></i> 登记完成</h3>\r\n          <p v-if=\"!registrationCompleted\">请填写详细的访问信息</p>\r\n          <p v-else>您的访客登记已成功提交</p>\r\n        </div>\r\n\r\n        <!-- 表单内容 -->\r\n        <div class=\"form-content\" v-if=\"!registrationCompleted\">\r\n          <el-form ref=\"visitForm\" :model=\"formData\" :rules=\"visitRules\"\r\n                   label-width=\"120px\" class=\"visit-form\">\r\n\r\n            <!-- 基本访问信息 -->\r\n            <div class=\"form-section\">\r\n              <h4 class=\"section-title\">基本访问信息</h4>\r\n              <el-row :gutter=\"20\">\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"来访事由\" prop=\"visitInfo.reasonForVisit\">\r\n                    <el-input v-model=\"formData.visitInfo.reasonForVisit\" placeholder=\"请输入来访事由\"\r\n                              prefix-icon=\"el-icon-tickets\" type=\"textarea\" :rows=\"3\" maxlength=\"200\" show-word-limit />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"被访人姓名\" prop=\"visitInfo.hostEmployeeName\">\r\n                    <el-input v-model=\"formData.visitInfo.hostEmployeeName\" placeholder=\"请输入被访人姓名\"\r\n                              prefix-icon=\"el-icon-user\" maxlength=\"20\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n\r\n              <el-row :gutter=\"20\">\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"被访部门\" prop=\"visitInfo.departmentVisited\">\r\n                    <el-input v-model=\"formData.visitInfo.departmentVisited\" placeholder=\"请输入被访部门\"\r\n                              prefix-icon=\"el-icon-office-building\" maxlength=\"50\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"车牌号\">\r\n                    <el-input v-model=\"formData.visitInfo.vehiclePlateNumber\" placeholder=\"如有车辆请填写车牌号，多个用逗号分隔\"\r\n                              prefix-icon=\"el-icon-truck\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </div>\r\n\r\n            <!-- 时间信息 -->\r\n            <div class=\"form-section\">\r\n              <h4 class=\"section-title\">访问时间</h4>\r\n              <el-row :gutter=\"20\">\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"预计到访时间\" prop=\"visitInfo.plannedEntryDatetime\">\r\n                    <el-date-picker\r\n                      v-model=\"formData.visitInfo.plannedEntryDatetime\"\r\n                      type=\"datetime\"\r\n                      placeholder=\"选择到访时间\"\r\n                      style=\"width: 100%\"\r\n                      value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                      :picker-options=\"entryPickerOptions\"\r\n                      @change=\"onArrivalTimeChange\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"预计离开时间\" prop=\"visitInfo.plannedExitDatetime\">\r\n                    <el-date-picker\r\n                      v-model=\"formData.visitInfo.plannedExitDatetime\"\r\n                      type=\"datetime\"\r\n                      placeholder=\"选择离开时间\"\r\n                      style=\"width: 100%\"\r\n                      value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                      :picker-options=\"exitPickerOptions\"\r\n                      @change=\"onDepartureTimeChange\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </div>\r\n\r\n            <!-- 访客信息 -->\r\n            <div class=\"form-section\">\r\n              <div class=\"section-header\">\r\n                <h4 class=\"section-title\">访客信息（第一位为主联系人）共 {{ totalVisitors }} 人</h4>\r\n                <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"addVisitor\" :disabled=\"formData.visitors.length >= 10\">\r\n                  添加访客\r\n                </el-button>\r\n              </div>\r\n\r\n              <div class=\"visitors-list\">\r\n                <div v-for=\"(visitor, index) in formData.visitors\" :key=\"index\" class=\"visitor-item\">\r\n                  <div class=\"visitor-header\">\r\n                    <h5 class=\"visitor-title\">\r\n                      {{ index === 0 ? '主联系人' : `访客 ${index + 1}` }}\r\n                    </h5>\r\n                    <el-button v-if=\"index > 0\" size=\"mini\" type=\"danger\" icon=\"el-icon-delete\"\r\n                               @click=\"removeVisitor(index)\" circle />\r\n                  </div>\r\n\r\n                  <el-row :gutter=\"20\">\r\n                    <el-col :span=\"12\">\r\n                      <el-form-item :label=\"`姓名`\" :prop=\"`visitors.${index}.name`\">\r\n                        <el-input v-model=\"visitor.name\" placeholder=\"请输入姓名\" maxlength=\"20\" />\r\n                      </el-form-item>\r\n                    </el-col>\r\n                    <el-col :span=\"12\">\r\n                      <el-form-item :label=\"`手机号`\" :prop=\"`visitors.${index}.phone`\">\r\n                        <el-input v-model=\"visitor.phone\" placeholder=\"请输入手机号\" />\r\n                      </el-form-item>\r\n                    </el-col>\r\n                  </el-row>\r\n\r\n                  <el-row :gutter=\"20\">\r\n                    <el-col :span=\"12\">\r\n                      <el-form-item :label=\"`身份证号`\" :prop=\"`visitors.${index}.idCard`\">\r\n                        <el-input v-model=\"visitor.idCard\" placeholder=\"请输入身份证号\" />\r\n                      </el-form-item>\r\n                    </el-col>\r\n                    <el-col :span=\"12\">\r\n                      <el-form-item :label=\"`公司名称`\">\r\n                        <el-input v-model=\"visitor.company\" placeholder=\"请输入公司名称（可选）\" maxlength=\"100\" />\r\n                      </el-form-item>\r\n                    </el-col>\r\n                  </el-row>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-form>\r\n        </div>\r\n\r\n        <!-- 登记成功页面 -->\r\n        <div class=\"success-content\" v-if=\"registrationCompleted\">\r\n          <div class=\"success-icon\">\r\n            <i class=\"el-icon-circle-check\"></i>\r\n          </div>\r\n\r\n          <h4>登记成功！</h4>\r\n          <p class=\"success-message\">您的访客登记已提交，请等待审核通过</p>\r\n\r\n          <!-- 登记信息摘要 -->\r\n          <div class=\"register-summary\">\r\n            <h5>登记信息摘要</h5>\r\n            <el-descriptions :column=\"2\" border>\r\n              <el-descriptions-item label=\"主联系人\">\r\n                {{ mainContact.name }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"联系电话\">\r\n                {{ mainContact.phone }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"被访人\">\r\n                {{ formData.visitInfo.hostEmployeeName }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"被访部门\">\r\n                {{ formData.visitInfo.departmentVisited }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"来访事由\" span=\"2\">\r\n                {{ formData.visitInfo.reasonForVisit }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"预计到访时间\">\r\n                {{ parseTime(formData.visitInfo.plannedEntryDatetime) }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"预计离开时间\">\r\n                {{ parseTime(formData.visitInfo.plannedExitDatetime) }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"访客总数\">\r\n                {{ totalVisitors }} 人\r\n              </el-descriptions-item>\r\n            </el-descriptions>\r\n          </div>\r\n\r\n          <!-- 访问凭证 -->\r\n          <div class=\"access-credential\" v-if=\"registrationId\">\r\n            <h5>访问凭证</h5>\r\n            <div class=\"credential-card\">\r\n              <div class=\"qr-code-container\">\r\n                <div class=\"qr-code\">\r\n                  <i class=\"el-icon-qrcode\"></i>\r\n                </div>\r\n                <p class=\"qr-code-id\">{{ registrationId }}</p>\r\n              </div>\r\n              <div class=\"credential-info\">\r\n                <h6>使用说明</h6>\r\n                <ul>\r\n                  <li>请保存此二维码截图</li>\r\n                  <li>审核通过后可用于园区门禁</li>\r\n                  <li>凭证仅限当次访问使用</li>\r\n                  <li>如有疑问请联系园区前台</li>\r\n                </ul>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 底部操作按钮 -->\r\n        <div class=\"form-actions\">\r\n          <el-button v-if=\"!registrationCompleted\"\r\n                     type=\"primary\" @click=\"submitForm\" size=\"large\" :loading=\"submitting\">\r\n            {{ submitting ? '登记中...' : `确认登记 (${totalVisitors}人)` }}\r\n          </el-button>\r\n\r\n          <el-button v-if=\"!registrationCompleted\" @click=\"resetForm\" size=\"large\">\r\n            重置表单\r\n          </el-button>\r\n\r\n          <div v-if=\"registrationCompleted\" class=\"final-actions\">\r\n            <el-button type=\"primary\" @click=\"printCredential\" size=\"large\">\r\n              <i class=\"el-icon-printer\"></i> 打印凭证\r\n            </el-button>\r\n            <el-button @click=\"resetForm\" size=\"large\">\r\n              <i class=\"el-icon-refresh\"></i> 重新登记\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { submitVisitorRegistration } from \"@/api/asc/visitor\";\r\n\r\nexport default {\r\n  name: \"SelfRegister\",\r\n  data() {\r\n    return {\r\n      submitting: false,\r\n      registrationCompleted: false,\r\n\r\n      // 表单数据 - 与微信端保持一致的结构\r\n      formData: {\r\n        // 访客列表（第一位为主联系人）\r\n        visitors: [\r\n          {\r\n            name: '',\r\n            phone: '',\r\n            idCard: '',\r\n            company: '',\r\n            isMainContact: true\r\n          }\r\n        ],\r\n        // 来访信息\r\n        visitInfo: {\r\n          reasonForVisit: '',\r\n          hostEmployeeName: '',\r\n          departmentVisited: '',\r\n          vehiclePlateNumber: '',\r\n          plannedEntryDatetime: '',\r\n          plannedExitDatetime: ''\r\n        }\r\n      },\r\n\r\n      // 表单验证规则 - 与微信端保持一致\r\n      visitRules: {\r\n        'visitInfo.reasonForVisit': [\r\n          { required: true, message: '请输入来访事由', trigger: 'blur' },\r\n          { min: 2, max: 200, message: '来访事由长度应在2-200个字符之间', trigger: 'blur' }\r\n        ],\r\n        'visitInfo.hostEmployeeName': [\r\n          { required: true, message: '请输入被访人姓名', trigger: 'blur' },\r\n          { min: 2, max: 20, message: '被访人姓名长度应在2-20个字符之间', trigger: 'blur' }\r\n        ],\r\n        'visitInfo.departmentVisited': [\r\n          { required: true, message: '请输入被访部门', trigger: 'blur' },\r\n          { min: 2, max: 50, message: '部门名称长度应在2-50个字符之间', trigger: 'blur' }\r\n        ],\r\n        'visitInfo.plannedEntryDatetime': [\r\n          { required: true, message: '请选择预计来访时间', trigger: 'change' }\r\n        ],\r\n        'visitInfo.plannedExitDatetime': [\r\n          { required: true, message: '请选择预计离开时间', trigger: 'change' }\r\n        ],\r\n        // 动态访客验证规则\r\n        'visitors.0.name': [\r\n          { required: true, message: '请输入主联系人姓名', trigger: 'blur' },\r\n          { min: 2, max: 20, message: '姓名长度应在2-20个字符之间', trigger: 'blur' }\r\n        ],\r\n        'visitors.0.phone': [\r\n          { required: true, message: '请输入主联系人手机号', trigger: 'blur' },\r\n          { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }\r\n        ],\r\n        'visitors.0.idCard': [\r\n          { required: true, message: '请输入主联系人身份证号', trigger: 'blur' }\r\n        ]\r\n      },\r\n\r\n      // 登记结果\r\n      registrationId: null,\r\n\r\n      // 时间选择器配置\r\n      entryPickerOptions: {\r\n        shortcuts: [{\r\n          text: '现在',\r\n          onClick(picker) {\r\n            picker.$emit('pick', new Date());\r\n          }\r\n        }, {\r\n          text: '1小时后',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setTime(date.getTime() + 3600 * 1000);\r\n            picker.$emit('pick', date);\r\n          }\r\n        }, {\r\n          text: '明天上午9点',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setDate(date.getDate() + 1);\r\n            date.setHours(9, 0, 0, 0);\r\n            picker.$emit('pick', date);\r\n          }\r\n        }],\r\n        disabledDate(time) {\r\n          // 允许选择当前时间前1小时到未来30天\r\n          const oneHourBefore = Date.now() - 3600 * 1000;\r\n          const thirtyDaysLater = Date.now() + 30 * 24 * 3600 * 1000;\r\n          return time.getTime() < oneHourBefore || time.getTime() > thirtyDaysLater;\r\n        }\r\n      },\r\n\r\n      exitPickerOptions: {\r\n        shortcuts: [{\r\n          text: '2小时后',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setTime(date.getTime() + 2 * 3600 * 1000);\r\n            picker.$emit('pick', date);\r\n          }\r\n        }, {\r\n          text: '今天下午6点',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setHours(18, 0, 0, 0);\r\n            if (date.getTime() < Date.now()) {\r\n              date.setDate(date.getDate() + 1);\r\n            }\r\n            picker.$emit('pick', date);\r\n          }\r\n        }, {\r\n          text: '明天下午6点',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setDate(date.getDate() + 1);\r\n            date.setHours(18, 0, 0, 0);\r\n            picker.$emit('pick', date);\r\n          }\r\n        }],\r\n        disabledDate(time) {\r\n          // 允许选择当前时间到未来30天\r\n          const thirtyDaysLater = Date.now() + 30 * 24 * 3600 * 1000;\r\n          return time.getTime() < Date.now() - 8.64e7 || time.getTime() > thirtyDaysLater;\r\n        }\r\n      }\r\n    };\r\n  },\r\n\r\n  computed: {\r\n    // 总访客人数\r\n    totalVisitors() {\r\n      return this.formData.visitors.length;\r\n    },\r\n\r\n    // 主联系人信息\r\n    mainContact() {\r\n      return this.formData.visitors[0] || {};\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    // 添加访客\r\n    addVisitor() {\r\n      if (this.formData.visitors.length >= 10) {\r\n        this.$message.warning('最多只能添加10名访客');\r\n        return;\r\n      }\r\n\r\n      this.formData.visitors.push({\r\n        name: '',\r\n        phone: '',\r\n        idCard: '',\r\n        company: '',\r\n        isMainContact: false\r\n      });\r\n\r\n      this.$message.success('已添加访客');\r\n    },\r\n\r\n    // 移除访客\r\n    removeVisitor(index) {\r\n      if (index === 0) {\r\n        this.$message.warning('不能删除主联系人');\r\n        return;\r\n      }\r\n\r\n      this.formData.visitors.splice(index, 1);\r\n      this.$message.success('已移除访客');\r\n    },\r\n\r\n    // 格式化日期为后端期望的格式\r\n    formatDateForBackend(dateStr) {\r\n      if (!dateStr) return '';\r\n\r\n      const date = new Date(dateStr);\r\n      if (isNaN(date.getTime())) return '';\r\n\r\n      const year = date.getFullYear();\r\n      const month = String(date.getMonth() + 1).padStart(2, '0');\r\n      const day = String(date.getDate()).padStart(2, '0');\r\n      const hours = String(date.getHours()).padStart(2, '0');\r\n      const minutes = String(date.getMinutes()).padStart(2, '0');\r\n      const seconds = String(date.getSeconds()).padStart(2, '0');\r\n\r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n    },\r\n\r\n    /** 来访时间变更事件 */\r\n    onArrivalTimeChange(value) {\r\n      console.log('预计来访时间变更:', value);\r\n      // 自动设置离开时间为来访时间后4小时\r\n      if (value && !this.formData.visitInfo.plannedExitDatetime) {\r\n        const arrivalTime = new Date(value);\r\n        const departureTime = new Date(arrivalTime.getTime() + 4 * 60 * 60 * 1000);\r\n\r\n        const year = departureTime.getFullYear();\r\n        const month = String(departureTime.getMonth() + 1).padStart(2, '0');\r\n        const day = String(departureTime.getDate()).padStart(2, '0');\r\n        const hours = String(departureTime.getHours()).padStart(2, '0');\r\n        const minutes = String(departureTime.getMinutes()).padStart(2, '0');\r\n        const seconds = String(departureTime.getSeconds()).padStart(2, '0');\r\n\r\n        this.formData.visitInfo.plannedExitDatetime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n      }\r\n    },\r\n\r\n    /** 离开时间变更事件 */\r\n    onDepartureTimeChange(value) {\r\n      console.log('预计离开时间变更:', value);\r\n      // 验证离开时间不能早于来访时间\r\n      if (value && this.formData.visitInfo.plannedEntryDatetime) {\r\n        const arrivalTime = new Date(this.formData.visitInfo.plannedEntryDatetime);\r\n        const departureTime = new Date(value);\r\n\r\n        if (departureTime <= arrivalTime) {\r\n          this.$message.warning('预计离开时间不能早于或等于来访时间');\r\n          this.formData.visitInfo.plannedExitDatetime = '';\r\n        }\r\n      }\r\n    },\r\n\r\n    /** 提交表单 */\r\n    async submitForm() {\r\n      try {\r\n        // 验证表单\r\n        await this.$refs.visitForm.validate();\r\n\r\n        // 验证访客信息\r\n        if (!this.validateVisitors()) {\r\n          return;\r\n        }\r\n\r\n        // 验证时间\r\n        if (!this.validateTimes()) {\r\n          return;\r\n        }\r\n\r\n        this.submitting = true;\r\n\r\n        // 获取主联系人信息\r\n        const mainContact = this.formData.visitors[0];\r\n\r\n        const submitData = {\r\n          // VisitRegistrations 对象\r\n          registration: {\r\n            primaryContactName: mainContact.name.trim(),\r\n            primaryContactPhone: mainContact.phone.trim(),\r\n            reasonForVisit: this.formData.visitInfo.reasonForVisit,\r\n            hostEmployeeName: this.formData.visitInfo.hostEmployeeName,\r\n            hostEmployeeId: null, // 前端暂时没有被访人ID，后端可能会自动处理\r\n            departmentVisited: this.formData.visitInfo.departmentVisited,\r\n            plannedEntryDatetime: this.formatDateForBackend(this.formData.visitInfo.plannedEntryDatetime),\r\n            plannedExitDatetime: this.formatDateForBackend(this.formData.visitInfo.plannedExitDatetime),\r\n            vehiclePlateNumber: this.formData.visitInfo.vehiclePlateNumber || '',\r\n            totalCompanions: this.formData.visitors.length - 1\r\n          },\r\n          // RegistrationAttendees 数组 - 使用后端期望的临时字段\r\n          attendeesList: this.formData.visitors.map((visitor, index) => ({\r\n            visitorName: visitor.name.trim(),\r\n            visitorPhone: visitor.phone.trim(),\r\n            visitorIdCard: visitor.idCard.trim().toUpperCase(),\r\n            visitorCompany: (visitor.company || '').trim(),\r\n            isPrimary: index === 0 ? \"1\" : \"0\", // 第一个访客必须是主联系人\r\n            visitorAvatarPhoto: null // 暂时不支持头像上传\r\n          }))\r\n        };\r\n\r\n        console.log('提交数据结构:', JSON.stringify(submitData, null, 2));\r\n\r\n        // 调用API提交数据\r\n        const response = await submitVisitorRegistration(submitData);\r\n\r\n        if (response.code === 200) {\r\n          this.registrationId = response.data || 'VR' + Date.now();\r\n          this.$message.success(`${this.totalVisitors}名访客登记成功！`);\r\n          this.registrationCompleted = true;\r\n        } else {\r\n          this.$message.error(response.msg || '登记失败，请重试');\r\n        }\r\n      } catch (error) {\r\n        console.error('提交表单失败:', error);\r\n        this.$message.error('登记失败，请检查网络连接');\r\n      } finally {\r\n        this.submitting = false;\r\n      }\r\n    },\r\n\r\n    // 验证访客信息\r\n    validateVisitors() {\r\n      for (let i = 0; i < this.formData.visitors.length; i++) {\r\n        const visitor = this.formData.visitors[i];\r\n        const visitorTitle = i === 0 ? '主联系人' : `访客${i + 1}`;\r\n\r\n        // 验证姓名\r\n        if (!visitor.name || visitor.name.trim() === '') {\r\n          this.$message.error(`请输入${visitorTitle}的姓名`);\r\n          return false;\r\n        }\r\n\r\n        if (visitor.name.trim().length < 2 || visitor.name.trim().length > 20) {\r\n          this.$message.error(`${visitorTitle}的姓名长度应在2-20个字符之间`);\r\n          return false;\r\n        }\r\n\r\n        // 验证手机号\r\n        if (!visitor.phone || visitor.phone.trim() === '') {\r\n          this.$message.error(`请输入${visitorTitle}的手机号`);\r\n          return false;\r\n        }\r\n\r\n        // 验证手机号格式（与后端保持一致）\r\n        const phonePattern = /^1[3-9]\\d{9}$/;\r\n        if (!phonePattern.test(visitor.phone.trim())) {\r\n          this.$message.error(`${visitorTitle}的手机号格式不正确`);\r\n          return false;\r\n        }\r\n\r\n        // 验证身份证号\r\n        if (!visitor.idCard || visitor.idCard.trim() === '') {\r\n          this.$message.error(`请输入${visitorTitle}的身份证号`);\r\n          return false;\r\n        }\r\n\r\n        const idCard = visitor.idCard.trim().toUpperCase();\r\n\r\n        // 如果是18位身份证号，验证格式\r\n        if (idCard.length === 18) {\r\n          const idPattern = /^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$/;\r\n          if (!idPattern.test(idCard)) {\r\n            this.$message.error(`${visitorTitle}的身份证号格式不正确`);\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 检查身份证号是否重复\r\n        for (let j = 0; j < this.formData.visitors.length; j++) {\r\n          if (i !== j && visitor.idCard.trim().toUpperCase() === this.formData.visitors[j].idCard.trim().toUpperCase()) {\r\n            this.$message.error(`${visitorTitle}与访客${j === 0 ? '主联系人' : j + 1}的身份证号重复`);\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 检查手机号是否重复\r\n        for (let j = 0; j < this.formData.visitors.length; j++) {\r\n          if (i !== j && visitor.phone.trim() === this.formData.visitors[j].phone.trim()) {\r\n            this.$message.error(`${visitorTitle}与访客${j === 0 ? '主联系人' : j + 1}的手机号重复，请确认是否为同一人`);\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 清理和标准化数据\r\n        visitor.name = visitor.name.trim();\r\n        visitor.phone = visitor.phone.trim();\r\n        visitor.idCard = visitor.idCard.trim().toUpperCase();\r\n        visitor.company = (visitor.company || '').trim();\r\n      }\r\n\r\n      return true;\r\n    },\r\n\r\n    // 验证时间\r\n    validateTimes() {\r\n      if (!this.formData.visitInfo.plannedEntryDatetime) {\r\n        this.$message.error('请选择预计来访时间');\r\n        return false;\r\n      }\r\n\r\n      if (!this.formData.visitInfo.plannedExitDatetime) {\r\n        this.$message.error('请选择预计离开时间');\r\n        return false;\r\n      }\r\n\r\n      const entryTime = new Date(this.formData.visitInfo.plannedEntryDatetime);\r\n      const exitTime = new Date(this.formData.visitInfo.plannedExitDatetime);\r\n      const now = new Date();\r\n\r\n      // 检查来访时间不能早于当前时间1小时前\r\n      const oneHourBefore = new Date(now.getTime() - 60 * 60 * 1000);\r\n      if (entryTime < oneHourBefore) {\r\n        this.$message.error('预计来访时间不能早于当前时间1小时前');\r\n        return false;\r\n      }\r\n\r\n      // 检查离开时间必须晚于来访时间\r\n      if (exitTime <= entryTime) {\r\n        this.$message.error('预计离开时间必须晚于来访时间');\r\n        return false;\r\n      }\r\n\r\n      // 检查访问时长不能超过24小时\r\n      const visitDuration = exitTime.getTime() - entryTime.getTime();\r\n      const maxDuration = 24 * 60 * 60 * 1000; // 24小时\r\n      if (visitDuration > maxDuration) {\r\n        this.$message.error('单次访问时长不能超过24小时');\r\n        return false;\r\n      }\r\n\r\n      return true;\r\n    },\r\n\r\n    // 重置表单\r\n    resetForm() {\r\n      this.$confirm('确定要重置表单吗？当前填写的信息将会丢失。', '确认重置', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 重置所有数据\r\n        this.registrationCompleted = false;\r\n\r\n        this.formData = {\r\n          visitors: [\r\n            {\r\n              name: '',\r\n              phone: '',\r\n              idCard: '',\r\n              company: '',\r\n              isMainContact: true\r\n            }\r\n          ],\r\n          visitInfo: {\r\n            reasonForVisit: '',\r\n            hostEmployeeName: '',\r\n            departmentVisited: '',\r\n            vehiclePlateNumber: '',\r\n            plannedEntryDatetime: '',\r\n            plannedExitDatetime: ''\r\n          }\r\n        };\r\n\r\n        this.registrationId = null;\r\n\r\n        this.$message.success('表单已重置');\r\n      });\r\n    },\r\n\r\n    // 打印凭证\r\n    printCredential() {\r\n      this.$message.info('打印功能开发中...');\r\n    },\r\n\r\n    // 格式化时间显示\r\n    parseTime(time) {\r\n      if (!time) return '';\r\n      const date = new Date(time);\r\n      if (isNaN(date.getTime())) return '';\r\n\r\n      const year = date.getFullYear();\r\n      const month = String(date.getMonth() + 1).padStart(2, '0');\r\n      const day = String(date.getDate()).padStart(2, '0');\r\n      const hours = String(date.getHours()).padStart(2, '0');\r\n      const minutes = String(date.getMinutes()).padStart(2, '0');\r\n\r\n      return `${year}-${month}-${day} ${hours}:${minutes}`;\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 主容器 */\r\n.pc-register-container {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n  font-family: 'Helvetica Neue', Arial, sans-serif;\r\n}\r\n\r\n/* 顶部导航 */\r\n.top-nav {\r\n  background: #fff;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  padding: 0 40px;\r\n  position: sticky;\r\n  top: 0;\r\n  z-index: 100;\r\n}\r\n\r\n.nav-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  height: 70px;\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.logo-section {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n}\r\n\r\n.nav-logo {\r\n  height: 40px;\r\n}\r\n\r\n.company-name {\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n.nav-actions {\r\n  display: flex;\r\n  gap: 20px;\r\n}\r\n\r\n/* 主内容区域 */\r\n.main-content {\r\n  display: flex;\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n  padding: 40px;\r\n  gap: 40px;\r\n  min-height: calc(100vh - 110px);\r\n}\r\n\r\n/* 左侧信息面板 */\r\n.info-panel {\r\n  flex: 0 0 400px;\r\n}\r\n\r\n.welcome-card {\r\n  background: #fff;\r\n  border-radius: 16px;\r\n  padding: 32px;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\r\n  height: fit-content;\r\n  position: sticky;\r\n  top: 110px;\r\n}\r\n\r\n.welcome-icon {\r\n  text-align: center;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.welcome-icon i {\r\n  font-size: 64px;\r\n  color: #3498db;\r\n}\r\n\r\n.welcome-card h2 {\r\n  text-align: center;\r\n  color: #2c3e50;\r\n  margin-bottom: 16px;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n}\r\n\r\n.welcome-desc {\r\n  text-align: center;\r\n  color: #7f8c8d;\r\n  margin-bottom: 32px;\r\n  font-size: 16px;\r\n  line-height: 1.6;\r\n}\r\n\r\n/* 流程步骤 */\r\n.process-steps {\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.step-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n  margin-bottom: 24px;\r\n  padding: 16px;\r\n  border-radius: 12px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.step-item.active {\r\n  background: linear-gradient(135deg, #3498db, #2980b9);\r\n  color: white;\r\n  transform: translateX(8px);\r\n}\r\n\r\n.step-item:not(.active) {\r\n  background: #f8f9fa;\r\n  color: #6c757d;\r\n}\r\n\r\n.step-circle {\r\n  width: 32px;\r\n  height: 32px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-weight: bold;\r\n  font-size: 14px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.step-item.active .step-circle {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  color: white;\r\n}\r\n\r\n.step-item:not(.active) .step-circle {\r\n  background: #dee2e6;\r\n  color: #6c757d;\r\n}\r\n\r\n.step-text h4 {\r\n  margin: 0 0 4px 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.step-text p {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  opacity: 0.8;\r\n}\r\n\r\n/* 安全提示 */\r\n.security-tips {\r\n  background: #f8f9fa;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  border-left: 4px solid #3498db;\r\n}\r\n\r\n.security-tips h4 {\r\n  margin: 0 0 12px 0;\r\n  color: #2c3e50;\r\n  font-size: 16px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.security-tips ul {\r\n  margin: 0;\r\n  padding-left: 20px;\r\n  color: #6c757d;\r\n}\r\n\r\n.security-tips li {\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n  line-height: 1.4;\r\n}\r\n\r\n/* 右侧表单面板 */\r\n.form-panel {\r\n  flex: 1;\r\n  background: #fff;\r\n  border-radius: 16px;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n/* 进度指示器 */\r\n.progress-indicator {\r\n  background: #f8f9fa;\r\n  padding: 24px 32px;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.progress-bar {\r\n  height: 6px;\r\n  background: #e9ecef;\r\n  border-radius: 3px;\r\n  overflow: hidden;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.progress-fill {\r\n  height: 100%;\r\n  background: linear-gradient(90deg, #3498db, #2980b9);\r\n  border-radius: 3px;\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.progress-text {\r\n  text-align: center;\r\n  color: #6c757d;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 表单内容 */\r\n.form-content {\r\n  padding: 32px;\r\n  max-height: calc(100vh - 300px);\r\n  overflow-y: auto;\r\n}\r\n\r\n.form-header {\r\n  text-align: center;\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.form-header h3 {\r\n  color: #2c3e50;\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  margin-bottom: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 12px;\r\n}\r\n\r\n.form-header p {\r\n  color: #7f8c8d;\r\n  margin: 0;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 验证方式网格 */\r\n.verify-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 20px;\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.verify-card {\r\n  position: relative;\r\n  background: #f8f9fa;\r\n  border: 2px solid #e9ecef;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.verify-card:hover {\r\n  border-color: #3498db;\r\n  background: #fff;\r\n  box-shadow: 0 4px 16px rgba(52, 152, 219, 0.15);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.verify-card.selected {\r\n  border-color: #3498db;\r\n  background: linear-gradient(135deg, #3498db, #2980b9);\r\n  color: white;\r\n  box-shadow: 0 8px 24px rgba(52, 152, 219, 0.3);\r\n}\r\n\r\n.verify-icon {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.verify-icon i {\r\n  font-size: 48px;\r\n  color: #3498db;\r\n}\r\n\r\n.verify-card.selected .verify-icon i {\r\n  color: white;\r\n}\r\n\r\n.verify-card h4 {\r\n  margin: 0 0 8px 0;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.verify-card p {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  opacity: 0.8;\r\n}\r\n\r\n.verify-status {\r\n  position: absolute;\r\n  top: 12px;\r\n  right: 12px;\r\n  width: 24px;\r\n  height: 24px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n/* 验证操作区域 */\r\n.verify-operation {\r\n  margin-top: 32px;\r\n  padding: 24px;\r\n  background: #f8f9fa;\r\n  border-radius: 12px;\r\n}\r\n\r\n.operation-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.operation-header h4 {\r\n  margin: 0;\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n/* 身份表单 */\r\n.identity-form {\r\n  background: white;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n}\r\n\r\n/* 身份证读卡器 */\r\n.id-card-reader {\r\n  text-align: center;\r\n}\r\n\r\n.reader-visual {\r\n  background: white;\r\n  padding: 40px;\r\n  border-radius: 12px;\r\n}\r\n\r\n.reader-animation {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.rotating {\r\n  animation: rotate 2s linear infinite;\r\n}\r\n\r\n@keyframes rotate {\r\n  from { transform: rotate(0deg); }\r\n  to { transform: rotate(360deg); }\r\n}\r\n\r\n.reader-animation i {\r\n  font-size: 64px;\r\n  color: #3498db;\r\n}\r\n\r\n.reader-visual h4 {\r\n  color: #2c3e50;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.reader-visual p {\r\n  color: #7f8c8d;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.reader-tips {\r\n  margin: 24px 0;\r\n}\r\n\r\n/* 人脸识别 */\r\n.face-recognition {\r\n  text-align: center;\r\n}\r\n\r\n.camera-container {\r\n  background: white;\r\n  padding: 40px;\r\n  border-radius: 12px;\r\n}\r\n\r\n.camera-frame {\r\n  position: relative;\r\n  width: 280px;\r\n  height: 210px;\r\n  margin: 0 auto 24px;\r\n  border: 3px solid #3498db;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #f8f9fa;\r\n  overflow: hidden;\r\n}\r\n\r\n.camera-overlay {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 16px;\r\n}\r\n\r\n.face-outline {\r\n  width: 120px;\r\n  height: 150px;\r\n  border: 2px dashed #3498db;\r\n  border-radius: 60px;\r\n  opacity: 0.6;\r\n}\r\n\r\n.camera-icon {\r\n  font-size: 32px;\r\n  color: #3498db;\r\n}\r\n\r\n.scanning-line {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 2px;\r\n  background: linear-gradient(90deg, transparent, #3498db, transparent);\r\n  animation: scan 2s ease-in-out infinite;\r\n}\r\n\r\n@keyframes scan {\r\n  0% { transform: translateY(0); }\r\n  50% { transform: translateY(206px); }\r\n  100% { transform: translateY(0); }\r\n}\r\n\r\n/* 表单分组 */\r\n.form-section {\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.section-title {\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin-bottom: 16px;\r\n  padding-bottom: 8px;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n/* 同行人员 */\r\n.no-companions {\r\n  text-align: center;\r\n  padding: 40px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.no-companions i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  color: #bdc3c7;\r\n}\r\n\r\n.companions-table {\r\n  background: #f8f9fa;\r\n  padding: 16px;\r\n  border-radius: 8px;\r\n}\r\n\r\n/* 成功页面 */\r\n.success-content {\r\n  text-align: center;\r\n}\r\n\r\n.success-icon {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.success-icon i {\r\n  font-size: 80px;\r\n  color: #27ae60;\r\n}\r\n\r\n.success-content h4 {\r\n  color: #2c3e50;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.success-message {\r\n  color: #7f8c8d;\r\n  font-size: 16px;\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.register-summary {\r\n  background: #f8f9fa;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n  margin-bottom: 32px;\r\n  text-align: left;\r\n}\r\n\r\n.register-summary h5 {\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin-bottom: 16px;\r\n  text-align: center;\r\n}\r\n\r\n.access-credential {\r\n  background: #f8f9fa;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n  text-align: left;\r\n}\r\n\r\n.access-credential h5 {\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin-bottom: 16px;\r\n  text-align: center;\r\n}\r\n\r\n.credential-card {\r\n  display: flex;\r\n  gap: 24px;\r\n  background: white;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n  border: 2px solid #e9ecef;\r\n}\r\n\r\n.qr-code-container {\r\n  flex: 0 0 120px;\r\n  text-align: center;\r\n}\r\n\r\n.qr-code {\r\n  width: 120px;\r\n  height: 120px;\r\n  border: 2px solid #e9ecef;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #f8f9fa;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.qr-code i {\r\n  font-size: 48px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.qr-code-id {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n  font-family: monospace;\r\n  margin: 0;\r\n}\r\n\r\n.credential-info {\r\n  flex: 1;\r\n}\r\n\r\n.credential-info h6 {\r\n  color: #2c3e50;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.credential-info ul {\r\n  margin: 0;\r\n  padding-left: 20px;\r\n  color: #6c757d;\r\n}\r\n\r\n.credential-info li {\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n  line-height: 1.4;\r\n}\r\n\r\n/* 底部操作按钮 */\r\n.form-actions {\r\n  padding: 24px 32px;\r\n  background: #f8f9fa;\r\n  border-top: 1px solid #e9ecef;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.final-actions {\r\n  display: flex;\r\n  gap: 16px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n  .main-content {\r\n    flex-direction: column;\r\n    gap: 24px;\r\n  }\r\n  \r\n  .info-panel {\r\n    flex: none;\r\n  }\r\n  \r\n  .welcome-card {\r\n    position: static;\r\n  }\r\n  \r\n  .verify-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .main-content {\r\n    padding: 20px;\r\n  }\r\n  \r\n  .nav-content {\r\n    padding: 0 20px;\r\n  }\r\n  \r\n  .company-name {\r\n    font-size: 16px;\r\n  }\r\n  \r\n  .form-content {\r\n    padding: 20px;\r\n  }\r\n  \r\n  .credential-card {\r\n    flex-direction: column;\r\n    text-align: center;\r\n  }\r\n  \r\n  .form-actions {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n}\r\n\r\n/* Element UI 样式覆盖 */\r\n.el-form-item {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.el-input__inner {\r\n  height: 44px;\r\n  border-radius: 8px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.el-input__inner:focus {\r\n  border-color: #3498db;\r\n  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.1);\r\n}\r\n\r\n.el-select {\r\n  width: 100%;\r\n}\r\n\r\n.el-date-editor {\r\n  width: 100%;\r\n}\r\n\r\n.el-button--large {\r\n  height: 44px;\r\n  padding: 12px 24px;\r\n  border-radius: 8px;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n}\r\n\r\n.el-button--primary {\r\n  background: linear-gradient(135deg, #3498db, #2980b9);\r\n  border: none;\r\n}\r\n\r\n.el-button--primary:hover {\r\n  background: linear-gradient(135deg, #2980b9, #1f4e79);\r\n}\r\n\r\n.el-descriptions {\r\n  border-radius: 8px;\r\n}\r\n\r\n.el-table {\r\n  border-radius: 8px;\r\n}\r\n\r\n.el-alert {\r\n  border-radius: 8px;\r\n}\r\n</style>\r\n"]}]}