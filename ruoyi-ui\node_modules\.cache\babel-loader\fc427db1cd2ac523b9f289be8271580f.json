{"remainingRequest": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\store\\modules\\user.js", "dependencies": [{"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\store\\modules\\user.js", "mtime": 1750833543385}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\babel.config.js", "mtime": 1750833543308}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750836929039}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750836940534}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750836931805}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_login", "require", "_auth", "user", "state", "token", "getToken", "name", "avatar", "roles", "permissions", "mutations", "SET_TOKEN", "SET_NAME", "SET_AVATAR", "SET_ROLES", "SET_PERMISSIONS", "actions", "<PERSON><PERSON>", "_ref", "userInfo", "commit", "username", "trim", "password", "code", "uuid", "Promise", "resolve", "reject", "login", "then", "res", "setToken", "catch", "error", "GetInfo", "_ref2", "getInfo", "process", "env", "VUE_APP_BASE_API", "length", "userName", "LogOut", "_ref3", "logout", "removeToken", "FedLogOut", "_ref4", "_default", "exports", "default"], "sources": ["D:/projects/gaoyi-plat/ruoyi-ui/src/store/modules/user.js"], "sourcesContent": ["import { login, logout, getInfo } from '@/api/login'\r\nimport { getToken, setToken, removeToken } from '@/utils/auth'\r\n\r\nconst user = {\r\n  state: {\r\n    token: getToken(),\r\n    name: '',\r\n    avatar: '',\r\n    roles: [],\r\n    permissions: []\r\n  },\r\n\r\n  mutations: {\r\n    SET_TOKEN: (state, token) => {\r\n      state.token = token\r\n    },\r\n    SET_NAME: (state, name) => {\r\n      state.name = name\r\n    },\r\n    SET_AVATAR: (state, avatar) => {\r\n      state.avatar = avatar\r\n    },\r\n    SET_ROLES: (state, roles) => {\r\n      state.roles = roles\r\n    },\r\n    SET_PERMISSIONS: (state, permissions) => {\r\n      state.permissions = permissions\r\n    }\r\n  },\r\n\r\n  actions: {\r\n    // 登录\r\n    Login({ commit }, userInfo) {\r\n      const username = userInfo.username.trim()\r\n      const password = userInfo.password\r\n      const code = userInfo.code\r\n      const uuid = userInfo.uuid\r\n      return new Promise((resolve, reject) => {\r\n        login(username, password, code, uuid).then(res => {\r\n          setToken(res.token)\r\n          commit('SET_TOKEN', res.token)\r\n          resolve()\r\n        }).catch(error => {\r\n          reject(error)\r\n        })\r\n      })\r\n    },\r\n\r\n    // 获取用户信息\r\n    GetInfo({ commit, state }) {\r\n      return new Promise((resolve, reject) => {\r\n        getInfo().then(res => {\r\n          const user = res.user\r\n          const avatar = (user.avatar == \"\" || user.avatar == null) ? require(\"@/assets/images/profile.jpg\") : process.env.VUE_APP_BASE_API + user.avatar;\r\n          if (res.roles && res.roles.length > 0) { // 验证返回的roles是否是一个非空数组\r\n            commit('SET_ROLES', res.roles)\r\n            commit('SET_PERMISSIONS', res.permissions)\r\n          } else {\r\n            commit('SET_ROLES', ['ROLE_DEFAULT'])\r\n          }\r\n          commit('SET_NAME', user.userName)\r\n          commit('SET_AVATAR', avatar)\r\n          resolve(res)\r\n        }).catch(error => {\r\n          reject(error)\r\n        })\r\n      })\r\n    },\r\n\r\n    // 退出系统\r\n    LogOut({ commit, state }) {\r\n      return new Promise((resolve, reject) => {\r\n        logout(state.token).then(() => {\r\n          commit('SET_TOKEN', '')\r\n          commit('SET_ROLES', [])\r\n          commit('SET_PERMISSIONS', [])\r\n          removeToken()\r\n          resolve()\r\n        }).catch(error => {\r\n          reject(error)\r\n        })\r\n      })\r\n    },\r\n\r\n    // 前端 登出\r\n    FedLogOut({ commit }) {\r\n      return new Promise(resolve => {\r\n        commit('SET_TOKEN', '')\r\n        removeToken()\r\n        resolve()\r\n      })\r\n    }\r\n  }\r\n}\r\n\r\nexport default user\r\n"], "mappings": ";;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAEA,IAAME,IAAI,GAAG;EACXC,KAAK,EAAE;IACLC,KAAK,EAAE,IAAAC,cAAQ,EAAC,CAAC;IACjBC,IAAI,EAAE,EAAE;IACRC,MAAM,EAAE,EAAE;IACVC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE;EACf,CAAC;EAEDC,SAAS,EAAE;IACTC,SAAS,EAAE,SAAXA,SAASA,CAAGR,KAAK,EAAEC,KAAK,EAAK;MAC3BD,KAAK,CAACC,KAAK,GAAGA,KAAK;IACrB,CAAC;IACDQ,QAAQ,EAAE,SAAVA,QAAQA,CAAGT,KAAK,EAAEG,IAAI,EAAK;MACzBH,KAAK,CAACG,IAAI,GAAGA,IAAI;IACnB,CAAC;IACDO,UAAU,EAAE,SAAZA,UAAUA,CAAGV,KAAK,EAAEI,MAAM,EAAK;MAC7BJ,KAAK,CAACI,MAAM,GAAGA,MAAM;IACvB,CAAC;IACDO,SAAS,EAAE,SAAXA,SAASA,CAAGX,KAAK,EAAEK,KAAK,EAAK;MAC3BL,KAAK,CAACK,KAAK,GAAGA,KAAK;IACrB,CAAC;IACDO,eAAe,EAAE,SAAjBA,eAAeA,CAAGZ,KAAK,EAAEM,WAAW,EAAK;MACvCN,KAAK,CAACM,WAAW,GAAGA,WAAW;IACjC;EACF,CAAC;EAEDO,OAAO,EAAE;IACP;IACAC,KAAK,WAALA,KAAKA,CAAAC,IAAA,EAAaC,QAAQ,EAAE;MAAA,IAApBC,MAAM,GAAAF,IAAA,CAANE,MAAM;MACZ,IAAMC,QAAQ,GAAGF,QAAQ,CAACE,QAAQ,CAACC,IAAI,CAAC,CAAC;MACzC,IAAMC,QAAQ,GAAGJ,QAAQ,CAACI,QAAQ;MAClC,IAAMC,IAAI,GAAGL,QAAQ,CAACK,IAAI;MAC1B,IAAMC,IAAI,GAAGN,QAAQ,CAACM,IAAI;MAC1B,OAAO,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;QACtC,IAAAC,YAAK,EAACR,QAAQ,EAAEE,QAAQ,EAAEC,IAAI,EAAEC,IAAI,CAAC,CAACK,IAAI,CAAC,UAAAC,GAAG,EAAI;UAChD,IAAAC,cAAQ,EAACD,GAAG,CAAC3B,KAAK,CAAC;UACnBgB,MAAM,CAAC,WAAW,EAAEW,GAAG,CAAC3B,KAAK,CAAC;UAC9BuB,OAAO,CAAC,CAAC;QACX,CAAC,CAAC,CAACM,KAAK,CAAC,UAAAC,KAAK,EAAI;UAChBN,MAAM,CAACM,KAAK,CAAC;QACf,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;IACAC,OAAO,WAAPA,OAAOA,CAAAC,KAAA,EAAoB;MAAA,IAAjBhB,MAAM,GAAAgB,KAAA,CAANhB,MAAM;QAAEjB,KAAK,GAAAiC,KAAA,CAALjC,KAAK;MACrB,OAAO,IAAIuB,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;QACtC,IAAAS,cAAO,EAAC,CAAC,CAACP,IAAI,CAAC,UAAAC,GAAG,EAAI;UACpB,IAAM7B,IAAI,GAAG6B,GAAG,CAAC7B,IAAI;UACrB,IAAMK,MAAM,GAAIL,IAAI,CAACK,MAAM,IAAI,EAAE,IAAIL,IAAI,CAACK,MAAM,IAAI,IAAI,GAAIP,OAAO,CAAC,6BAA6B,CAAC,GAAGsC,OAAO,CAACC,GAAG,CAACC,gBAAgB,GAAGtC,IAAI,CAACK,MAAM;UAC/I,IAAIwB,GAAG,CAACvB,KAAK,IAAIuB,GAAG,CAACvB,KAAK,CAACiC,MAAM,GAAG,CAAC,EAAE;YAAE;YACvCrB,MAAM,CAAC,WAAW,EAAEW,GAAG,CAACvB,KAAK,CAAC;YAC9BY,MAAM,CAAC,iBAAiB,EAAEW,GAAG,CAACtB,WAAW,CAAC;UAC5C,CAAC,MAAM;YACLW,MAAM,CAAC,WAAW,EAAE,CAAC,cAAc,CAAC,CAAC;UACvC;UACAA,MAAM,CAAC,UAAU,EAAElB,IAAI,CAACwC,QAAQ,CAAC;UACjCtB,MAAM,CAAC,YAAY,EAAEb,MAAM,CAAC;UAC5BoB,OAAO,CAACI,GAAG,CAAC;QACd,CAAC,CAAC,CAACE,KAAK,CAAC,UAAAC,KAAK,EAAI;UAChBN,MAAM,CAACM,KAAK,CAAC;QACf,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;IACAS,MAAM,WAANA,MAAMA,CAAAC,KAAA,EAAoB;MAAA,IAAjBxB,MAAM,GAAAwB,KAAA,CAANxB,MAAM;QAAEjB,KAAK,GAAAyC,KAAA,CAALzC,KAAK;MACpB,OAAO,IAAIuB,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;QACtC,IAAAiB,aAAM,EAAC1C,KAAK,CAACC,KAAK,CAAC,CAAC0B,IAAI,CAAC,YAAM;UAC7BV,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;UACvBA,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;UACvBA,MAAM,CAAC,iBAAiB,EAAE,EAAE,CAAC;UAC7B,IAAA0B,iBAAW,EAAC,CAAC;UACbnB,OAAO,CAAC,CAAC;QACX,CAAC,CAAC,CAACM,KAAK,CAAC,UAAAC,KAAK,EAAI;UAChBN,MAAM,CAACM,KAAK,CAAC;QACf,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;IACAa,SAAS,WAATA,SAASA,CAAAC,KAAA,EAAa;MAAA,IAAV5B,MAAM,GAAA4B,KAAA,CAAN5B,MAAM;MAChB,OAAO,IAAIM,OAAO,CAAC,UAAAC,OAAO,EAAI;QAC5BP,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;QACvB,IAAA0B,iBAAW,EAAC,CAAC;QACbnB,OAAO,CAAC,CAAC;MACX,CAAC,CAAC;IACJ;EACF;AACF,CAAC;AAAA,IAAAsB,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEcjD,IAAI", "ignoreList": []}]}