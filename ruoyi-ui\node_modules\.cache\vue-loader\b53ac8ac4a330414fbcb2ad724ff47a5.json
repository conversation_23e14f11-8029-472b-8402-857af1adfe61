{"remainingRequest": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\views\\asc\\visitor\\self-register.vue?vue&type=style&index=0&id=31326bdb&scoped=true&lang=css", "dependencies": [{"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\views\\asc\\visitor\\self-register.vue", "mtime": 1750985468795}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750836933411}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750836980592}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750836940519}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750836929039}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750836957095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi8qIOS4u+WuueWZqCAqLw0KLnBjLXJlZ2lzdGVyLWNvbnRhaW5lciB7DQogIG1pbi1oZWlnaHQ6IDEwMHZoOw0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZjVmN2ZhIDAlLCAjYzNjZmUyIDEwMCUpOw0KICBmb250LWZhbWlseTogJ0hlbHZldGljYSBOZXVlJywgQXJpYWwsIHNhbnMtc2VyaWY7DQp9DQoNCi8qIOmhtumDqOWvvOiIqiAqLw0KLnRvcC1uYXYgew0KICBiYWNrZ3JvdW5kOiAjZmZmOw0KICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpOw0KICBwYWRkaW5nOiAwIDQwcHg7DQogIHBvc2l0aW9uOiBzdGlja3k7DQogIHRvcDogMDsNCiAgei1pbmRleDogMTAwOw0KfQ0KDQoubmF2LWNvbnRlbnQgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGhlaWdodDogNzBweDsNCiAgbWF4LXdpZHRoOiAxNDAwcHg7DQogIG1hcmdpbjogMCBhdXRvOw0KfQ0KDQoubG9nby1zZWN0aW9uIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgZ2FwOiAxNXB4Ow0KfQ0KDQoubmF2LWxvZ28gew0KICBoZWlnaHQ6IDQwcHg7DQp9DQoNCi5jb21wYW55LW5hbWUgew0KICBmb250LXNpemU6IDIwcHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIGNvbG9yOiAjMmMzZTUwOw0KfQ0KDQoubmF2LWFjdGlvbnMgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBnYXA6IDIwcHg7DQp9DQoNCi8qIOS4u+WGheWuueWMuuWfnyAqLw0KLm1haW4tY29udGVudCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIG1heC13aWR0aDogMTQwMHB4Ow0KICBtYXJnaW46IDAgYXV0bzsNCiAgcGFkZGluZzogNDBweDsNCiAgZ2FwOiA0MHB4Ow0KICBtaW4taGVpZ2h0OiBjYWxjKDEwMHZoIC0gMTEwcHgpOw0KfQ0KDQovKiDlt6bkvqfkv6Hmga/pnaLmnb8gKi8NCi5pbmZvLXBhbmVsIHsNCiAgZmxleDogMCAwIDQwMHB4Ow0KfQ0KDQoud2VsY29tZS1jYXJkIHsNCiAgYmFja2dyb3VuZDogI2ZmZjsNCiAgYm9yZGVyLXJhZGl1czogMTZweDsNCiAgcGFkZGluZzogMzJweDsNCiAgYm94LXNoYWRvdzogMCA4cHggMzJweCByZ2JhKDAsIDAsIDAsIDAuMSk7DQogIGhlaWdodDogZml0LWNvbnRlbnQ7DQogIHBvc2l0aW9uOiBzdGlja3k7DQogIHRvcDogMTEwcHg7DQp9DQoNCi53ZWxjb21lLWljb24gew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIG1hcmdpbi1ib3R0b206IDI0cHg7DQp9DQoNCi53ZWxjb21lLWljb24gaSB7DQogIGZvbnQtc2l6ZTogNjRweDsNCiAgY29sb3I6ICMzNDk4ZGI7DQp9DQoNCi53ZWxjb21lLWNhcmQgaDIgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIGNvbG9yOiAjMmMzZTUwOw0KICBtYXJnaW4tYm90dG9tOiAxNnB4Ow0KICBmb250LXNpemU6IDI4cHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQp9DQoNCi53ZWxjb21lLWRlc2Mgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIGNvbG9yOiAjN2Y4YzhkOw0KICBtYXJnaW4tYm90dG9tOiAzMnB4Ow0KICBmb250LXNpemU6IDE2cHg7DQogIGxpbmUtaGVpZ2h0OiAxLjY7DQp9DQoNCi8qIOa1geeoi+atpemqpCAqLw0KLnByb2Nlc3Mtc3RlcHMgew0KICBtYXJnaW4tYm90dG9tOiAzMnB4Ow0KfQ0KDQouc3RlcC1pdGVtIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgZ2FwOiAxNnB4Ow0KICBtYXJnaW4tYm90dG9tOiAyNHB4Ow0KICBwYWRkaW5nOiAxNnB4Ow0KICBib3JkZXItcmFkaXVzOiAxMnB4Ow0KICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOw0KfQ0KDQouc3RlcC1pdGVtLmFjdGl2ZSB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMzNDk4ZGIsICMyOTgwYjkpOw0KICBjb2xvcjogd2hpdGU7DQogIHRyYW5zZm9ybTogdHJhbnNsYXRlWCg4cHgpOw0KfQ0KDQouc3RlcC1pdGVtOm5vdCguYWN0aXZlKSB7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogIGNvbG9yOiAjNmM3NTdkOw0KfQ0KDQouc3RlcC1jaXJjbGUgew0KICB3aWR0aDogMzJweDsNCiAgaGVpZ2h0OiAzMnB4Ow0KICBib3JkZXItcmFkaXVzOiA1MCU7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICBmb250LXdlaWdodDogYm9sZDsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBmbGV4LXNocmluazogMDsNCn0NCg0KLnN0ZXAtaXRlbS5hY3RpdmUgLnN0ZXAtY2lyY2xlIHsNCiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpOw0KICBjb2xvcjogd2hpdGU7DQp9DQoNCi5zdGVwLWl0ZW06bm90KC5hY3RpdmUpIC5zdGVwLWNpcmNsZSB7DQogIGJhY2tncm91bmQ6ICNkZWUyZTY7DQogIGNvbG9yOiAjNmM3NTdkOw0KfQ0KDQouc3RlcC10ZXh0IGg0IHsNCiAgbWFyZ2luOiAwIDAgNHB4IDA7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCn0NCg0KLnN0ZXAtdGV4dCBwIHsNCiAgbWFyZ2luOiAwOw0KICBmb250LXNpemU6IDE0cHg7DQogIG9wYWNpdHk6IDAuODsNCn0NCg0KLyog5a6J5YWo5o+Q56S6ICovDQouc2VjdXJpdHktdGlwcyB7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogIGJvcmRlci1yYWRpdXM6IDEycHg7DQogIHBhZGRpbmc6IDIwcHg7DQogIGJvcmRlci1sZWZ0OiA0cHggc29saWQgIzM0OThkYjsNCn0NCg0KLnNlY3VyaXR5LXRpcHMgaDQgew0KICBtYXJnaW46IDAgMCAxMnB4IDA7DQogIGNvbG9yOiAjMmMzZTUwOw0KICBmb250LXNpemU6IDE2cHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGdhcDogOHB4Ow0KfQ0KDQouc2VjdXJpdHktdGlwcyB1bCB7DQogIG1hcmdpbjogMDsNCiAgcGFkZGluZy1sZWZ0OiAyMHB4Ow0KICBjb2xvcjogIzZjNzU3ZDsNCn0NCg0KLnNlY3VyaXR5LXRpcHMgbGkgew0KICBtYXJnaW4tYm90dG9tOiA4cHg7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgbGluZS1oZWlnaHQ6IDEuNDsNCn0NCg0KLyog5Y+z5L6n6KGo5Y2V6Z2i5p2/ICovDQouZm9ybS1wYW5lbCB7DQogIGZsZXg6IDE7DQogIGJhY2tncm91bmQ6ICNmZmY7DQogIGJvcmRlci1yYWRpdXM6IDE2cHg7DQogIGJveC1zaGFkb3c6IDAgOHB4IDMycHggcmdiYSgwLCAwLCAwLCAwLjEpOw0KICBvdmVyZmxvdzogaGlkZGVuOw0KfQ0KDQovKiDov5vluqbmjIfnpLrlmaggKi8NCi5wcm9ncmVzcy1pbmRpY2F0b3Igew0KICBiYWNrZ3JvdW5kOiAjZjhmOWZhOw0KICBwYWRkaW5nOiAyNHB4IDMycHg7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTllY2VmOw0KfQ0KDQoucHJvZ3Jlc3MtYmFyIHsNCiAgaGVpZ2h0OiA2cHg7DQogIGJhY2tncm91bmQ6ICNlOWVjZWY7DQogIGJvcmRlci1yYWRpdXM6IDNweDsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgbWFyZ2luLWJvdHRvbTogOHB4Ow0KfQ0KDQoucHJvZ3Jlc3MtZmlsbCB7DQogIGhlaWdodDogMTAwJTsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDkwZGVnLCAjMzQ5OGRiLCAjMjk4MGI5KTsNCiAgYm9yZGVyLXJhZGl1czogM3B4Ow0KICB0cmFuc2l0aW9uOiB3aWR0aCAwLjNzIGVhc2U7DQp9DQoNCi5wcm9ncmVzcy10ZXh0IHsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBjb2xvcjogIzZjNzU3ZDsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBmb250LXdlaWdodDogNTAwOw0KfQ0KDQovKiDooajljZXlhoXlrrkgKi8NCi5mb3JtLWNvbnRlbnQgew0KICBwYWRkaW5nOiAzMnB4Ow0KICBtYXgtaGVpZ2h0OiBjYWxjKDEwMHZoIC0gMzAwcHgpOw0KICBvdmVyZmxvdy15OiBhdXRvOw0KfQ0KDQouZm9ybS1oZWFkZXIgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIG1hcmdpbi1ib3R0b206IDMycHg7DQp9DQoNCi5mb3JtLWhlYWRlciBoMyB7DQogIGNvbG9yOiAjMmMzZTUwOw0KICBmb250LXNpemU6IDI0cHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIG1hcmdpbi1ib3R0b206IDhweDsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIGdhcDogMTJweDsNCn0NCg0KLmZvcm0taGVhZGVyIHAgew0KICBjb2xvcjogIzdmOGM4ZDsNCiAgbWFyZ2luOiAwOw0KICBmb250LXNpemU6IDE2cHg7DQp9DQoNCi8qIOmqjOivgeaWueW8j+e9keagvCAqLw0KLnZlcmlmeS1ncmlkIHsNCiAgZGlzcGxheTogZ3JpZDsNCiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoMiwgMWZyKTsNCiAgZ2FwOiAyMHB4Ow0KICBtYXJnaW4tYm90dG9tOiAzMnB4Ow0KfQ0KDQoudmVyaWZ5LWNhcmQgew0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogIGJvcmRlcjogMnB4IHNvbGlkICNlOWVjZWY7DQogIGJvcmRlci1yYWRpdXM6IDEycHg7DQogIHBhZGRpbmc6IDI0cHg7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgY3Vyc29yOiBwb2ludGVyOw0KICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOw0KfQ0KDQoudmVyaWZ5LWNhcmQ6aG92ZXIgew0KICBib3JkZXItY29sb3I6ICMzNDk4ZGI7DQogIGJhY2tncm91bmQ6ICNmZmY7DQogIGJveC1zaGFkb3c6IDAgNHB4IDE2cHggcmdiYSg1MiwgMTUyLCAyMTksIDAuMTUpOw0KICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7DQp9DQoNCi52ZXJpZnktY2FyZC5zZWxlY3RlZCB7DQogIGJvcmRlci1jb2xvcjogIzM0OThkYjsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzM0OThkYiwgIzI5ODBiOSk7DQogIGNvbG9yOiB3aGl0ZTsNCiAgYm94LXNoYWRvdzogMCA4cHggMjRweCByZ2JhKDUyLCAxNTIsIDIxOSwgMC4zKTsNCn0NCg0KLnZlcmlmeS1pY29uIHsNCiAgbWFyZ2luLWJvdHRvbTogMTZweDsNCn0NCg0KLnZlcmlmeS1pY29uIGkgew0KICBmb250LXNpemU6IDQ4cHg7DQogIGNvbG9yOiAjMzQ5OGRiOw0KfQ0KDQoudmVyaWZ5LWNhcmQuc2VsZWN0ZWQgLnZlcmlmeS1pY29uIGkgew0KICBjb2xvcjogd2hpdGU7DQp9DQoNCi52ZXJpZnktY2FyZCBoNCB7DQogIG1hcmdpbjogMCAwIDhweCAwOw0KICBmb250LXNpemU6IDE4cHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQp9DQoNCi52ZXJpZnktY2FyZCBwIHsNCiAgbWFyZ2luOiAwOw0KICBmb250LXNpemU6IDE0cHg7DQogIG9wYWNpdHk6IDAuODsNCn0NCg0KLnZlcmlmeS1zdGF0dXMgew0KICBwb3NpdGlvbjogYWJzb2x1dGU7DQogIHRvcDogMTJweDsNCiAgcmlnaHQ6IDEycHg7DQogIHdpZHRoOiAyNHB4Ow0KICBoZWlnaHQ6IDI0cHg7DQogIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKTsNCiAgYm9yZGVyLXJhZGl1czogNTAlOw0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCn0NCg0KLyog6aqM6K+B5pON5L2c5Yy65Z+fICovDQoudmVyaWZ5LW9wZXJhdGlvbiB7DQogIG1hcmdpbi10b3A6IDMycHg7DQogIHBhZGRpbmc6IDI0cHg7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogIGJvcmRlci1yYWRpdXM6IDEycHg7DQp9DQoNCi5vcGVyYXRpb24taGVhZGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBtYXJnaW4tYm90dG9tOiAyNHB4Ow0KfQ0KDQoub3BlcmF0aW9uLWhlYWRlciBoNCB7DQogIG1hcmdpbjogMDsNCiAgY29sb3I6ICMyYzNlNTA7DQogIGZvbnQtc2l6ZTogMThweDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCn0NCg0KLyog6Lqr5Lu96KGo5Y2VICovDQouaWRlbnRpdHktZm9ybSB7DQogIGJhY2tncm91bmQ6IHdoaXRlOw0KICBwYWRkaW5nOiAyNHB4Ow0KICBib3JkZXItcmFkaXVzOiAxMnB4Ow0KfQ0KDQovKiDouqvku73or4Hor7vljaHlmaggKi8NCi5pZC1jYXJkLXJlYWRlciB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCn0NCg0KLnJlYWRlci12aXN1YWwgew0KICBiYWNrZ3JvdW5kOiB3aGl0ZTsNCiAgcGFkZGluZzogNDBweDsNCiAgYm9yZGVyLXJhZGl1czogMTJweDsNCn0NCg0KLnJlYWRlci1hbmltYXRpb24gew0KICBtYXJnaW4tYm90dG9tOiAyNHB4Ow0KfQ0KDQoucm90YXRpbmcgew0KICBhbmltYXRpb246IHJvdGF0ZSAycyBsaW5lYXIgaW5maW5pdGU7DQp9DQoNCkBrZXlmcmFtZXMgcm90YXRlIHsNCiAgZnJvbSB7IHRyYW5zZm9ybTogcm90YXRlKDBkZWcpOyB9DQogIHRvIHsgdHJhbnNmb3JtOiByb3RhdGUoMzYwZGVnKTsgfQ0KfQ0KDQoucmVhZGVyLWFuaW1hdGlvbiBpIHsNCiAgZm9udC1zaXplOiA2NHB4Ow0KICBjb2xvcjogIzM0OThkYjsNCn0NCg0KLnJlYWRlci12aXN1YWwgaDQgew0KICBjb2xvcjogIzJjM2U1MDsNCiAgbWFyZ2luLWJvdHRvbTogOHB4Ow0KfQ0KDQoucmVhZGVyLXZpc3VhbCBwIHsNCiAgY29sb3I6ICM3ZjhjOGQ7DQogIG1hcmdpbi1ib3R0b206IDI0cHg7DQp9DQoNCi5yZWFkZXItdGlwcyB7DQogIG1hcmdpbjogMjRweCAwOw0KfQ0KDQovKiDkurrohLjor4bliKsgKi8NCi5mYWNlLXJlY29nbml0aW9uIHsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KfQ0KDQouY2FtZXJhLWNvbnRhaW5lciB7DQogIGJhY2tncm91bmQ6IHdoaXRlOw0KICBwYWRkaW5nOiA0MHB4Ow0KICBib3JkZXItcmFkaXVzOiAxMnB4Ow0KfQ0KDQouY2FtZXJhLWZyYW1lIHsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KICB3aWR0aDogMjgwcHg7DQogIGhlaWdodDogMjEwcHg7DQogIG1hcmdpbjogMCBhdXRvIDI0cHg7DQogIGJvcmRlcjogM3B4IHNvbGlkICMzNDk4ZGI7DQogIGJvcmRlci1yYWRpdXM6IDEycHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICBiYWNrZ3JvdW5kOiAjZjhmOWZhOw0KICBvdmVyZmxvdzogaGlkZGVuOw0KfQ0KDQouY2FtZXJhLW92ZXJsYXkgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBnYXA6IDE2cHg7DQp9DQoNCi5mYWNlLW91dGxpbmUgew0KICB3aWR0aDogMTIwcHg7DQogIGhlaWdodDogMTUwcHg7DQogIGJvcmRlcjogMnB4IGRhc2hlZCAjMzQ5OGRiOw0KICBib3JkZXItcmFkaXVzOiA2MHB4Ow0KICBvcGFjaXR5OiAwLjY7DQp9DQoNCi5jYW1lcmEtaWNvbiB7DQogIGZvbnQtc2l6ZTogMzJweDsNCiAgY29sb3I6ICMzNDk4ZGI7DQp9DQoNCi5zY2FubmluZy1saW5lIHsNCiAgcG9zaXRpb246IGFic29sdXRlOw0KICB0b3A6IDA7DQogIGxlZnQ6IDA7DQogIHJpZ2h0OiAwOw0KICBoZWlnaHQ6IDJweDsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDkwZGVnLCB0cmFuc3BhcmVudCwgIzM0OThkYiwgdHJhbnNwYXJlbnQpOw0KICBhbmltYXRpb246IHNjYW4gMnMgZWFzZS1pbi1vdXQgaW5maW5pdGU7DQp9DQoNCkBrZXlmcmFtZXMgc2NhbiB7DQogIDAlIHsgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApOyB9DQogIDUwJSB7IHRyYW5zZm9ybTogdHJhbnNsYXRlWSgyMDZweCk7IH0NCiAgMTAwJSB7IHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKTsgfQ0KfQ0KDQovKiDooajljZXliIbnu4QgKi8NCi5mb3JtLXNlY3Rpb24gew0KICBtYXJnaW4tYm90dG9tOiAzMnB4Ow0KfQ0KDQouc2VjdGlvbi10aXRsZSB7DQogIGNvbG9yOiAjMmMzZTUwOw0KICBmb250LXNpemU6IDE4cHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIG1hcmdpbi1ib3R0b206IDE2cHg7DQogIHBhZGRpbmctYm90dG9tOiA4cHg7DQogIGJvcmRlci1ib3R0b206IDJweCBzb2xpZCAjZTllY2VmOw0KfQ0KDQouc2VjdGlvbi1oZWFkZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIG1hcmdpbi1ib3R0b206IDE2cHg7DQp9DQoNCi8qIOWQjOihjOS6uuWRmCAqLw0KLm5vLWNvbXBhbmlvbnMgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIHBhZGRpbmc6IDQwcHg7DQogIGNvbG9yOiAjN2Y4YzhkOw0KfQ0KDQoubm8tY29tcGFuaW9ucyBpIHsNCiAgZm9udC1zaXplOiA0OHB4Ow0KICBtYXJnaW4tYm90dG9tOiAxNnB4Ow0KICBjb2xvcjogI2JkYzNjNzsNCn0NCg0KLmNvbXBhbmlvbnMtdGFibGUgew0KICBiYWNrZ3JvdW5kOiAjZjhmOWZhOw0KICBwYWRkaW5nOiAxNnB4Ow0KICBib3JkZXItcmFkaXVzOiA4cHg7DQp9DQoNCi8qIOaIkOWKn+mhtemdoiAqLw0KLnN1Y2Nlc3MtY29udGVudCB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCn0NCg0KLnN1Y2Nlc3MtaWNvbiB7DQogIG1hcmdpbi1ib3R0b206IDI0cHg7DQp9DQoNCi5zdWNjZXNzLWljb24gaSB7DQogIGZvbnQtc2l6ZTogODBweDsNCiAgY29sb3I6ICMyN2FlNjA7DQp9DQoNCi5zdWNjZXNzLWNvbnRlbnQgaDQgew0KICBjb2xvcjogIzJjM2U1MDsNCiAgZm9udC1zaXplOiAyOHB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KICBtYXJnaW4tYm90dG9tOiAxMnB4Ow0KfQ0KDQouc3VjY2Vzcy1tZXNzYWdlIHsNCiAgY29sb3I6ICM3ZjhjOGQ7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgbWFyZ2luLWJvdHRvbTogMzJweDsNCn0NCg0KLnJlZ2lzdGVyLXN1bW1hcnkgew0KICBiYWNrZ3JvdW5kOiAjZjhmOWZhOw0KICBwYWRkaW5nOiAyNHB4Ow0KICBib3JkZXItcmFkaXVzOiAxMnB4Ow0KICBtYXJnaW4tYm90dG9tOiAzMnB4Ow0KICB0ZXh0LWFsaWduOiBsZWZ0Ow0KfQ0KDQoucmVnaXN0ZXItc3VtbWFyeSBoNSB7DQogIGNvbG9yOiAjMmMzZTUwOw0KICBmb250LXNpemU6IDE4cHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIG1hcmdpbi1ib3R0b206IDE2cHg7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCn0NCg0KLmFjY2Vzcy1jcmVkZW50aWFsIHsNCiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsNCiAgcGFkZGluZzogMjRweDsNCiAgYm9yZGVyLXJhZGl1czogMTJweDsNCiAgdGV4dC1hbGlnbjogbGVmdDsNCn0NCg0KLmFjY2Vzcy1jcmVkZW50aWFsIGg1IHsNCiAgY29sb3I6ICMyYzNlNTA7DQogIGZvbnQtc2l6ZTogMThweDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgbWFyZ2luLWJvdHRvbTogMTZweDsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KfQ0KDQouY3JlZGVudGlhbC1jYXJkIHsNCiAgZGlzcGxheTogZmxleDsNCiAgZ2FwOiAyNHB4Ow0KICBiYWNrZ3JvdW5kOiB3aGl0ZTsNCiAgcGFkZGluZzogMjRweDsNCiAgYm9yZGVyLXJhZGl1czogMTJweDsNCiAgYm9yZGVyOiAycHggc29saWQgI2U5ZWNlZjsNCn0NCg0KLnFyLWNvZGUtY29udGFpbmVyIHsNCiAgZmxleDogMCAwIDEyMHB4Ow0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQp9DQoNCi5xci1jb2RlIHsNCiAgd2lkdGg6IDEyMHB4Ow0KICBoZWlnaHQ6IDEyMHB4Ow0KICBib3JkZXI6IDJweCBzb2xpZCAjZTllY2VmOw0KICBib3JkZXItcmFkaXVzOiA4cHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICBiYWNrZ3JvdW5kOiAjZjhmOWZhOw0KICBtYXJnaW4tYm90dG9tOiA4cHg7DQp9DQoNCi5xci1jb2RlIGkgew0KICBmb250LXNpemU6IDQ4cHg7DQogIGNvbG9yOiAjN2Y4YzhkOw0KfQ0KDQoucXItY29kZS1pZCB7DQogIGZvbnQtc2l6ZTogMTJweDsNCiAgY29sb3I6ICM2Yzc1N2Q7DQogIGZvbnQtZmFtaWx5OiBtb25vc3BhY2U7DQogIG1hcmdpbjogMDsNCn0NCg0KLmNyZWRlbnRpYWwtaW5mbyB7DQogIGZsZXg6IDE7DQp9DQoNCi5jcmVkZW50aWFsLWluZm8gaDYgew0KICBjb2xvcjogIzJjM2U1MDsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KICBtYXJnaW4tYm90dG9tOiAxMnB4Ow0KfQ0KDQouY3JlZGVudGlhbC1pbmZvIHVsIHsNCiAgbWFyZ2luOiAwOw0KICBwYWRkaW5nLWxlZnQ6IDIwcHg7DQogIGNvbG9yOiAjNmM3NTdkOw0KfQ0KDQouY3JlZGVudGlhbC1pbmZvIGxpIHsNCiAgbWFyZ2luLWJvdHRvbTogOHB4Ow0KICBmb250LXNpemU6IDE0cHg7DQogIGxpbmUtaGVpZ2h0OiAxLjQ7DQp9DQoNCi8qIOW6lemDqOaTjeS9nOaMiemSriAqLw0KLmZvcm0tYWN0aW9ucyB7DQogIHBhZGRpbmc6IDI0cHggMzJweDsNCiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsNCiAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNlOWVjZWY7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCn0NCg0KLmZpbmFsLWFjdGlvbnMgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBnYXA6IDE2cHg7DQp9DQoNCi8qIOWTjeW6lOW8j+iuvuiuoSAqLw0KQG1lZGlhIChtYXgtd2lkdGg6IDEyMDBweCkgew0KICAubWFpbi1jb250ZW50IHsNCiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgIGdhcDogMjRweDsNCiAgfQ0KICANCiAgLmluZm8tcGFuZWwgew0KICAgIGZsZXg6IG5vbmU7DQogIH0NCiAgDQogIC53ZWxjb21lLWNhcmQgew0KICAgIHBvc2l0aW9uOiBzdGF0aWM7DQogIH0NCiAgDQogIC52ZXJpZnktZ3JpZCB7DQogICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAxZnI7DQogIH0NCn0NCg0KQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7DQogIC5tYWluLWNvbnRlbnQgew0KICAgIHBhZGRpbmc6IDIwcHg7DQogIH0NCiAgDQogIC5uYXYtY29udGVudCB7DQogICAgcGFkZGluZzogMCAyMHB4Ow0KICB9DQogIA0KICAuY29tcGFueS1uYW1lIHsNCiAgICBmb250LXNpemU6IDE2cHg7DQogIH0NCiAgDQogIC5mb3JtLWNvbnRlbnQgew0KICAgIHBhZGRpbmc6IDIwcHg7DQogIH0NCiAgDQogIC5jcmVkZW50aWFsLWNhcmQgew0KICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgdGV4dC1hbGlnbjogY2VudGVyOw0KICB9DQogIA0KICAuZm9ybS1hY3Rpb25zIHsNCiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgIGdhcDogMTZweDsNCiAgfQ0KfQ0KDQovKiBFbGVtZW50IFVJIOagt+W8j+imhuebliAqLw0KLmVsLWZvcm0taXRlbSB7DQogIG1hcmdpbi1ib3R0b206IDI0cHg7DQp9DQoNCi5lbC1pbnB1dF9faW5uZXIgew0KICBoZWlnaHQ6IDQ0cHg7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgYm9yZGVyOiAxcHggc29saWQgI2U5ZWNlZjsNCn0NCg0KLmVsLWlucHV0X19pbm5lcjpmb2N1cyB7DQogIGJvcmRlci1jb2xvcjogIzM0OThkYjsNCiAgYm94LXNoYWRvdzogMCAwIDAgMnB4IHJnYmEoNTIsIDE1MiwgMjE5LCAwLjEpOw0KfQ0KDQouZWwtc2VsZWN0IHsNCiAgd2lkdGg6IDEwMCU7DQp9DQoNCi5lbC1kYXRlLWVkaXRvciB7DQogIHdpZHRoOiAxMDAlOw0KfQ0KDQouZWwtYnV0dG9uLS1sYXJnZSB7DQogIGhlaWdodDogNDRweDsNCiAgcGFkZGluZzogMTJweCAyNHB4Ow0KICBib3JkZXItcmFkaXVzOiA4cHg7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgZm9udC13ZWlnaHQ6IDUwMDsNCn0NCg0KLmVsLWJ1dHRvbi0tcHJpbWFyeSB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMzNDk4ZGIsICMyOTgwYjkpOw0KICBib3JkZXI6IG5vbmU7DQp9DQoNCi5lbC1idXR0b24tLXByaW1hcnk6aG92ZXIgew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMjk4MGI5LCAjMWY0ZTc5KTsNCn0NCg0KLmVsLWRlc2NyaXB0aW9ucyB7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCn0NCg0KLmVsLXRhYmxlIHsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KfQ0KDQouZWwtYWxlcnQgew0KICBib3JkZXItcmFkaXVzOiA4cHg7DQp9DQo="}, {"version": 3, "sources": ["self-register.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+iCA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "self-register.vue", "sourceRoot": "src/views/asc/visitor", "sourcesContent": ["<template>\r\n  <div class=\"pc-register-container\">\r\n    <!-- 顶部导航栏 -->\r\n    <div class=\"top-nav\">\r\n      <div class=\"nav-content\">\r\n        <div class=\"logo-section\">\r\n          <img src=\"@/assets/logo/logo.png\" alt=\"公司logo\" class=\"nav-logo\" />\r\n          <span class=\"company-name\">智慧园区访客登记系统</span>\r\n        </div>\r\n        <div class=\"nav-actions\">\r\n          <el-button type=\"text\" @click=\"showHelp\">使用帮助</el-button>\r\n          <el-button type=\"text\" @click=\"contactService\">联系客服</el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 主要内容区域 -->\r\n    <div class=\"main-content\">\r\n      <!-- 左侧信息面板 -->\r\n      <div class=\"info-panel\">\r\n        <div class=\"welcome-card\">\r\n          <div class=\"welcome-icon\">\r\n            <i class=\"el-icon-office-building\"></i>\r\n          </div>\r\n          <h2>欢迎访问智慧园区</h2>\r\n          <p class=\"welcome-desc\">为了您的安全和便利，请按照以下步骤完成访客登记</p>\r\n          \r\n          <div class=\"process-steps\">\r\n            <div class=\"step-item active\">\r\n              <div class=\"step-circle\">1</div>\r\n              <div class=\"step-text\">\r\n                <h4>填写信息</h4>\r\n                <p>完善访问详细信息</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"step-item\" :class=\"{ active: registrationCompleted }\">\r\n              <div class=\"step-circle\">2</div>\r\n              <div class=\"step-text\">\r\n                <h4>获取凭证</h4>\r\n                <p>生成访问凭证二维码</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"security-tips\">\r\n            <h4><i class=\"el-icon-lock\"></i> 安全提示</h4>\r\n            <ul>\r\n              <li>请确保提供真实有效的身份信息</li>\r\n              <li>您的个人信息将被严格保密</li>\r\n              <li>访问凭证仅限当次使用</li>\r\n            </ul>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 右侧表单区域 -->\r\n      <div class=\"form-panel\">\r\n        <!-- 进度指示器 -->\r\n        <div class=\"progress-indicator\">\r\n          <div class=\"progress-bar\">\r\n            <div class=\"progress-fill\" :style=\"{ width: progressWidth }\"></div>\r\n          </div>\r\n          <div class=\"progress-text\">\r\n            步骤 {{ currentStep + 1 }} / 3\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 表单内容 -->\r\n        <div class=\"form-content\">\r\n          <!-- 步骤1: 身份验证 -->\r\n          <div v-show=\"currentStep === 0\" class=\"step-form\">\r\n            <div class=\"form-header\">\r\n              <h3><i class=\"el-icon-user\"></i> 身份验证</h3>\r\n              <p>请选择合适的验证方式进行身份确认</p>\r\n            </div>\r\n            \r\n            <div class=\"verify-grid\">\r\n              <div class=\"verify-card\" \r\n                   :class=\"{ selected: verifyMethod === 'id_card' }\"\r\n                   @click=\"selectVerifyMethod('id_card')\">\r\n                <div class=\"verify-icon\">\r\n                  <i class=\"el-icon-postcard\"></i>\r\n                </div>\r\n                <h4>身份证验证</h4>\r\n                <p>使用身份证读卡器快速验证</p>\r\n                <div class=\"verify-status\" v-if=\"verifyMethod === 'id_card'\">\r\n                  <i class=\"el-icon-check\"></i>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"verify-card\"\r\n                   :class=\"{ selected: verifyMethod === 'face' }\"\r\n                   @click=\"selectVerifyMethod('face')\">\r\n                <div class=\"verify-icon\">\r\n                  <i class=\"el-icon-camera-solid\"></i>\r\n                </div>\r\n                <h4>人脸识别</h4>\r\n                <p>使用摄像头进行人脸识别</p>\r\n                <div class=\"verify-status\" v-if=\"verifyMethod === 'face'\">\r\n                  <i class=\"el-icon-check\"></i>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"verify-card\"\r\n                   :class=\"{ selected: verifyMethod === 'manual' }\"\r\n                   @click=\"selectVerifyMethod('manual')\">\r\n                <div class=\"verify-icon\">\r\n                  <i class=\"el-icon-edit-outline\"></i>\r\n                </div>\r\n                <h4>手动输入</h4>\r\n                <p>手动填写身份信息</p>\r\n                <div class=\"verify-status\" v-if=\"verifyMethod === 'manual'\">\r\n                  <i class=\"el-icon-check\"></i>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"verify-card\"\r\n                   :class=\"{ selected: verifyMethod === 'passport' }\"\r\n                   @click=\"selectVerifyMethod('passport')\">\r\n                <div class=\"verify-icon\">\r\n                  <i class=\"el-icon-document\"></i>\r\n                </div>\r\n                <h4>护照验证</h4>\r\n                <p>使用护照等其他证件</p>\r\n                <div class=\"verify-status\" v-if=\"verifyMethod === 'passport'\">\r\n                  <i class=\"el-icon-check\"></i>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 验证方式具体操作区域 -->\r\n            <div v-if=\"showManualInput\" class=\"verify-operation\">\r\n              <div class=\"operation-header\">\r\n                <h4>请填写身份信息</h4>\r\n                <el-button type=\"text\" @click=\"backToVerifyMethod\">\r\n                  <i class=\"el-icon-arrow-left\"></i> 重新选择验证方式\r\n                </el-button>\r\n              </div>\r\n              \r\n              <el-form ref=\"identityForm\" :model=\"identityForm\" :rules=\"identityRules\" \r\n                       label-width=\"100px\" class=\"identity-form\">\r\n                <el-row :gutter=\"20\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"姓名\" prop=\"name\">\r\n                      <el-input v-model=\"identityForm.name\" placeholder=\"请输入真实姓名\" \r\n                                prefix-icon=\"el-icon-user\" />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"手机号\" prop=\"phone\">\r\n                      <el-input v-model=\"identityForm.phone\" placeholder=\"请输入手机号码\"\r\n                                prefix-icon=\"el-icon-phone\" />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                \r\n                <el-row :gutter=\"20\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"证件类型\" prop=\"idType\">\r\n                      <el-select v-model=\"identityForm.idType\" placeholder=\"请选择证件类型\" style=\"width: 100%\">\r\n                        <el-option label=\"身份证\" value=\"id_card\" />\r\n                        <el-option label=\"护照\" value=\"passport\" />\r\n                        <el-option label=\"港澳通行证\" value=\"hk_mo_pass\" />\r\n                        <el-option label=\"台湾通行证\" value=\"tw_pass\" />\r\n                        <el-option label=\"其他\" value=\"other\" />\r\n                      </el-select>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"证件号码\" prop=\"idNumber\">\r\n                      <el-input v-model=\"identityForm.idNumber\" placeholder=\"请输入证件号码\"\r\n                                prefix-icon=\"el-icon-postcard\" />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                \r\n                <el-form-item label=\"所属公司\" prop=\"company\">\r\n                  <el-input v-model=\"identityForm.company\" placeholder=\"请输入公司名称\"\r\n                            prefix-icon=\"el-icon-office-building\" />\r\n                </el-form-item>\r\n              </el-form>\r\n            </div>\r\n\r\n            <!-- 身份证读卡器界面 -->\r\n            <div v-if=\"showIdCardReader\" class=\"verify-operation\">\r\n              <div class=\"operation-header\">\r\n                <h4>身份证读卡验证</h4>\r\n                <el-button type=\"text\" @click=\"backToVerifyMethod\">\r\n                  <i class=\"el-icon-arrow-left\"></i> 重新选择验证方式\r\n                </el-button>\r\n              </div>\r\n              \r\n              <div class=\"id-card-reader\">\r\n                <div class=\"reader-visual\">\r\n                  <div class=\"reader-animation\">\r\n                    <i class=\"el-icon-loading rotating\"></i>\r\n                  </div>\r\n                  <h4>请将身份证放置在读卡器上</h4>\r\n                  <p>确保身份证平放在读卡器感应区域</p>\r\n                  <div class=\"reader-tips\">\r\n                    <el-alert title=\"提示\" type=\"info\" :closable=\"false\">\r\n                      如果读卡失败，请检查身份证是否放置正确，或选择其他验证方式\r\n                    </el-alert>\r\n                  </div>\r\n                  <el-button type=\"primary\" @click=\"simulateIdCardRead\" style=\"margin-top: 20px;\">\r\n                    模拟读取成功\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 人脸识别界面 -->\r\n            <div v-if=\"showFaceRecognition\" class=\"verify-operation\">\r\n              <div class=\"operation-header\">\r\n                <h4>人脸识别验证</h4>\r\n                <el-button type=\"text\" @click=\"backToVerifyMethod\">\r\n                  <i class=\"el-icon-arrow-left\"></i> 重新选择验证方式\r\n                </el-button>\r\n              </div>\r\n              \r\n              <div class=\"face-recognition\">\r\n                <div class=\"camera-container\">\r\n                  <div class=\"camera-frame\">\r\n                    <div class=\"camera-overlay\">\r\n                      <div class=\"face-outline\"></div>\r\n                      <i class=\"el-icon-camera-solid camera-icon\"></i>\r\n                    </div>\r\n                    <div class=\"scanning-line\"></div>\r\n                  </div>\r\n                  <h4>请面向摄像头</h4>\r\n                  <p>保持面部清晰可见，等待识别完成</p>\r\n                  <el-button type=\"primary\" @click=\"simulateFaceRecognition\" style=\"margin-top: 20px;\">\r\n                    模拟识别成功\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 步骤2: 填写访问信息 -->\r\n          <div v-show=\"currentStep === 1\" class=\"step-form\">\r\n            <div class=\"form-header\">\r\n              <h3><i class=\"el-icon-edit\"></i> 完善访问信息</h3>\r\n              <p>请填写详细的访问信息</p>\r\n            </div>\r\n\r\n            <el-form ref=\"visitForm\" :model=\"visitForm\" :rules=\"visitRules\" \r\n                     label-width=\"120px\" class=\"visit-form\">\r\n              \r\n              <!-- 基本访问信息 -->\r\n              <div class=\"form-section\">\r\n                <h4 class=\"section-title\">基本访问信息</h4>\r\n                <el-row :gutter=\"20\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"来访事由\" prop=\"reasonForVisit\">\r\n                      <el-input v-model=\"visitForm.reasonForVisit\" placeholder=\"请输入来访事由\"\r\n                                prefix-icon=\"el-icon-tickets\" type=\"textarea\" :rows=\"3\" maxlength=\"200\" show-word-limit />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"被访人姓名\" prop=\"hostEmployeeName\">\r\n                      <el-input v-model=\"visitForm.hostEmployeeName\" placeholder=\"请输入被访人姓名\"\r\n                                prefix-icon=\"el-icon-user\" maxlength=\"20\" />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n\r\n                <el-row :gutter=\"20\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"被访部门\" prop=\"departmentVisited\">\r\n                      <el-input v-model=\"visitForm.departmentVisited\" placeholder=\"请输入被访部门\"\r\n                                prefix-icon=\"el-icon-office-building\" maxlength=\"50\" />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"车牌号\">\r\n                      <el-input v-model=\"visitForm.vehiclePlateNumber\" placeholder=\"如有车辆请填写车牌号，多个用逗号分隔\"\r\n                                prefix-icon=\"el-icon-truck\" />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n              </div>\r\n\r\n              <!-- 时间信息 -->\r\n              <div class=\"form-section\">\r\n                <h4 class=\"section-title\">访问时间</h4>\r\n                <el-row :gutter=\"20\">\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"预计到访时间\" prop=\"plannedEntryDatetime\">\r\n                      <el-date-picker\r\n                        v-model=\"visitForm.plannedEntryDatetime\"\r\n                        type=\"datetime\"\r\n                        placeholder=\"选择到访时间\"\r\n                        style=\"width: 100%\"\r\n                        value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                        :picker-options=\"entryPickerOptions\"\r\n                        @change=\"onArrivalTimeChange\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"预计离开时间\" prop=\"plannedExitDatetime\">\r\n                      <el-date-picker\r\n                        v-model=\"visitForm.plannedExitDatetime\"\r\n                        type=\"datetime\"\r\n                        placeholder=\"选择离开时间\"\r\n                        style=\"width: 100%\"\r\n                        value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                        :picker-options=\"exitPickerOptions\"\r\n                        @change=\"onDepartureTimeChange\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n              </div>\r\n\r\n              <!-- 同行人员信息 -->\r\n              <div class=\"form-section\">\r\n                <div class=\"section-header\">\r\n                  <h4 class=\"section-title\">访客信息（第一位为主联系人）共 {{ totalVisitors }} 人</h4>\r\n                  <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"addCompanion\" :disabled=\"companionList.length >= 9\">\r\n                    添加访客\r\n                  </el-button>\r\n                </div>\r\n                \r\n                <div v-if=\"companionList.length === 0\" class=\"no-companions\">\r\n                  <i class=\"el-icon-user-solid\"></i>\r\n                  <p>暂无同行人员，主访客信息已在身份验证步骤中填写</p>\r\n                </div>\r\n                \r\n                <div v-else class=\"companions-table\">\r\n                  <el-table :data=\"companionList\" border style=\"width: 100%;\">\r\n                    <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        访客{{ scope.$index + 2 }}\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"姓名\" width=\"150\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-input v-model=\"scope.row.name\" placeholder=\"请输入姓名\" size=\"small\" maxlength=\"20\" />\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"手机号\" width=\"150\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-input v-model=\"scope.row.phone\" placeholder=\"请输入手机号\" size=\"small\" />\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"身份证号\" min-width=\"180\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-input v-model=\"scope.row.idNumber\" placeholder=\"请输入身份证号\" size=\"small\" />\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"公司\" width=\"150\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-input v-model=\"scope.row.company\" placeholder=\"请输入公司（可选）\" size=\"small\" maxlength=\"100\" />\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"操作\" width=\"80\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-button size=\"mini\" type=\"danger\" icon=\"el-icon-delete\" \r\n                                   @click=\"removeCompanion(scope.$index)\" circle />\r\n                      </template>\r\n                    </el-table-column>\r\n                  </el-table>\r\n                </div>\r\n              </div>\r\n            </el-form>\r\n          </div>\r\n\r\n          <!-- 步骤3: 完成登记 -->\r\n          <div v-show=\"currentStep === 2\" class=\"step-form\">\r\n            <div class=\"form-header\">\r\n              <h3><i class=\"el-icon-circle-check\"></i> 登记完成</h3>\r\n              <p>您的访客登记已成功提交</p>\r\n            </div>\r\n\r\n            <div class=\"success-content\">\r\n              <div class=\"success-icon\">\r\n                <i class=\"el-icon-circle-check\"></i>\r\n              </div>\r\n              \r\n              <h4>登记成功！</h4>\r\n              <p class=\"success-message\">您的访客登记已提交，请等待审核通过</p>\r\n              \r\n              <!-- 登记信息摘要 -->\r\n              <div class=\"register-summary\">\r\n                <h5>登记信息摘要</h5>\r\n                <el-descriptions :column=\"2\" border>\r\n                  <el-descriptions-item label=\"访客姓名\">\r\n                    {{ identityForm.name }}\r\n                  </el-descriptions-item>\r\n                  <el-descriptions-item label=\"联系电话\">\r\n                    {{ identityForm.phone }}\r\n                  </el-descriptions-item>\r\n                  <el-descriptions-item label=\"被访人\">\r\n                    {{ visitForm.hostEmployeeName }}\r\n                  </el-descriptions-item>\r\n                  <el-descriptions-item label=\"被访部门\">\r\n                    {{ visitForm.departmentVisited }}\r\n                  </el-descriptions-item>\r\n                  <el-descriptions-item label=\"来访事由\" span=\"2\">\r\n                    {{ visitForm.reasonForVisit }}\r\n                  </el-descriptions-item>\r\n                  <el-descriptions-item label=\"预计到访时间\">\r\n                    {{ parseTime(visitForm.plannedEntryDatetime) }}\r\n                  </el-descriptions-item>\r\n                  <el-descriptions-item label=\"预计离开时间\">\r\n                    {{ parseTime(visitForm.plannedExitDatetime) }}\r\n                  </el-descriptions-item>\r\n                </el-descriptions>\r\n              </div>\r\n\r\n              <!-- 访问凭证 -->\r\n              <div class=\"access-credential\" v-if=\"registrationId\">\r\n                <h5>访问凭证</h5>\r\n                <div class=\"credential-card\">\r\n                  <div class=\"qr-code-container\">\r\n                    <div class=\"qr-code\">\r\n                      <i class=\"el-icon-qrcode\"></i>\r\n                    </div>\r\n                    <p class=\"qr-code-id\">{{ registrationId }}</p>\r\n                  </div>\r\n                  <div class=\"credential-info\">\r\n                    <h6>使用说明</h6>\r\n                    <ul>\r\n                      <li>请保存此二维码截图</li>\r\n                      <li>审核通过后可用于园区门禁</li>\r\n                      <li>凭证仅限当次访问使用</li>\r\n                      <li>如有疑问请联系园区前台</li>\r\n                    </ul>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 底部操作按钮 -->\r\n        <div class=\"form-actions\">\r\n          <el-button v-if=\"currentStep > 0\" @click=\"prevStep\" size=\"large\">\r\n            <i class=\"el-icon-arrow-left\"></i> 上一步\r\n          </el-button>\r\n          \r\n          <el-button v-if=\"currentStep === 0 && verifyMethod && !showVerifyOperation\" \r\n                     type=\"primary\" @click=\"handleVerifyNext\" size=\"large\" :loading=\"verifying\">\r\n            下一步 <i class=\"el-icon-arrow-right\"></i>\r\n          </el-button>\r\n          \r\n          <el-button v-if=\"currentStep === 0 && showManualInput\" \r\n                     type=\"primary\" @click=\"confirmIdentity\" size=\"large\" :loading=\"verifying\">\r\n            确认身份 <i class=\"el-icon-arrow-right\"></i>\r\n          </el-button>\r\n          \r\n          <el-button v-if=\"currentStep === 1\" \r\n                     type=\"primary\" @click=\"nextStep\" size=\"large\" :loading=\"submitting\">\r\n            提交登记 <i class=\"el-icon-arrow-right\"></i>\r\n          </el-button>\r\n          \r\n          <div v-if=\"currentStep === 2\" class=\"final-actions\">\r\n            <el-button type=\"primary\" @click=\"printCredential\" size=\"large\">\r\n              <i class=\"el-icon-printer\"></i> 打印凭证\r\n            </el-button>\r\n            <el-button @click=\"resetRegister\" size=\"large\">\r\n              <i class=\"el-icon-refresh\"></i> 重新登记\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { submitVisitorRegistration } from \"@/api/asc/visitor\";\r\n\r\nexport default {\r\n  name: \"SelfRegister\",\r\n  data() {\r\n    return {\r\n      currentStep: 0,\r\n      verifying: false,\r\n      submitting: false,\r\n      \r\n      // 验证方式\r\n      verifyMethod: '',\r\n      showManualInput: false,\r\n      showIdCardReader: false,\r\n      showFaceRecognition: false,\r\n      \r\n      // 身份信息\r\n      identityForm: {\r\n        name: '',\r\n        phone: '',\r\n        idType: '',\r\n        idNumber: '',\r\n        company: ''\r\n      },\r\n      identityRules: {\r\n        name: [\r\n          { required: true, message: '请输入姓名', trigger: 'blur' },\r\n          { min: 2, max: 20, message: '姓名长度应在2-20个字符之间', trigger: 'blur' },\r\n          { pattern: /^[\\u4e00-\\u9fa5a-zA-Z·\\s]+$/, message: '姓名只能包含中文、英文字母和常见符号', trigger: 'blur' }\r\n        ],\r\n        phone: [\r\n          { required: true, message: '请输入手机号', trigger: 'blur' },\r\n          { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }\r\n        ],\r\n        idType: [{ required: true, message: '请选择证件类型', trigger: 'change' }],\r\n        idNumber: [\r\n          { required: true, message: '请输入证件号码', trigger: 'blur' },\r\n          { min: 15, max: 18, message: '证件号码长度不正确', trigger: 'blur' }\r\n        ],\r\n        company: [{ max: 100, message: '公司名称不能超过100个字符', trigger: 'blur' }]\r\n      },\r\n      \r\n      // 访问信息\r\n      visitForm: {\r\n        reasonForVisit: '',\r\n        hostEmployeeName: '',\r\n        departmentVisited: '',\r\n        vehiclePlateNumber: '',\r\n        plannedEntryDatetime: null,\r\n        plannedExitDatetime: null\r\n      },\r\n      visitRules: {\r\n        reasonForVisit: [\r\n          { required: true, message: '请输入来访事由', trigger: 'blur' },\r\n          { min: 2, max: 200, message: '来访事由长度应在2-200个字符之间', trigger: 'blur' }\r\n        ],\r\n        hostEmployeeName: [\r\n          { required: true, message: '请输入被访人姓名', trigger: 'blur' },\r\n          { min: 2, max: 20, message: '被访人姓名长度应在2-20个字符之间', trigger: 'blur' }\r\n        ],\r\n        departmentVisited: [\r\n          { required: true, message: '请输入被访部门', trigger: 'blur' },\r\n          { min: 2, max: 50, message: '部门名称长度应在2-50个字符之间', trigger: 'blur' }\r\n        ],\r\n        plannedEntryDatetime: [{ required: true, message: '请选择预计到访时间', trigger: 'change' }],\r\n        plannedExitDatetime: [{ required: true, message: '请选择预计离开时间', trigger: 'change' }]\r\n      },\r\n      \r\n      // 同行人员\r\n      companionList: [],\r\n      \r\n      // 登记结果\r\n      registrationId: null,\r\n\r\n      // 时间选择器配置\r\n      entryPickerOptions: {\r\n        shortcuts: [{\r\n          text: '现在',\r\n          onClick(picker) {\r\n            picker.$emit('pick', new Date());\r\n          }\r\n        }, {\r\n          text: '1小时后',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setTime(date.getTime() + 3600 * 1000);\r\n            picker.$emit('pick', date);\r\n          }\r\n        }, {\r\n          text: '明天上午9点',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setDate(date.getDate() + 1);\r\n            date.setHours(9, 0, 0, 0);\r\n            picker.$emit('pick', date);\r\n          }\r\n        }],\r\n        disabledDate(time) {\r\n          // 允许选择当前时间前1小时到未来30天\r\n          const oneHourBefore = Date.now() - 3600 * 1000;\r\n          const thirtyDaysLater = Date.now() + 30 * 24 * 3600 * 1000;\r\n          return time.getTime() < oneHourBefore || time.getTime() > thirtyDaysLater;\r\n        }\r\n      },\r\n      \r\n      exitPickerOptions: {\r\n        shortcuts: [{\r\n          text: '2小时后',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setTime(date.getTime() + 2 * 3600 * 1000);\r\n            picker.$emit('pick', date);\r\n          }\r\n        }, {\r\n          text: '今天下午6点',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setHours(18, 0, 0, 0);\r\n            if (date.getTime() < Date.now()) {\r\n              date.setDate(date.getDate() + 1);\r\n            }\r\n            picker.$emit('pick', date);\r\n          }\r\n        }, {\r\n          text: '明天下午6点',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setDate(date.getDate() + 1);\r\n            date.setHours(18, 0, 0, 0);\r\n            picker.$emit('pick', date);\r\n          }\r\n        }],\r\n        disabledDate(time) {\r\n          // 允许选择当前时间到未来30天\r\n          const thirtyDaysLater = Date.now() + 30 * 24 * 3600 * 1000;\r\n          return time.getTime() < Date.now() - 8.64e7 || time.getTime() > thirtyDaysLater;\r\n        }\r\n      }\r\n    };\r\n  },\r\n  \r\n  computed: {\r\n    progressWidth() {\r\n      return ((this.currentStep + 1) / 3) * 100 + '%';\r\n    },\r\n    \r\n    showVerifyOperation() {\r\n      return this.showManualInput || this.showIdCardReader || this.showFaceRecognition;\r\n    },\r\n\r\n    // 总访客人数（主访客 + 同行人员）\r\n    totalVisitors() {\r\n      return 1 + this.companionList.length;\r\n    }\r\n  },\r\n  \r\n  methods: {\r\n    /** 选择验证方式 */\r\n    selectVerifyMethod(method) {\r\n      this.verifyMethod = method;\r\n      this.resetVerifyDisplay();\r\n      \r\n      switch (method) {\r\n        case 'manual':\r\n          this.showManualInput = true;\r\n          break;\r\n        case 'id_card':\r\n          this.showIdCardReader = true;\r\n          break;\r\n        case 'face':\r\n          this.showFaceRecognition = true;\r\n          break;\r\n        case 'passport':\r\n          this.showManualInput = true;\r\n          this.identityForm.idType = 'passport';\r\n          break;\r\n      }\r\n    },\r\n    \r\n    /** 重置验证显示 */\r\n    resetVerifyDisplay() {\r\n      this.showManualInput = false;\r\n      this.showIdCardReader = false;\r\n      this.showFaceRecognition = false;\r\n    },\r\n    \r\n    /** 返回验证方式选择 */\r\n    backToVerifyMethod() {\r\n      this.resetVerifyDisplay();\r\n      this.verifyMethod = '';\r\n    },\r\n\r\n    /** 处理验证方式下一步 */\r\n    handleVerifyNext() {\r\n      if (this.verifyMethod === 'manual') {\r\n        this.showManualInput = true;\r\n      } else if (this.verifyMethod === 'id_card') {\r\n        this.showIdCardReader = true;\r\n      } else if (this.verifyMethod === 'face') {\r\n        this.showFaceRecognition = true;\r\n      } else if (this.verifyMethod === 'passport') {\r\n        this.showManualInput = true;\r\n        this.identityForm.idType = 'passport';\r\n      }\r\n    },\r\n    \r\n    /** 确认身份 */\r\n    confirmIdentity() {\r\n      this.$refs.identityForm.validate(valid => {\r\n        if (valid) {\r\n          this.verifying = true;\r\n          // 模拟验证过程\r\n          setTimeout(() => {\r\n            this.verifying = false;\r\n            this.$message.success('身份验证成功');\r\n            this.nextStep();\r\n          }, 1000);\r\n        }\r\n      });\r\n    },\r\n    \r\n    /** 模拟身份证读取 */\r\n    simulateIdCardRead() {\r\n      this.identityForm = {\r\n        name: '张三',\r\n        phone: '13800138000',\r\n        idType: 'id_card',\r\n        idNumber: '110101199001011234',\r\n        company: '某某科技有限公司'\r\n      };\r\n      this.$message.success('身份证信息读取成功');\r\n      setTimeout(() => {\r\n        this.nextStep();\r\n      }, 1000);\r\n    },\r\n    \r\n    /** 模拟人脸识别 */\r\n    simulateFaceRecognition() {\r\n      this.identityForm = {\r\n        name: '李四',\r\n        phone: '13900139000',\r\n        idType: 'id_card',\r\n        idNumber: '110101199002021234',\r\n        company: '某某贸易有限公司'\r\n      };\r\n      this.$message.success('人脸识别成功');\r\n      setTimeout(() => {\r\n        this.nextStep();\r\n      }, 1000);\r\n    },\r\n    \r\n    /** 下一步 */\r\n    nextStep() {\r\n      if (this.currentStep === 1) {\r\n        this.submitRegistration();\r\n      } else {\r\n        this.currentStep++;\r\n      }\r\n    },\r\n    \r\n    /** 上一步 */\r\n    prevStep() {\r\n      this.currentStep--;\r\n      if (this.currentStep === 0) {\r\n        this.resetVerifyDisplay();\r\n      }\r\n    },\r\n    \r\n    /** 添加同行人员 */\r\n    addCompanion() {\r\n      if (this.companionList.length >= 9) {\r\n        this.$message.warning('最多只能添加9名同行人员（总计10名访客）');\r\n        return;\r\n      }\r\n\r\n      this.companionList.push({\r\n        name: '',\r\n        phone: '',\r\n        idNumber: '',\r\n        company: ''\r\n      });\r\n      \r\n      this.$message.success('已添加访客');\r\n    },\r\n    \r\n    /** 来访时间变更事件 */\r\n    onArrivalTimeChange(value) {\r\n      console.log('预计来访时间变更:', value);\r\n      // 自动设置离开时间为来访时间后4小时\r\n      if (value && !this.visitForm.plannedExitDatetime) {\r\n        const arrivalTime = new Date(value);\r\n        const departureTime = new Date(arrivalTime.getTime() + 4 * 60 * 60 * 1000);\r\n        \r\n        const year = departureTime.getFullYear();\r\n        const month = String(departureTime.getMonth() + 1).padStart(2, '0');\r\n        const day = String(departureTime.getDate()).padStart(2, '0');\r\n        const hours = String(departureTime.getHours()).padStart(2, '0');\r\n        const minutes = String(departureTime.getMinutes()).padStart(2, '0');\r\n        const seconds = String(departureTime.getSeconds()).padStart(2, '0');\r\n        \r\n        this.visitForm.plannedExitDatetime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n      }\r\n    },\r\n\r\n    /** 离开时间变更事件 */\r\n    onDepartureTimeChange(value) {\r\n      console.log('预计离开时间变更:', value);\r\n      // 验证离开时间不能早于来访时间\r\n      if (value && this.visitForm.plannedEntryDatetime) {\r\n        const arrivalTime = new Date(this.visitForm.plannedEntryDatetime);\r\n        const departureTime = new Date(value);\r\n\r\n        if (departureTime <= arrivalTime) {\r\n          this.$message.warning('预计离开时间不能早于或等于来访时间');\r\n          this.visitForm.plannedExitDatetime = '';\r\n        }\r\n      }\r\n    },\r\n    \r\n    /** 提交登记 */\r\n    submitRegistration() {\r\n      this.$refs.visitForm.validate(valid => {\r\n        if (valid) {\r\n          // 验证同行人员信息\r\n          if (!this.validateVisitors()) {\r\n            return;\r\n          }\r\n\r\n          // 验证时间\r\n          if (!this.validateTimes()) {\r\n            return;\r\n          }\r\n\r\n          this.submitting = true;\r\n          \r\n          // 构建提交数据 - 按照微信端相同的数据结构\r\n          const submitData = {\r\n            // VisitRegistrations 对象\r\n            registration: {\r\n              primaryContactName: this.identityForm.name.trim(),\r\n              primaryContactPhone: this.identityForm.phone.trim(),\r\n              reasonForVisit: this.visitForm.reasonForVisit.trim(),\r\n              hostEmployeeName: this.visitForm.hostEmployeeName.trim(),\r\n              hostEmployeeId: null, // 前端暂时没有被访人ID，后端可能会自动处理\r\n              departmentVisited: this.visitForm.departmentVisited.trim(),\r\n              plannedEntryDatetime: this.formatDateForBackend(this.visitForm.plannedEntryDatetime),\r\n              plannedExitDatetime: this.formatDateForBackend(this.visitForm.plannedExitDatetime),\r\n              vehiclePlateNumber: this.visitForm.vehiclePlateNumber ? this.visitForm.vehiclePlateNumber.trim() : '',\r\n              totalCompanions: this.companionList.length\r\n            },\r\n            // RegistrationAttendees 数组 - 使用后端期望的临时字段\r\n            attendeesList: [\r\n              // 主访客\r\n              {\r\n                visitorName: this.identityForm.name.trim(),\r\n                visitorPhone: this.identityForm.phone.trim(),\r\n                visitorIdCard: this.identityForm.idNumber.trim().toUpperCase(),\r\n                visitorCompany: this.identityForm.company ? this.identityForm.company.trim() : '',\r\n                isPrimary: \"1\", // 第一个访客必须是主联系人\r\n                visitorAvatarPhoto: null // 暂时不支持头像上传\r\n              },\r\n              // 同行人员\r\n              ...this.companionList\r\n                .filter(item => item.name && item.phone && item.idNumber)\r\n                .map(item => ({\r\n                  visitorName: item.name.trim(),\r\n                  visitorPhone: item.phone.trim(),\r\n                  visitorIdCard: item.idNumber.trim().toUpperCase(),\r\n                  visitorCompany: item.company ? item.company.trim() : '',\r\n                  isPrimary: \"0\",\r\n                  visitorAvatarPhoto: null\r\n                }))\r\n            ]\r\n          };\r\n\r\n          console.log('提交数据结构:', JSON.stringify(submitData, null, 2));\r\n          \r\n          submitVisitorRegistration(submitData)\r\n            .then(response => {\r\n              this.registrationId = response.data || 'VR' + Date.now();\r\n              this.$message.success(`${this.totalVisitors}名访客登记成功！`);\r\n              this.currentStep++;\r\n            })\r\n            .catch(error => {\r\n              this.$message.error(error.msg || '登记失败，请重试');\r\n            })\r\n            .finally(() => {\r\n              this.submitting = false;\r\n            });\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 验证访客信息 */\r\n    validateVisitors() {\r\n      // 验证主访客信息（已在身份验证步骤中验证，这里再次确认）\r\n      if (!this.identityForm.name || this.identityForm.name.trim().length < 2) {\r\n        this.$message.error('主访客姓名不能为空且长度不能少于2个字符');\r\n        return false;\r\n      }\r\n\r\n      if (!this.identityForm.phone || !/^1[3-9]\\d{9}$/.test(this.identityForm.phone.trim())) {\r\n        this.$message.error('主访客手机号格式不正确');\r\n        return false;\r\n      }\r\n\r\n      if (!this.identityForm.idNumber || this.identityForm.idNumber.trim().length < 15) {\r\n        this.$message.error('主访客身份证号不能为空');\r\n        return false;\r\n      }\r\n\r\n      // 验证同行人员信息\r\n      for (let i = 0; i < this.companionList.length; i++) {\r\n        const visitor = this.companionList[i];\r\n        const visitorTitle = `访客${i + 2}`;\r\n\r\n        // 验证姓名\r\n        if (!visitor.name || visitor.name.trim() === '') {\r\n          this.$message.error(`请输入${visitorTitle}的姓名`);\r\n          return false;\r\n        }\r\n        \r\n        if (visitor.name.trim().length < 2 || visitor.name.trim().length > 20) {\r\n          this.$message.error(`${visitorTitle}的姓名长度应在2-20个字符之间`);\r\n          return false;\r\n        }\r\n\r\n        // 验证姓名格式\r\n        const namePattern = /^[\\u4e00-\\u9fa5a-zA-Z·\\s]+$/;\r\n        if (!namePattern.test(visitor.name.trim())) {\r\n          this.$message.error(`${visitorTitle}的姓名只能包含中文、英文字母和常见符号`);\r\n          return false;\r\n        }\r\n\r\n        // 验证手机号\r\n        if (!visitor.phone || visitor.phone.trim() === '') {\r\n          this.$message.error(`请输入${visitorTitle}的手机号`);\r\n          return false;\r\n        }\r\n\r\n        const phonePattern = /^1[3-9]\\d{9}$/;\r\n        if (!phonePattern.test(visitor.phone.trim())) {\r\n          this.$message.error(`${visitorTitle}的手机号格式不正确`);\r\n          return false;\r\n        }\r\n\r\n        // 验证身份证号\r\n        if (!visitor.idNumber || visitor.idNumber.trim() === '') {\r\n          this.$message.error(`请输入${visitorTitle}的身份证号`);\r\n          return false;\r\n        }\r\n        \r\n        const idCard = visitor.idNumber.trim().toUpperCase();\r\n        \r\n        // 如果是18位身份证号，验证格式\r\n        if (idCard.length === 18) {\r\n          const idPattern = /^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$/;\r\n          if (!idPattern.test(idCard)) {\r\n            this.$message.error(`${visitorTitle}的身份证号格式不正确`);\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 检查身份证号是否与主访客重复\r\n        if (visitor.idNumber.trim().toUpperCase() === this.identityForm.idNumber.trim().toUpperCase()) {\r\n          this.$message.error(`${visitorTitle}与主访客的身份证号重复`);\r\n          return false;\r\n        }\r\n\r\n        // 检查身份证号是否与其他同行人员重复\r\n        for (let j = 0; j < this.companionList.length; j++) {\r\n          if (i !== j && visitor.idNumber.trim().toUpperCase() === this.companionList[j].idNumber.trim().toUpperCase()) {\r\n            this.$message.error(`${visitorTitle}与访客${j + 2}的身份证号重复`);\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 检查手机号是否与主访客重复\r\n        if (visitor.phone.trim() === this.identityForm.phone.trim()) {\r\n          this.$message.error(`${visitorTitle}与主访客的手机号重复，请确认是否为同一人`);\r\n          return false;\r\n        }\r\n\r\n        // 检查手机号是否与其他同行人员重复\r\n        for (let j = 0; j < this.companionList.length; j++) {\r\n          if (i !== j && visitor.phone.trim() === this.companionList[j].phone.trim()) {\r\n            this.$message.error(`${visitorTitle}与访客${j + 2}的手机号重复，请确认是否为同一人`);\r\n            return false;\r\n          }\r\n        }\r\n        \r\n        // 清理和标准化数据\r\n        visitor.name = visitor.name.trim();\r\n        visitor.phone = visitor.phone.trim();\r\n        visitor.idNumber = visitor.idNumber.trim().toUpperCase();\r\n        visitor.company = (visitor.company || '').trim();\r\n      }\r\n\r\n      return true;\r\n    },\r\n\r\n    /** 验证时间 */\r\n    validateTimes() {\r\n      if (!this.visitForm.plannedEntryDatetime) {\r\n        this.$message.error('请选择预计来访时间');\r\n        return false;\r\n      }\r\n\r\n      if (!this.visitForm.plannedExitDatetime) {\r\n        this.$message.error('请选择预计离开时间');\r\n        return false;\r\n      }\r\n\r\n      const arrivalTime = new Date(this.visitForm.plannedEntryDatetime);\r\n      const departureTime = new Date(this.visitForm.plannedExitDatetime);\r\n      const now = new Date();\r\n\r\n      // 验证来访时间不能是过去时间（允许当前时间前1小时的误差）\r\n      const oneHourBefore = new Date(now.getTime() - 60 * 60 * 1000);\r\n      if (arrivalTime < oneHourBefore) {\r\n        this.$message.error('预计来访时间不能是过去时间');\r\n        return false;\r\n      }\r\n\r\n      // 验证离开时间必须晚于来访时间\r\n      if (departureTime <= arrivalTime) {\r\n        this.$message.error('预计离开时间必须晚于来访时间');\r\n        return false;\r\n      }\r\n\r\n      return true;\r\n    },\r\n\r\n    /** 格式化日期为后端期望的格式 */\r\n    formatDateForBackend(dateStr) {\r\n      if (!dateStr) return '';\r\n      \r\n      const date = new Date(dateStr);\r\n      if (isNaN(date.getTime())) return '';\r\n      \r\n      const year = date.getFullYear();\r\n      const month = String(date.getMonth() + 1).padStart(2, '0');\r\n      const day = String(date.getDate()).padStart(2, '0');\r\n      const hours = String(date.getHours()).padStart(2, '0');\r\n      const minutes = String(date.getMinutes()).padStart(2, '0');\r\n      const seconds = String(date.getSeconds()).padStart(2, '0');\r\n      \r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n    },\r\n    \r\n    /** 重新登记 */\r\n    resetRegister() {\r\n      this.currentStep = 0;\r\n      this.resetVerifyDisplay();\r\n      this.verifyMethod = '';\r\n      this.identityForm = {\r\n        name: '',\r\n        phone: '',\r\n        idType: '',\r\n        idNumber: '',\r\n        company: ''\r\n      };\r\n      this.visitForm = {\r\n        reasonForVisit: '',\r\n        hostEmployeeName: '',\r\n        departmentVisited: '',\r\n        vehiclePlateNumber: '',\r\n        plannedEntryDatetime: null,\r\n        plannedExitDatetime: null\r\n      };\r\n      this.companionList = [];\r\n      this.registrationId = null;\r\n    },\r\n    \r\n    /** 打印凭证 */\r\n    printCredential() {\r\n      this.$message.info('正在生成打印文件...');\r\n      // TODO: 实现打印功能\r\n    },\r\n\r\n    /** 显示帮助 */\r\n    showHelp() {\r\n      this.$alert('如需帮助，请联系园区前台或拨打服务热线', '使用帮助', {\r\n        confirmButtonText: '确定'\r\n      });\r\n    },\r\n\r\n    /** 联系客服 */\r\n    contactService() {\r\n      this.$alert('服务热线：400-1234-567\\n服务时间：周一至周五 8:00-18:00', '联系客服', {\r\n        confirmButtonText: '确定'\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 主容器 */\r\n.pc-register-container {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n  font-family: 'Helvetica Neue', Arial, sans-serif;\r\n}\r\n\r\n/* 顶部导航 */\r\n.top-nav {\r\n  background: #fff;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  padding: 0 40px;\r\n  position: sticky;\r\n  top: 0;\r\n  z-index: 100;\r\n}\r\n\r\n.nav-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  height: 70px;\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.logo-section {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n}\r\n\r\n.nav-logo {\r\n  height: 40px;\r\n}\r\n\r\n.company-name {\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n.nav-actions {\r\n  display: flex;\r\n  gap: 20px;\r\n}\r\n\r\n/* 主内容区域 */\r\n.main-content {\r\n  display: flex;\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n  padding: 40px;\r\n  gap: 40px;\r\n  min-height: calc(100vh - 110px);\r\n}\r\n\r\n/* 左侧信息面板 */\r\n.info-panel {\r\n  flex: 0 0 400px;\r\n}\r\n\r\n.welcome-card {\r\n  background: #fff;\r\n  border-radius: 16px;\r\n  padding: 32px;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\r\n  height: fit-content;\r\n  position: sticky;\r\n  top: 110px;\r\n}\r\n\r\n.welcome-icon {\r\n  text-align: center;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.welcome-icon i {\r\n  font-size: 64px;\r\n  color: #3498db;\r\n}\r\n\r\n.welcome-card h2 {\r\n  text-align: center;\r\n  color: #2c3e50;\r\n  margin-bottom: 16px;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n}\r\n\r\n.welcome-desc {\r\n  text-align: center;\r\n  color: #7f8c8d;\r\n  margin-bottom: 32px;\r\n  font-size: 16px;\r\n  line-height: 1.6;\r\n}\r\n\r\n/* 流程步骤 */\r\n.process-steps {\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.step-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n  margin-bottom: 24px;\r\n  padding: 16px;\r\n  border-radius: 12px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.step-item.active {\r\n  background: linear-gradient(135deg, #3498db, #2980b9);\r\n  color: white;\r\n  transform: translateX(8px);\r\n}\r\n\r\n.step-item:not(.active) {\r\n  background: #f8f9fa;\r\n  color: #6c757d;\r\n}\r\n\r\n.step-circle {\r\n  width: 32px;\r\n  height: 32px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-weight: bold;\r\n  font-size: 14px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.step-item.active .step-circle {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  color: white;\r\n}\r\n\r\n.step-item:not(.active) .step-circle {\r\n  background: #dee2e6;\r\n  color: #6c757d;\r\n}\r\n\r\n.step-text h4 {\r\n  margin: 0 0 4px 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.step-text p {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  opacity: 0.8;\r\n}\r\n\r\n/* 安全提示 */\r\n.security-tips {\r\n  background: #f8f9fa;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  border-left: 4px solid #3498db;\r\n}\r\n\r\n.security-tips h4 {\r\n  margin: 0 0 12px 0;\r\n  color: #2c3e50;\r\n  font-size: 16px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.security-tips ul {\r\n  margin: 0;\r\n  padding-left: 20px;\r\n  color: #6c757d;\r\n}\r\n\r\n.security-tips li {\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n  line-height: 1.4;\r\n}\r\n\r\n/* 右侧表单面板 */\r\n.form-panel {\r\n  flex: 1;\r\n  background: #fff;\r\n  border-radius: 16px;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n/* 进度指示器 */\r\n.progress-indicator {\r\n  background: #f8f9fa;\r\n  padding: 24px 32px;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.progress-bar {\r\n  height: 6px;\r\n  background: #e9ecef;\r\n  border-radius: 3px;\r\n  overflow: hidden;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.progress-fill {\r\n  height: 100%;\r\n  background: linear-gradient(90deg, #3498db, #2980b9);\r\n  border-radius: 3px;\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.progress-text {\r\n  text-align: center;\r\n  color: #6c757d;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 表单内容 */\r\n.form-content {\r\n  padding: 32px;\r\n  max-height: calc(100vh - 300px);\r\n  overflow-y: auto;\r\n}\r\n\r\n.form-header {\r\n  text-align: center;\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.form-header h3 {\r\n  color: #2c3e50;\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  margin-bottom: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 12px;\r\n}\r\n\r\n.form-header p {\r\n  color: #7f8c8d;\r\n  margin: 0;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 验证方式网格 */\r\n.verify-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 20px;\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.verify-card {\r\n  position: relative;\r\n  background: #f8f9fa;\r\n  border: 2px solid #e9ecef;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.verify-card:hover {\r\n  border-color: #3498db;\r\n  background: #fff;\r\n  box-shadow: 0 4px 16px rgba(52, 152, 219, 0.15);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.verify-card.selected {\r\n  border-color: #3498db;\r\n  background: linear-gradient(135deg, #3498db, #2980b9);\r\n  color: white;\r\n  box-shadow: 0 8px 24px rgba(52, 152, 219, 0.3);\r\n}\r\n\r\n.verify-icon {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.verify-icon i {\r\n  font-size: 48px;\r\n  color: #3498db;\r\n}\r\n\r\n.verify-card.selected .verify-icon i {\r\n  color: white;\r\n}\r\n\r\n.verify-card h4 {\r\n  margin: 0 0 8px 0;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.verify-card p {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  opacity: 0.8;\r\n}\r\n\r\n.verify-status {\r\n  position: absolute;\r\n  top: 12px;\r\n  right: 12px;\r\n  width: 24px;\r\n  height: 24px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n/* 验证操作区域 */\r\n.verify-operation {\r\n  margin-top: 32px;\r\n  padding: 24px;\r\n  background: #f8f9fa;\r\n  border-radius: 12px;\r\n}\r\n\r\n.operation-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.operation-header h4 {\r\n  margin: 0;\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n/* 身份表单 */\r\n.identity-form {\r\n  background: white;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n}\r\n\r\n/* 身份证读卡器 */\r\n.id-card-reader {\r\n  text-align: center;\r\n}\r\n\r\n.reader-visual {\r\n  background: white;\r\n  padding: 40px;\r\n  border-radius: 12px;\r\n}\r\n\r\n.reader-animation {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.rotating {\r\n  animation: rotate 2s linear infinite;\r\n}\r\n\r\n@keyframes rotate {\r\n  from { transform: rotate(0deg); }\r\n  to { transform: rotate(360deg); }\r\n}\r\n\r\n.reader-animation i {\r\n  font-size: 64px;\r\n  color: #3498db;\r\n}\r\n\r\n.reader-visual h4 {\r\n  color: #2c3e50;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.reader-visual p {\r\n  color: #7f8c8d;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.reader-tips {\r\n  margin: 24px 0;\r\n}\r\n\r\n/* 人脸识别 */\r\n.face-recognition {\r\n  text-align: center;\r\n}\r\n\r\n.camera-container {\r\n  background: white;\r\n  padding: 40px;\r\n  border-radius: 12px;\r\n}\r\n\r\n.camera-frame {\r\n  position: relative;\r\n  width: 280px;\r\n  height: 210px;\r\n  margin: 0 auto 24px;\r\n  border: 3px solid #3498db;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #f8f9fa;\r\n  overflow: hidden;\r\n}\r\n\r\n.camera-overlay {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 16px;\r\n}\r\n\r\n.face-outline {\r\n  width: 120px;\r\n  height: 150px;\r\n  border: 2px dashed #3498db;\r\n  border-radius: 60px;\r\n  opacity: 0.6;\r\n}\r\n\r\n.camera-icon {\r\n  font-size: 32px;\r\n  color: #3498db;\r\n}\r\n\r\n.scanning-line {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 2px;\r\n  background: linear-gradient(90deg, transparent, #3498db, transparent);\r\n  animation: scan 2s ease-in-out infinite;\r\n}\r\n\r\n@keyframes scan {\r\n  0% { transform: translateY(0); }\r\n  50% { transform: translateY(206px); }\r\n  100% { transform: translateY(0); }\r\n}\r\n\r\n/* 表单分组 */\r\n.form-section {\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.section-title {\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin-bottom: 16px;\r\n  padding-bottom: 8px;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n/* 同行人员 */\r\n.no-companions {\r\n  text-align: center;\r\n  padding: 40px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.no-companions i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  color: #bdc3c7;\r\n}\r\n\r\n.companions-table {\r\n  background: #f8f9fa;\r\n  padding: 16px;\r\n  border-radius: 8px;\r\n}\r\n\r\n/* 成功页面 */\r\n.success-content {\r\n  text-align: center;\r\n}\r\n\r\n.success-icon {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.success-icon i {\r\n  font-size: 80px;\r\n  color: #27ae60;\r\n}\r\n\r\n.success-content h4 {\r\n  color: #2c3e50;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.success-message {\r\n  color: #7f8c8d;\r\n  font-size: 16px;\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.register-summary {\r\n  background: #f8f9fa;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n  margin-bottom: 32px;\r\n  text-align: left;\r\n}\r\n\r\n.register-summary h5 {\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin-bottom: 16px;\r\n  text-align: center;\r\n}\r\n\r\n.access-credential {\r\n  background: #f8f9fa;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n  text-align: left;\r\n}\r\n\r\n.access-credential h5 {\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin-bottom: 16px;\r\n  text-align: center;\r\n}\r\n\r\n.credential-card {\r\n  display: flex;\r\n  gap: 24px;\r\n  background: white;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n  border: 2px solid #e9ecef;\r\n}\r\n\r\n.qr-code-container {\r\n  flex: 0 0 120px;\r\n  text-align: center;\r\n}\r\n\r\n.qr-code {\r\n  width: 120px;\r\n  height: 120px;\r\n  border: 2px solid #e9ecef;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #f8f9fa;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.qr-code i {\r\n  font-size: 48px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.qr-code-id {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n  font-family: monospace;\r\n  margin: 0;\r\n}\r\n\r\n.credential-info {\r\n  flex: 1;\r\n}\r\n\r\n.credential-info h6 {\r\n  color: #2c3e50;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.credential-info ul {\r\n  margin: 0;\r\n  padding-left: 20px;\r\n  color: #6c757d;\r\n}\r\n\r\n.credential-info li {\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n  line-height: 1.4;\r\n}\r\n\r\n/* 底部操作按钮 */\r\n.form-actions {\r\n  padding: 24px 32px;\r\n  background: #f8f9fa;\r\n  border-top: 1px solid #e9ecef;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.final-actions {\r\n  display: flex;\r\n  gap: 16px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n  .main-content {\r\n    flex-direction: column;\r\n    gap: 24px;\r\n  }\r\n  \r\n  .info-panel {\r\n    flex: none;\r\n  }\r\n  \r\n  .welcome-card {\r\n    position: static;\r\n  }\r\n  \r\n  .verify-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .main-content {\r\n    padding: 20px;\r\n  }\r\n  \r\n  .nav-content {\r\n    padding: 0 20px;\r\n  }\r\n  \r\n  .company-name {\r\n    font-size: 16px;\r\n  }\r\n  \r\n  .form-content {\r\n    padding: 20px;\r\n  }\r\n  \r\n  .credential-card {\r\n    flex-direction: column;\r\n    text-align: center;\r\n  }\r\n  \r\n  .form-actions {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n}\r\n\r\n/* Element UI 样式覆盖 */\r\n.el-form-item {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.el-input__inner {\r\n  height: 44px;\r\n  border-radius: 8px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.el-input__inner:focus {\r\n  border-color: #3498db;\r\n  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.1);\r\n}\r\n\r\n.el-select {\r\n  width: 100%;\r\n}\r\n\r\n.el-date-editor {\r\n  width: 100%;\r\n}\r\n\r\n.el-button--large {\r\n  height: 44px;\r\n  padding: 12px 24px;\r\n  border-radius: 8px;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n}\r\n\r\n.el-button--primary {\r\n  background: linear-gradient(135deg, #3498db, #2980b9);\r\n  border: none;\r\n}\r\n\r\n.el-button--primary:hover {\r\n  background: linear-gradient(135deg, #2980b9, #1f4e79);\r\n}\r\n\r\n.el-descriptions {\r\n  border-radius: 8px;\r\n}\r\n\r\n.el-table {\r\n  border-radius: 8px;\r\n}\r\n\r\n.el-alert {\r\n  border-radius: 8px;\r\n}\r\n</style>\r\n"]}]}