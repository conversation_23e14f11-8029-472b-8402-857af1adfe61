{"remainingRequest": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\views\\asc\\visitor\\self-register.vue?vue&type=style&index=0&id=31326bdb&scoped=true&lang=css", "dependencies": [{"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\views\\asc\\visitor\\self-register.vue", "mtime": 1750986040997}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750836933411}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750836980592}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750836940519}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750836929039}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750836957095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi8qIOS4u+WuueWZqCAqLw0KLnBjLXJlZ2lzdGVyLWNvbnRhaW5lciB7DQogIG1pbi1oZWlnaHQ6IDEwMHZoOw0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZjVmN2ZhIDAlLCAjYzNjZmUyIDEwMCUpOw0KICBmb250LWZhbWlseTogJ0hlbHZldGljYSBOZXVlJywgQXJpYWwsIHNhbnMtc2VyaWY7DQp9DQoNCi8qIOmhtumDqOWvvOiIqiAqLw0KLnRvcC1uYXYgew0KICBiYWNrZ3JvdW5kOiAjZmZmOw0KICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpOw0KICBwYWRkaW5nOiAwIDQwcHg7DQogIHBvc2l0aW9uOiBzdGlja3k7DQogIHRvcDogMDsNCiAgei1pbmRleDogMTAwOw0KfQ0KDQoubmF2LWNvbnRlbnQgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGhlaWdodDogNzBweDsNCiAgbWF4LXdpZHRoOiAxNDAwcHg7DQogIG1hcmdpbjogMCBhdXRvOw0KfQ0KDQoubG9nby1zZWN0aW9uIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgZ2FwOiAxNXB4Ow0KfQ0KDQoubmF2LWxvZ28gew0KICBoZWlnaHQ6IDQwcHg7DQp9DQoNCi5jb21wYW55LW5hbWUgew0KICBmb250LXNpemU6IDIwcHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIGNvbG9yOiAjMmMzZTUwOw0KfQ0KDQoubmF2LWFjdGlvbnMgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBnYXA6IDIwcHg7DQp9DQoNCi8qIOS4u+WGheWuueWMuuWfnyAqLw0KLm1haW4tY29udGVudCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIG1heC13aWR0aDogMTQwMHB4Ow0KICBtYXJnaW46IDAgYXV0bzsNCiAgcGFkZGluZzogNDBweDsNCiAgZ2FwOiA0MHB4Ow0KICBtaW4taGVpZ2h0OiBjYWxjKDEwMHZoIC0gMTEwcHgpOw0KfQ0KDQovKiDlt6bkvqfkv6Hmga/pnaLmnb8gKi8NCi5pbmZvLXBhbmVsIHsNCiAgZmxleDogMCAwIDQwMHB4Ow0KfQ0KDQoud2VsY29tZS1jYXJkIHsNCiAgYmFja2dyb3VuZDogI2ZmZjsNCiAgYm9yZGVyLXJhZGl1czogMTZweDsNCiAgcGFkZGluZzogMzJweDsNCiAgYm94LXNoYWRvdzogMCA4cHggMzJweCByZ2JhKDAsIDAsIDAsIDAuMSk7DQogIGhlaWdodDogZml0LWNvbnRlbnQ7DQogIHBvc2l0aW9uOiBzdGlja3k7DQogIHRvcDogMTEwcHg7DQp9DQoNCi53ZWxjb21lLWljb24gew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIG1hcmdpbi1ib3R0b206IDI0cHg7DQp9DQoNCi53ZWxjb21lLWljb24gaSB7DQogIGZvbnQtc2l6ZTogNjRweDsNCiAgY29sb3I6ICMzNDk4ZGI7DQp9DQoNCi53ZWxjb21lLWNhcmQgaDIgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIGNvbG9yOiAjMmMzZTUwOw0KICBtYXJnaW4tYm90dG9tOiAxNnB4Ow0KICBmb250LXNpemU6IDI4cHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQp9DQoNCi53ZWxjb21lLWRlc2Mgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIGNvbG9yOiAjN2Y4YzhkOw0KICBtYXJnaW4tYm90dG9tOiAzMnB4Ow0KICBmb250LXNpemU6IDE2cHg7DQogIGxpbmUtaGVpZ2h0OiAxLjY7DQp9DQoNCi8qIOa1geeoi+atpemqpCAqLw0KLnByb2Nlc3Mtc3RlcHMgew0KICBtYXJnaW4tYm90dG9tOiAzMnB4Ow0KfQ0KDQouc3RlcC1pdGVtIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgZ2FwOiAxNnB4Ow0KICBtYXJnaW4tYm90dG9tOiAyNHB4Ow0KICBwYWRkaW5nOiAxNnB4Ow0KICBib3JkZXItcmFkaXVzOiAxMnB4Ow0KICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOw0KfQ0KDQouc3RlcC1pdGVtLmFjdGl2ZSB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMzNDk4ZGIsICMyOTgwYjkpOw0KICBjb2xvcjogd2hpdGU7DQogIHRyYW5zZm9ybTogdHJhbnNsYXRlWCg4cHgpOw0KfQ0KDQouc3RlcC1pdGVtOm5vdCguYWN0aXZlKSB7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogIGNvbG9yOiAjNmM3NTdkOw0KfQ0KDQouc3RlcC1jaXJjbGUgew0KICB3aWR0aDogMzJweDsNCiAgaGVpZ2h0OiAzMnB4Ow0KICBib3JkZXItcmFkaXVzOiA1MCU7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICBmb250LXdlaWdodDogYm9sZDsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBmbGV4LXNocmluazogMDsNCn0NCg0KLnN0ZXAtaXRlbS5hY3RpdmUgLnN0ZXAtY2lyY2xlIHsNCiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpOw0KICBjb2xvcjogd2hpdGU7DQp9DQoNCi5zdGVwLWl0ZW06bm90KC5hY3RpdmUpIC5zdGVwLWNpcmNsZSB7DQogIGJhY2tncm91bmQ6ICNkZWUyZTY7DQogIGNvbG9yOiAjNmM3NTdkOw0KfQ0KDQouc3RlcC10ZXh0IGg0IHsNCiAgbWFyZ2luOiAwIDAgNHB4IDA7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCn0NCg0KLnN0ZXAtdGV4dCBwIHsNCiAgbWFyZ2luOiAwOw0KICBmb250LXNpemU6IDE0cHg7DQogIG9wYWNpdHk6IDAuODsNCn0NCg0KLyog5a6J5YWo5o+Q56S6ICovDQouc2VjdXJpdHktdGlwcyB7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogIGJvcmRlci1yYWRpdXM6IDEycHg7DQogIHBhZGRpbmc6IDIwcHg7DQogIGJvcmRlci1sZWZ0OiA0cHggc29saWQgIzM0OThkYjsNCn0NCg0KLnNlY3VyaXR5LXRpcHMgaDQgew0KICBtYXJnaW46IDAgMCAxMnB4IDA7DQogIGNvbG9yOiAjMmMzZTUwOw0KICBmb250LXNpemU6IDE2cHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGdhcDogOHB4Ow0KfQ0KDQouc2VjdXJpdHktdGlwcyB1bCB7DQogIG1hcmdpbjogMDsNCiAgcGFkZGluZy1sZWZ0OiAyMHB4Ow0KICBjb2xvcjogIzZjNzU3ZDsNCn0NCg0KLnNlY3VyaXR5LXRpcHMgbGkgew0KICBtYXJnaW4tYm90dG9tOiA4cHg7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgbGluZS1oZWlnaHQ6IDEuNDsNCn0NCg0KLyog5Y+z5L6n6KGo5Y2V6Z2i5p2/ICovDQouZm9ybS1wYW5lbCB7DQogIGZsZXg6IDE7DQogIGJhY2tncm91bmQ6ICNmZmY7DQogIGJvcmRlci1yYWRpdXM6IDE2cHg7DQogIGJveC1zaGFkb3c6IDAgOHB4IDMycHggcmdiYSgwLCAwLCAwLCAwLjEpOw0KICBvdmVyZmxvdzogaGlkZGVuOw0KfQ0KDQovKiDov5vluqbmjIfnpLrlmaggKi8NCi5wcm9ncmVzcy1pbmRpY2F0b3Igew0KICBiYWNrZ3JvdW5kOiAjZjhmOWZhOw0KICBwYWRkaW5nOiAyNHB4IDMycHg7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTllY2VmOw0KfQ0KDQoucHJvZ3Jlc3MtYmFyIHsNCiAgaGVpZ2h0OiA2cHg7DQogIGJhY2tncm91bmQ6ICNlOWVjZWY7DQogIGJvcmRlci1yYWRpdXM6IDNweDsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgbWFyZ2luLWJvdHRvbTogOHB4Ow0KfQ0KDQoucHJvZ3Jlc3MtZmlsbCB7DQogIGhlaWdodDogMTAwJTsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDkwZGVnLCAjMzQ5OGRiLCAjMjk4MGI5KTsNCiAgYm9yZGVyLXJhZGl1czogM3B4Ow0KICB0cmFuc2l0aW9uOiB3aWR0aCAwLjNzIGVhc2U7DQp9DQoNCi5wcm9ncmVzcy10ZXh0IHsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBjb2xvcjogIzZjNzU3ZDsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBmb250LXdlaWdodDogNTAwOw0KfQ0KDQovKiDooajljZXlhoXlrrkgKi8NCi5mb3JtLWNvbnRlbnQgew0KICBwYWRkaW5nOiAzMnB4Ow0KICBtYXgtaGVpZ2h0OiBjYWxjKDEwMHZoIC0gMzAwcHgpOw0KICBvdmVyZmxvdy15OiBhdXRvOw0KfQ0KDQouZm9ybS1oZWFkZXIgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIG1hcmdpbi1ib3R0b206IDMycHg7DQp9DQoNCi5mb3JtLWhlYWRlciBoMyB7DQogIGNvbG9yOiAjMmMzZTUwOw0KICBmb250LXNpemU6IDI0cHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIG1hcmdpbi1ib3R0b206IDhweDsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIGdhcDogMTJweDsNCn0NCg0KLmZvcm0taGVhZGVyIHAgew0KICBjb2xvcjogIzdmOGM4ZDsNCiAgbWFyZ2luOiAwOw0KICBmb250LXNpemU6IDE2cHg7DQp9DQoNCi8qIOmqjOivgeaWueW8j+e9keagvCAqLw0KLnZlcmlmeS1ncmlkIHsNCiAgZGlzcGxheTogZ3JpZDsNCiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoMiwgMWZyKTsNCiAgZ2FwOiAyMHB4Ow0KICBtYXJnaW4tYm90dG9tOiAzMnB4Ow0KfQ0KDQoudmVyaWZ5LWNhcmQgew0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogIGJvcmRlcjogMnB4IHNvbGlkICNlOWVjZWY7DQogIGJvcmRlci1yYWRpdXM6IDEycHg7DQogIHBhZGRpbmc6IDI0cHg7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgY3Vyc29yOiBwb2ludGVyOw0KICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOw0KfQ0KDQoudmVyaWZ5LWNhcmQ6aG92ZXIgew0KICBib3JkZXItY29sb3I6ICMzNDk4ZGI7DQogIGJhY2tncm91bmQ6ICNmZmY7DQogIGJveC1zaGFkb3c6IDAgNHB4IDE2cHggcmdiYSg1MiwgMTUyLCAyMTksIDAuMTUpOw0KICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7DQp9DQoNCi52ZXJpZnktY2FyZC5zZWxlY3RlZCB7DQogIGJvcmRlci1jb2xvcjogIzM0OThkYjsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzM0OThkYiwgIzI5ODBiOSk7DQogIGNvbG9yOiB3aGl0ZTsNCiAgYm94LXNoYWRvdzogMCA4cHggMjRweCByZ2JhKDUyLCAxNTIsIDIxOSwgMC4zKTsNCn0NCg0KLnZlcmlmeS1pY29uIHsNCiAgbWFyZ2luLWJvdHRvbTogMTZweDsNCn0NCg0KLnZlcmlmeS1pY29uIGkgew0KICBmb250LXNpemU6IDQ4cHg7DQogIGNvbG9yOiAjMzQ5OGRiOw0KfQ0KDQoudmVyaWZ5LWNhcmQuc2VsZWN0ZWQgLnZlcmlmeS1pY29uIGkgew0KICBjb2xvcjogd2hpdGU7DQp9DQoNCi52ZXJpZnktY2FyZCBoNCB7DQogIG1hcmdpbjogMCAwIDhweCAwOw0KICBmb250LXNpemU6IDE4cHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQp9DQoNCi52ZXJpZnktY2FyZCBwIHsNCiAgbWFyZ2luOiAwOw0KICBmb250LXNpemU6IDE0cHg7DQogIG9wYWNpdHk6IDAuODsNCn0NCg0KLnZlcmlmeS1zdGF0dXMgew0KICBwb3NpdGlvbjogYWJzb2x1dGU7DQogIHRvcDogMTJweDsNCiAgcmlnaHQ6IDEycHg7DQogIHdpZHRoOiAyNHB4Ow0KICBoZWlnaHQ6IDI0cHg7DQogIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKTsNCiAgYm9yZGVyLXJhZGl1czogNTAlOw0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCn0NCg0KLyog6aqM6K+B5pON5L2c5Yy65Z+fICovDQoudmVyaWZ5LW9wZXJhdGlvbiB7DQogIG1hcmdpbi10b3A6IDMycHg7DQogIHBhZGRpbmc6IDI0cHg7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogIGJvcmRlci1yYWRpdXM6IDEycHg7DQp9DQoNCi5vcGVyYXRpb24taGVhZGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBtYXJnaW4tYm90dG9tOiAyNHB4Ow0KfQ0KDQoub3BlcmF0aW9uLWhlYWRlciBoNCB7DQogIG1hcmdpbjogMDsNCiAgY29sb3I6ICMyYzNlNTA7DQogIGZvbnQtc2l6ZTogMThweDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCn0NCg0KLyog6Lqr5Lu96KGo5Y2VICovDQouaWRlbnRpdHktZm9ybSB7DQogIGJhY2tncm91bmQ6IHdoaXRlOw0KICBwYWRkaW5nOiAyNHB4Ow0KICBib3JkZXItcmFkaXVzOiAxMnB4Ow0KfQ0KDQovKiDouqvku73or4Hor7vljaHlmaggKi8NCi5pZC1jYXJkLXJlYWRlciB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCn0NCg0KLnJlYWRlci12aXN1YWwgew0KICBiYWNrZ3JvdW5kOiB3aGl0ZTsNCiAgcGFkZGluZzogNDBweDsNCiAgYm9yZGVyLXJhZGl1czogMTJweDsNCn0NCg0KLnJlYWRlci1hbmltYXRpb24gew0KICBtYXJnaW4tYm90dG9tOiAyNHB4Ow0KfQ0KDQoucm90YXRpbmcgew0KICBhbmltYXRpb246IHJvdGF0ZSAycyBsaW5lYXIgaW5maW5pdGU7DQp9DQoNCkBrZXlmcmFtZXMgcm90YXRlIHsNCiAgZnJvbSB7IHRyYW5zZm9ybTogcm90YXRlKDBkZWcpOyB9DQogIHRvIHsgdHJhbnNmb3JtOiByb3RhdGUoMzYwZGVnKTsgfQ0KfQ0KDQoucmVhZGVyLWFuaW1hdGlvbiBpIHsNCiAgZm9udC1zaXplOiA2NHB4Ow0KICBjb2xvcjogIzM0OThkYjsNCn0NCg0KLnJlYWRlci12aXN1YWwgaDQgew0KICBjb2xvcjogIzJjM2U1MDsNCiAgbWFyZ2luLWJvdHRvbTogOHB4Ow0KfQ0KDQoucmVhZGVyLXZpc3VhbCBwIHsNCiAgY29sb3I6ICM3ZjhjOGQ7DQogIG1hcmdpbi1ib3R0b206IDI0cHg7DQp9DQoNCi5yZWFkZXItdGlwcyB7DQogIG1hcmdpbjogMjRweCAwOw0KfQ0KDQovKiDkurrohLjor4bliKsgKi8NCi5mYWNlLXJlY29nbml0aW9uIHsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KfQ0KDQouY2FtZXJhLWNvbnRhaW5lciB7DQogIGJhY2tncm91bmQ6IHdoaXRlOw0KICBwYWRkaW5nOiA0MHB4Ow0KICBib3JkZXItcmFkaXVzOiAxMnB4Ow0KfQ0KDQouY2FtZXJhLWZyYW1lIHsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KICB3aWR0aDogMjgwcHg7DQogIGhlaWdodDogMjEwcHg7DQogIG1hcmdpbjogMCBhdXRvIDI0cHg7DQogIGJvcmRlcjogM3B4IHNvbGlkICMzNDk4ZGI7DQogIGJvcmRlci1yYWRpdXM6IDEycHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICBiYWNrZ3JvdW5kOiAjZjhmOWZhOw0KICBvdmVyZmxvdzogaGlkZGVuOw0KfQ0KDQouY2FtZXJhLW92ZXJsYXkgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBnYXA6IDE2cHg7DQp9DQoNCi5mYWNlLW91dGxpbmUgew0KICB3aWR0aDogMTIwcHg7DQogIGhlaWdodDogMTUwcHg7DQogIGJvcmRlcjogMnB4IGRhc2hlZCAjMzQ5OGRiOw0KICBib3JkZXItcmFkaXVzOiA2MHB4Ow0KICBvcGFjaXR5OiAwLjY7DQp9DQoNCi5jYW1lcmEtaWNvbiB7DQogIGZvbnQtc2l6ZTogMzJweDsNCiAgY29sb3I6ICMzNDk4ZGI7DQp9DQoNCi5zY2FubmluZy1saW5lIHsNCiAgcG9zaXRpb246IGFic29sdXRlOw0KICB0b3A6IDA7DQogIGxlZnQ6IDA7DQogIHJpZ2h0OiAwOw0KICBoZWlnaHQ6IDJweDsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDkwZGVnLCB0cmFuc3BhcmVudCwgIzM0OThkYiwgdHJhbnNwYXJlbnQpOw0KICBhbmltYXRpb246IHNjYW4gMnMgZWFzZS1pbi1vdXQgaW5maW5pdGU7DQp9DQoNCkBrZXlmcmFtZXMgc2NhbiB7DQogIDAlIHsgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApOyB9DQogIDUwJSB7IHRyYW5zZm9ybTogdHJhbnNsYXRlWSgyMDZweCk7IH0NCiAgMTAwJSB7IHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKTsgfQ0KfQ0KDQovKiDooajljZXliIbnu4QgKi8NCi5mb3JtLXNlY3Rpb24gew0KICBtYXJnaW4tYm90dG9tOiAzMnB4Ow0KfQ0KDQouc2VjdGlvbi10aXRsZSB7DQogIGNvbG9yOiAjMmMzZTUwOw0KICBmb250LXNpemU6IDE4cHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIG1hcmdpbi1ib3R0b206IDE2cHg7DQogIHBhZGRpbmctYm90dG9tOiA4cHg7DQogIGJvcmRlci1ib3R0b206IDJweCBzb2xpZCAjZTllY2VmOw0KfQ0KDQouc2VjdGlvbi1oZWFkZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIG1hcmdpbi1ib3R0b206IDE2cHg7DQp9DQoNCi8qIOWQjOihjOS6uuWRmCAqLw0KLm5vLWNvbXBhbmlvbnMgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIHBhZGRpbmc6IDQwcHg7DQogIGNvbG9yOiAjN2Y4YzhkOw0KfQ0KDQoubm8tY29tcGFuaW9ucyBpIHsNCiAgZm9udC1zaXplOiA0OHB4Ow0KICBtYXJnaW4tYm90dG9tOiAxNnB4Ow0KICBjb2xvcjogI2JkYzNjNzsNCn0NCg0KLmNvbXBhbmlvbnMtdGFibGUgew0KICBiYWNrZ3JvdW5kOiAjZjhmOWZhOw0KICBwYWRkaW5nOiAxNnB4Ow0KICBib3JkZXItcmFkaXVzOiA4cHg7DQp9DQoNCi8qIOaIkOWKn+mhtemdoiAqLw0KLnN1Y2Nlc3MtY29udGVudCB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCn0NCg0KLnN1Y2Nlc3MtaWNvbiB7DQogIG1hcmdpbi1ib3R0b206IDI0cHg7DQp9DQoNCi5zdWNjZXNzLWljb24gaSB7DQogIGZvbnQtc2l6ZTogODBweDsNCiAgY29sb3I6ICMyN2FlNjA7DQp9DQoNCi5zdWNjZXNzLWNvbnRlbnQgaDQgew0KICBjb2xvcjogIzJjM2U1MDsNCiAgZm9udC1zaXplOiAyOHB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KICBtYXJnaW4tYm90dG9tOiAxMnB4Ow0KfQ0KDQouc3VjY2Vzcy1tZXNzYWdlIHsNCiAgY29sb3I6ICM3ZjhjOGQ7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgbWFyZ2luLWJvdHRvbTogMzJweDsNCn0NCg0KLnJlZ2lzdGVyLXN1bW1hcnkgew0KICBiYWNrZ3JvdW5kOiAjZjhmOWZhOw0KICBwYWRkaW5nOiAyNHB4Ow0KICBib3JkZXItcmFkaXVzOiAxMnB4Ow0KICBtYXJnaW4tYm90dG9tOiAzMnB4Ow0KICB0ZXh0LWFsaWduOiBsZWZ0Ow0KfQ0KDQoucmVnaXN0ZXItc3VtbWFyeSBoNSB7DQogIGNvbG9yOiAjMmMzZTUwOw0KICBmb250LXNpemU6IDE4cHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIG1hcmdpbi1ib3R0b206IDE2cHg7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCn0NCg0KLmFjY2Vzcy1jcmVkZW50aWFsIHsNCiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsNCiAgcGFkZGluZzogMjRweDsNCiAgYm9yZGVyLXJhZGl1czogMTJweDsNCiAgdGV4dC1hbGlnbjogbGVmdDsNCn0NCg0KLmFjY2Vzcy1jcmVkZW50aWFsIGg1IHsNCiAgY29sb3I6ICMyYzNlNTA7DQogIGZvbnQtc2l6ZTogMThweDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgbWFyZ2luLWJvdHRvbTogMTZweDsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KfQ0KDQouY3JlZGVudGlhbC1jYXJkIHsNCiAgZGlzcGxheTogZmxleDsNCiAgZ2FwOiAyNHB4Ow0KICBiYWNrZ3JvdW5kOiB3aGl0ZTsNCiAgcGFkZGluZzogMjRweDsNCiAgYm9yZGVyLXJhZGl1czogMTJweDsNCiAgYm9yZGVyOiAycHggc29saWQgI2U5ZWNlZjsNCn0NCg0KLnFyLWNvZGUtY29udGFpbmVyIHsNCiAgZmxleDogMCAwIDEyMHB4Ow0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQp9DQoNCi5xci1jb2RlIHsNCiAgd2lkdGg6IDEyMHB4Ow0KICBoZWlnaHQ6IDEyMHB4Ow0KICBib3JkZXI6IDJweCBzb2xpZCAjZTllY2VmOw0KICBib3JkZXItcmFkaXVzOiA4cHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICBiYWNrZ3JvdW5kOiAjZjhmOWZhOw0KICBtYXJnaW4tYm90dG9tOiA4cHg7DQp9DQoNCi5xci1jb2RlIGkgew0KICBmb250LXNpemU6IDQ4cHg7DQogIGNvbG9yOiAjN2Y4YzhkOw0KfQ0KDQoucXItY29kZS1pZCB7DQogIGZvbnQtc2l6ZTogMTJweDsNCiAgY29sb3I6ICM2Yzc1N2Q7DQogIGZvbnQtZmFtaWx5OiBtb25vc3BhY2U7DQogIG1hcmdpbjogMDsNCn0NCg0KLmNyZWRlbnRpYWwtaW5mbyB7DQogIGZsZXg6IDE7DQp9DQoNCi5jcmVkZW50aWFsLWluZm8gaDYgew0KICBjb2xvcjogIzJjM2U1MDsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KICBtYXJnaW4tYm90dG9tOiAxMnB4Ow0KfQ0KDQouY3JlZGVudGlhbC1pbmZvIHVsIHsNCiAgbWFyZ2luOiAwOw0KICBwYWRkaW5nLWxlZnQ6IDIwcHg7DQogIGNvbG9yOiAjNmM3NTdkOw0KfQ0KDQouY3JlZGVudGlhbC1pbmZvIGxpIHsNCiAgbWFyZ2luLWJvdHRvbTogOHB4Ow0KICBmb250LXNpemU6IDE0cHg7DQogIGxpbmUtaGVpZ2h0OiAxLjQ7DQp9DQoNCi8qIOW6lemDqOaTjeS9nOaMiemSriAqLw0KLmZvcm0tYWN0aW9ucyB7DQogIHBhZGRpbmc6IDI0cHggMzJweDsNCiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsNCiAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNlOWVjZWY7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCn0NCg0KLmZpbmFsLWFjdGlvbnMgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBnYXA6IDE2cHg7DQp9DQoNCi8qIOWTjeW6lOW8j+iuvuiuoSAqLw0KQG1lZGlhIChtYXgtd2lkdGg6IDEyMDBweCkgew0KICAubWFpbi1jb250ZW50IHsNCiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgIGdhcDogMjRweDsNCiAgfQ0KICANCiAgLmluZm8tcGFuZWwgew0KICAgIGZsZXg6IG5vbmU7DQogIH0NCiAgDQogIC53ZWxjb21lLWNhcmQgew0KICAgIHBvc2l0aW9uOiBzdGF0aWM7DQogIH0NCiAgDQogIC52ZXJpZnktZ3JpZCB7DQogICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAxZnI7DQogIH0NCn0NCg0KQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7DQogIC5tYWluLWNvbnRlbnQgew0KICAgIHBhZGRpbmc6IDIwcHg7DQogIH0NCiAgDQogIC5uYXYtY29udGVudCB7DQogICAgcGFkZGluZzogMCAyMHB4Ow0KICB9DQogIA0KICAuY29tcGFueS1uYW1lIHsNCiAgICBmb250LXNpemU6IDE2cHg7DQogIH0NCiAgDQogIC5mb3JtLWNvbnRlbnQgew0KICAgIHBhZGRpbmc6IDIwcHg7DQogIH0NCiAgDQogIC5jcmVkZW50aWFsLWNhcmQgew0KICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgdGV4dC1hbGlnbjogY2VudGVyOw0KICB9DQogIA0KICAuZm9ybS1hY3Rpb25zIHsNCiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgIGdhcDogMTZweDsNCiAgfQ0KfQ0KDQovKiBFbGVtZW50IFVJIOagt+W8j+imhuebliAqLw0KLmVsLWZvcm0taXRlbSB7DQogIG1hcmdpbi1ib3R0b206IDI0cHg7DQp9DQoNCi5lbC1pbnB1dF9faW5uZXIgew0KICBoZWlnaHQ6IDQ0cHg7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgYm9yZGVyOiAxcHggc29saWQgI2U5ZWNlZjsNCn0NCg0KLmVsLWlucHV0X19pbm5lcjpmb2N1cyB7DQogIGJvcmRlci1jb2xvcjogIzM0OThkYjsNCiAgYm94LXNoYWRvdzogMCAwIDAgMnB4IHJnYmEoNTIsIDE1MiwgMjE5LCAwLjEpOw0KfQ0KDQouZWwtc2VsZWN0IHsNCiAgd2lkdGg6IDEwMCU7DQp9DQoNCi5lbC1kYXRlLWVkaXRvciB7DQogIHdpZHRoOiAxMDAlOw0KfQ0KDQouZWwtYnV0dG9uLS1sYXJnZSB7DQogIGhlaWdodDogNDRweDsNCiAgcGFkZGluZzogMTJweCAyNHB4Ow0KICBib3JkZXItcmFkaXVzOiA4cHg7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgZm9udC13ZWlnaHQ6IDUwMDsNCn0NCg0KLmVsLWJ1dHRvbi0tcHJpbWFyeSB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMzNDk4ZGIsICMyOTgwYjkpOw0KICBib3JkZXI6IG5vbmU7DQp9DQoNCi5lbC1idXR0b24tLXByaW1hcnk6aG92ZXIgew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMjk4MGI5LCAjMWY0ZTc5KTsNCn0NCg0KLmVsLWRlc2NyaXB0aW9ucyB7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCn0NCg0KLmVsLXRhYmxlIHsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KfQ0KDQouZWwtYWxlcnQgew0KICBib3JkZXItcmFkaXVzOiA4cHg7DQp9DQo="}, {"version": 3, "sources": ["self-register.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwuBA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "self-register.vue", "sourceRoot": "src/views/asc/visitor", "sourcesContent": ["<template>\r\n  <div class=\"pc-register-container\">\r\n    <!-- 顶部导航栏 -->\r\n    <div class=\"top-nav\">\r\n      <div class=\"nav-content\">\r\n        <div class=\"logo-section\">\r\n          <img src=\"@/assets/logo/logo.png\" alt=\"公司logo\" class=\"nav-logo\" />\r\n          <span class=\"company-name\">智慧园区访客登记系统</span>\r\n        </div>\r\n        <div class=\"nav-actions\">\r\n          <el-button type=\"text\" @click=\"showHelp\">使用帮助</el-button>\r\n          <el-button type=\"text\" @click=\"contactService\">联系客服</el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 主要内容区域 -->\r\n    <div class=\"main-content\">\r\n      <!-- 左侧信息面板 -->\r\n      <div class=\"info-panel\">\r\n        <div class=\"welcome-card\">\r\n          <div class=\"welcome-icon\">\r\n            <i class=\"el-icon-office-building\"></i>\r\n          </div>\r\n          <h2>欢迎访问智慧园区</h2>\r\n          <p class=\"welcome-desc\">为了您的安全和便利，请填写以下信息完成访客登记</p>\r\n\r\n          <div class=\"process-steps\">\r\n            <div class=\"step-item\" :class=\"{ active: !registrationCompleted }\">\r\n              <div class=\"step-circle\">1</div>\r\n              <div class=\"step-text\">\r\n                <h4>填写信息</h4>\r\n                <p>完善访问详细信息</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"step-item\" :class=\"{ active: registrationCompleted }\">\r\n              <div class=\"step-circle\">2</div>\r\n              <div class=\"step-text\">\r\n                <h4>获取凭证</h4>\r\n                <p>生成访问凭证二维码</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"security-tips\">\r\n            <h4><i class=\"el-icon-lock\"></i> 安全提示</h4>\r\n            <ul>\r\n              <li>请确保提供真实有效的身份信息</li>\r\n              <li>您的个人信息将被严格保密</li>\r\n              <li>访问凭证仅限当次使用</li>\r\n            </ul>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 右侧表单区域 -->\r\n      <div class=\"form-panel\">\r\n        <!-- 表单标题 -->\r\n        <div class=\"form-header\">\r\n          <h3 v-if=\"!registrationCompleted\"><i class=\"el-icon-edit\"></i> 访客登记信息</h3>\r\n          <h3 v-else><i class=\"el-icon-circle-check\"></i> 登记完成</h3>\r\n          <p v-if=\"!registrationCompleted\">请填写详细的访问信息</p>\r\n          <p v-else>您的访客登记已成功提交</p>\r\n        </div>\r\n\r\n        <!-- 表单内容 -->\r\n        <div class=\"form-content\" v-if=\"!registrationCompleted\">\r\n          <el-form ref=\"visitForm\" :model=\"formData\" :rules=\"visitRules\"\r\n                   label-width=\"120px\" class=\"visit-form\">\r\n\r\n            <!-- 基本访问信息 -->\r\n            <div class=\"form-section\">\r\n              <h4 class=\"section-title\">基本访问信息</h4>\r\n              <el-row :gutter=\"20\">\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"来访事由\" prop=\"visitInfo.reasonForVisit\">\r\n                    <el-input v-model=\"formData.visitInfo.reasonForVisit\" placeholder=\"请输入来访事由\"\r\n                              prefix-icon=\"el-icon-tickets\" type=\"textarea\" :rows=\"3\" maxlength=\"200\" show-word-limit />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"被访人姓名\" prop=\"visitInfo.hostEmployeeName\">\r\n                    <el-input v-model=\"formData.visitInfo.hostEmployeeName\" placeholder=\"请输入被访人姓名\"\r\n                              prefix-icon=\"el-icon-user\" maxlength=\"20\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n\r\n              <el-row :gutter=\"20\">\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"被访部门\" prop=\"visitInfo.departmentVisited\">\r\n                    <el-input v-model=\"formData.visitInfo.departmentVisited\" placeholder=\"请输入被访部门\"\r\n                              prefix-icon=\"el-icon-office-building\" maxlength=\"50\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"车牌号\">\r\n                    <el-input v-model=\"formData.visitInfo.vehiclePlateNumber\" placeholder=\"如有车辆请填写车牌号，多个用逗号分隔\"\r\n                              prefix-icon=\"el-icon-truck\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </div>\r\n\r\n            <!-- 时间信息 -->\r\n            <div class=\"form-section\">\r\n              <h4 class=\"section-title\">访问时间</h4>\r\n              <el-row :gutter=\"20\">\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"预计到访时间\" prop=\"visitInfo.plannedEntryDatetime\">\r\n                    <el-date-picker\r\n                      v-model=\"formData.visitInfo.plannedEntryDatetime\"\r\n                      type=\"datetime\"\r\n                      placeholder=\"选择到访时间\"\r\n                      style=\"width: 100%\"\r\n                      value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                      :picker-options=\"entryPickerOptions\"\r\n                      @change=\"onArrivalTimeChange\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"预计离开时间\" prop=\"visitInfo.plannedExitDatetime\">\r\n                    <el-date-picker\r\n                      v-model=\"formData.visitInfo.plannedExitDatetime\"\r\n                      type=\"datetime\"\r\n                      placeholder=\"选择离开时间\"\r\n                      style=\"width: 100%\"\r\n                      value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                      :picker-options=\"exitPickerOptions\"\r\n                      @change=\"onDepartureTimeChange\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </div>\r\n\r\n            <!-- 访客信息 -->\r\n            <div class=\"form-section\">\r\n              <div class=\"section-header\">\r\n                <h4 class=\"section-title\">访客信息（第一位为主联系人）共 {{ totalVisitors }} 人</h4>\r\n                <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"addVisitor\" :disabled=\"formData.visitors.length >= 10\">\r\n                  添加访客\r\n                </el-button>\r\n              </div>\r\n\r\n              <div class=\"visitors-list\">\r\n                <div v-for=\"(visitor, index) in formData.visitors\" :key=\"index\" class=\"visitor-item\">\r\n                  <div class=\"visitor-header\">\r\n                    <h5 class=\"visitor-title\">\r\n                      {{ index === 0 ? '主联系人' : `访客 ${index + 1}` }}\r\n                    </h5>\r\n                    <el-button v-if=\"index > 0\" size=\"mini\" type=\"danger\" icon=\"el-icon-delete\"\r\n                               @click=\"removeVisitor(index)\" circle />\r\n                  </div>\r\n\r\n                  <el-row :gutter=\"20\">\r\n                    <el-col :span=\"12\">\r\n                      <el-form-item :label=\"`姓名`\" :prop=\"`visitors.${index}.name`\">\r\n                        <el-input v-model=\"visitor.name\" placeholder=\"请输入姓名\" maxlength=\"20\" />\r\n                      </el-form-item>\r\n                    </el-col>\r\n                    <el-col :span=\"12\">\r\n                      <el-form-item :label=\"`手机号`\" :prop=\"`visitors.${index}.phone`\">\r\n                        <el-input v-model=\"visitor.phone\" placeholder=\"请输入手机号\" />\r\n                      </el-form-item>\r\n                    </el-col>\r\n                  </el-row>\r\n\r\n                  <el-row :gutter=\"20\">\r\n                    <el-col :span=\"12\">\r\n                      <el-form-item :label=\"`身份证号`\" :prop=\"`visitors.${index}.idCard`\">\r\n                        <el-input v-model=\"visitor.idCard\" placeholder=\"请输入身份证号\" />\r\n                      </el-form-item>\r\n                    </el-col>\r\n                    <el-col :span=\"12\">\r\n                      <el-form-item :label=\"`公司名称`\">\r\n                        <el-input v-model=\"visitor.company\" placeholder=\"请输入公司名称（可选）\" maxlength=\"100\" />\r\n                      </el-form-item>\r\n                    </el-col>\r\n                  </el-row>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-form>\r\n        </div>\r\n\r\n        <!-- 登记成功页面 -->\r\n        <div class=\"success-content\" v-if=\"registrationCompleted\">\r\n          <div class=\"success-icon\">\r\n            <i class=\"el-icon-circle-check\"></i>\r\n          </div>\r\n\r\n          <h4>登记成功！</h4>\r\n          <p class=\"success-message\">您的访客登记已提交，请等待审核通过</p>\r\n\r\n          <!-- 登记信息摘要 -->\r\n          <div class=\"register-summary\">\r\n            <h5>登记信息摘要</h5>\r\n            <el-descriptions :column=\"2\" border>\r\n              <el-descriptions-item label=\"主联系人\">\r\n                {{ mainContact.name }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"联系电话\">\r\n                {{ mainContact.phone }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"被访人\">\r\n                {{ formData.visitInfo.hostEmployeeName }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"被访部门\">\r\n                {{ formData.visitInfo.departmentVisited }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"来访事由\" span=\"2\">\r\n                {{ formData.visitInfo.reasonForVisit }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"预计到访时间\">\r\n                {{ parseTime(formData.visitInfo.plannedEntryDatetime) }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"预计离开时间\">\r\n                {{ parseTime(formData.visitInfo.plannedExitDatetime) }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"访客总数\">\r\n                {{ totalVisitors }} 人\r\n              </el-descriptions-item>\r\n            </el-descriptions>\r\n          </div>\r\n\r\n          <!-- 访问凭证 -->\r\n          <div class=\"access-credential\" v-if=\"registrationId\">\r\n            <h5>访问凭证</h5>\r\n            <div class=\"credential-card\">\r\n              <div class=\"qr-code-container\">\r\n                <div class=\"qr-code\">\r\n                  <i class=\"el-icon-qrcode\"></i>\r\n                </div>\r\n                <p class=\"qr-code-id\">{{ registrationId }}</p>\r\n              </div>\r\n              <div class=\"credential-info\">\r\n                <h6>使用说明</h6>\r\n                <ul>\r\n                  <li>请保存此二维码截图</li>\r\n                  <li>审核通过后可用于园区门禁</li>\r\n                  <li>凭证仅限当次访问使用</li>\r\n                  <li>如有疑问请联系园区前台</li>\r\n                </ul>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 底部操作按钮 -->\r\n        <div class=\"form-actions\">\r\n          <el-button v-if=\"!registrationCompleted\"\r\n                     type=\"primary\" @click=\"submitForm\" size=\"large\" :loading=\"submitting\">\r\n            {{ submitting ? '登记中...' : `确认登记 (${totalVisitors}人)` }}\r\n          </el-button>\r\n\r\n          <el-button v-if=\"!registrationCompleted\" @click=\"resetForm\" size=\"large\">\r\n            重置表单\r\n          </el-button>\r\n\r\n          <div v-if=\"registrationCompleted\" class=\"final-actions\">\r\n            <el-button type=\"primary\" @click=\"printCredential\" size=\"large\">\r\n              <i class=\"el-icon-printer\"></i> 打印凭证\r\n            </el-button>\r\n            <el-button @click=\"resetForm\" size=\"large\">\r\n              <i class=\"el-icon-refresh\"></i> 重新登记\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { submitVisitorRegistration } from \"@/api/asc/visitor\";\r\n\r\nexport default {\r\n  name: \"SelfRegister\",\r\n  data() {\r\n    return {\r\n      submitting: false,\r\n      registrationCompleted: false,\r\n\r\n      // 表单数据 - 与微信端保持一致的结构\r\n      formData: {\r\n        // 访客列表（第一位为主联系人）\r\n        visitors: [\r\n          {\r\n            name: '',\r\n            phone: '',\r\n            idCard: '',\r\n            company: '',\r\n            isMainContact: true\r\n          }\r\n        ],\r\n        // 来访信息\r\n        visitInfo: {\r\n          reasonForVisit: '',\r\n          hostEmployeeName: '',\r\n          departmentVisited: '',\r\n          vehiclePlateNumber: '',\r\n          plannedEntryDatetime: '',\r\n          plannedExitDatetime: ''\r\n        }\r\n      },\r\n\r\n      // 表单验证规则 - 与微信端保持一致\r\n      visitRules: {\r\n        'visitInfo.reasonForVisit': [\r\n          { required: true, message: '请输入来访事由', trigger: 'blur' },\r\n          { min: 2, max: 200, message: '来访事由长度应在2-200个字符之间', trigger: 'blur' }\r\n        ],\r\n        'visitInfo.hostEmployeeName': [\r\n          { required: true, message: '请输入被访人姓名', trigger: 'blur' },\r\n          { min: 2, max: 20, message: '被访人姓名长度应在2-20个字符之间', trigger: 'blur' }\r\n        ],\r\n        'visitInfo.departmentVisited': [\r\n          { required: true, message: '请输入被访部门', trigger: 'blur' },\r\n          { min: 2, max: 50, message: '部门名称长度应在2-50个字符之间', trigger: 'blur' }\r\n        ],\r\n        'visitInfo.plannedEntryDatetime': [\r\n          { required: true, message: '请选择预计来访时间', trigger: 'change' }\r\n        ],\r\n        'visitInfo.plannedExitDatetime': [\r\n          { required: true, message: '请选择预计离开时间', trigger: 'change' }\r\n        ],\r\n        // 动态访客验证规则\r\n        'visitors.0.name': [\r\n          { required: true, message: '请输入主联系人姓名', trigger: 'blur' },\r\n          { min: 2, max: 20, message: '姓名长度应在2-20个字符之间', trigger: 'blur' }\r\n        ],\r\n        'visitors.0.phone': [\r\n          { required: true, message: '请输入主联系人手机号', trigger: 'blur' },\r\n          { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }\r\n        ],\r\n        'visitors.0.idCard': [\r\n          { required: true, message: '请输入主联系人身份证号', trigger: 'blur' }\r\n        ]\r\n      },\r\n\r\n      // 登记结果\r\n      registrationId: null,\r\n\r\n      // 时间选择器配置\r\n      entryPickerOptions: {\r\n        shortcuts: [{\r\n          text: '现在',\r\n          onClick(picker) {\r\n            picker.$emit('pick', new Date());\r\n          }\r\n        }, {\r\n          text: '1小时后',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setTime(date.getTime() + 3600 * 1000);\r\n            picker.$emit('pick', date);\r\n          }\r\n        }, {\r\n          text: '明天上午9点',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setDate(date.getDate() + 1);\r\n            date.setHours(9, 0, 0, 0);\r\n            picker.$emit('pick', date);\r\n          }\r\n        }],\r\n        disabledDate(time) {\r\n          // 允许选择当前时间前1小时到未来30天\r\n          const oneHourBefore = Date.now() - 3600 * 1000;\r\n          const thirtyDaysLater = Date.now() + 30 * 24 * 3600 * 1000;\r\n          return time.getTime() < oneHourBefore || time.getTime() > thirtyDaysLater;\r\n        }\r\n      },\r\n\r\n      exitPickerOptions: {\r\n        shortcuts: [{\r\n          text: '2小时后',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setTime(date.getTime() + 2 * 3600 * 1000);\r\n            picker.$emit('pick', date);\r\n          }\r\n        }, {\r\n          text: '今天下午6点',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setHours(18, 0, 0, 0);\r\n            if (date.getTime() < Date.now()) {\r\n              date.setDate(date.getDate() + 1);\r\n            }\r\n            picker.$emit('pick', date);\r\n          }\r\n        }, {\r\n          text: '明天下午6点',\r\n          onClick(picker) {\r\n            const date = new Date();\r\n            date.setDate(date.getDate() + 1);\r\n            date.setHours(18, 0, 0, 0);\r\n            picker.$emit('pick', date);\r\n          }\r\n        }],\r\n        disabledDate(time) {\r\n          // 允许选择当前时间到未来30天\r\n          const thirtyDaysLater = Date.now() + 30 * 24 * 3600 * 1000;\r\n          return time.getTime() < Date.now() - 8.64e7 || time.getTime() > thirtyDaysLater;\r\n        }\r\n      }\r\n    };\r\n  },\r\n\r\n  computed: {\r\n    // 总访客人数\r\n    totalVisitors() {\r\n      return this.formData.visitors.length;\r\n    },\r\n\r\n    // 主联系人信息\r\n    mainContact() {\r\n      return this.formData.visitors[0] || {};\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    // 添加访客\r\n    addVisitor() {\r\n      if (this.formData.visitors.length >= 10) {\r\n        this.$message.warning('最多只能添加10名访客');\r\n        return;\r\n      }\r\n\r\n      this.formData.visitors.push({\r\n        name: '',\r\n        phone: '',\r\n        idCard: '',\r\n        company: '',\r\n        isMainContact: false\r\n      });\r\n\r\n      this.$message.success('已添加访客');\r\n    },\r\n\r\n    // 移除访客\r\n    removeVisitor(index) {\r\n      if (index === 0) {\r\n        this.$message.warning('不能删除主联系人');\r\n        return;\r\n      }\r\n\r\n      this.formData.visitors.splice(index, 1);\r\n      this.$message.success('已移除访客');\r\n    },\r\n\r\n    // 格式化日期为后端期望的格式\r\n    formatDateForBackend(dateStr) {\r\n      if (!dateStr) return '';\r\n\r\n      const date = new Date(dateStr);\r\n      if (isNaN(date.getTime())) return '';\r\n\r\n      const year = date.getFullYear();\r\n      const month = String(date.getMonth() + 1).padStart(2, '0');\r\n      const day = String(date.getDate()).padStart(2, '0');\r\n      const hours = String(date.getHours()).padStart(2, '0');\r\n      const minutes = String(date.getMinutes()).padStart(2, '0');\r\n      const seconds = String(date.getSeconds()).padStart(2, '0');\r\n\r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n    },\r\n\r\n    /** 来访时间变更事件 */\r\n    onArrivalTimeChange(value) {\r\n      console.log('预计来访时间变更:', value);\r\n      // 自动设置离开时间为来访时间后4小时\r\n      if (value && !this.formData.visitInfo.plannedExitDatetime) {\r\n        const arrivalTime = new Date(value);\r\n        const departureTime = new Date(arrivalTime.getTime() + 4 * 60 * 60 * 1000);\r\n\r\n        const year = departureTime.getFullYear();\r\n        const month = String(departureTime.getMonth() + 1).padStart(2, '0');\r\n        const day = String(departureTime.getDate()).padStart(2, '0');\r\n        const hours = String(departureTime.getHours()).padStart(2, '0');\r\n        const minutes = String(departureTime.getMinutes()).padStart(2, '0');\r\n        const seconds = String(departureTime.getSeconds()).padStart(2, '0');\r\n\r\n        this.formData.visitInfo.plannedExitDatetime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n      }\r\n    },\r\n\r\n    /** 离开时间变更事件 */\r\n    onDepartureTimeChange(value) {\r\n      console.log('预计离开时间变更:', value);\r\n      // 验证离开时间不能早于来访时间\r\n      if (value && this.formData.visitInfo.plannedEntryDatetime) {\r\n        const arrivalTime = new Date(this.formData.visitInfo.plannedEntryDatetime);\r\n        const departureTime = new Date(value);\r\n\r\n        if (departureTime <= arrivalTime) {\r\n          this.$message.warning('预计离开时间不能早于或等于来访时间');\r\n          this.formData.visitInfo.plannedExitDatetime = '';\r\n        }\r\n      }\r\n    },\r\n\r\n    /** 提交表单 */\r\n    async submitForm() {\r\n      try {\r\n        // 验证表单\r\n        await this.$refs.visitForm.validate();\r\n\r\n        // 验证访客信息\r\n        if (!this.validateVisitors()) {\r\n          return;\r\n        }\r\n\r\n        // 验证时间\r\n        if (!this.validateTimes()) {\r\n          return;\r\n        }\r\n\r\n        this.submitting = true;\r\n\r\n        // 获取主联系人信息\r\n        const mainContact = this.formData.visitors[0];\r\n\r\n        const submitData = {\r\n          // VisitRegistrations 对象\r\n          registration: {\r\n            primaryContactName: mainContact.name.trim(),\r\n            primaryContactPhone: mainContact.phone.trim(),\r\n            reasonForVisit: this.formData.visitInfo.reasonForVisit,\r\n            hostEmployeeName: this.formData.visitInfo.hostEmployeeName,\r\n            hostEmployeeId: null, // 前端暂时没有被访人ID，后端可能会自动处理\r\n            departmentVisited: this.formData.visitInfo.departmentVisited,\r\n            plannedEntryDatetime: this.formatDateForBackend(this.formData.visitInfo.plannedEntryDatetime),\r\n            plannedExitDatetime: this.formatDateForBackend(this.formData.visitInfo.plannedExitDatetime),\r\n            vehiclePlateNumber: this.formData.visitInfo.vehiclePlateNumber || '',\r\n            totalCompanions: this.formData.visitors.length - 1\r\n          },\r\n          // RegistrationAttendees 数组 - 使用后端期望的临时字段\r\n          attendeesList: this.formData.visitors.map((visitor, index) => ({\r\n            visitorName: visitor.name.trim(),\r\n            visitorPhone: visitor.phone.trim(),\r\n            visitorIdCard: visitor.idCard.trim().toUpperCase(),\r\n            visitorCompany: (visitor.company || '').trim(),\r\n            isPrimary: index === 0 ? \"1\" : \"0\", // 第一个访客必须是主联系人\r\n            visitorAvatarPhoto: null // 暂时不支持头像上传\r\n          }))\r\n        };\r\n\r\n        console.log('提交数据结构:', JSON.stringify(submitData, null, 2));\r\n\r\n        // 调用API提交数据\r\n        const response = await submitVisitorRegistration(submitData);\r\n\r\n        if (response.code === 200) {\r\n          this.registrationId = response.data || 'VR' + Date.now();\r\n          this.$message.success(`${this.totalVisitors}名访客登记成功！`);\r\n          this.registrationCompleted = true;\r\n        } else {\r\n          this.$message.error(response.msg || '登记失败，请重试');\r\n        }\r\n      } catch (error) {\r\n        console.error('提交表单失败:', error);\r\n        this.$message.error('登记失败，请检查网络连接');\r\n      } finally {\r\n        this.submitting = false;\r\n      }\r\n    },\r\n\r\n    // 验证访客信息\r\n    validateVisitors() {\r\n      for (let i = 0; i < this.formData.visitors.length; i++) {\r\n        const visitor = this.formData.visitors[i];\r\n        const visitorTitle = i === 0 ? '主联系人' : `访客${i + 1}`;\r\n\r\n        // 验证姓名\r\n        if (!visitor.name || visitor.name.trim() === '') {\r\n          this.$message.error(`请输入${visitorTitle}的姓名`);\r\n          return false;\r\n        }\r\n\r\n        if (visitor.name.trim().length < 2 || visitor.name.trim().length > 20) {\r\n          this.$message.error(`${visitorTitle}的姓名长度应在2-20个字符之间`);\r\n          return false;\r\n        }\r\n\r\n        // 验证手机号\r\n        if (!visitor.phone || visitor.phone.trim() === '') {\r\n          this.$message.error(`请输入${visitorTitle}的手机号`);\r\n          return false;\r\n        }\r\n\r\n        // 验证手机号格式（与后端保持一致）\r\n        const phonePattern = /^1[3-9]\\d{9}$/;\r\n        if (!phonePattern.test(visitor.phone.trim())) {\r\n          this.$message.error(`${visitorTitle}的手机号格式不正确`);\r\n          return false;\r\n        }\r\n\r\n        // 验证身份证号\r\n        if (!visitor.idCard || visitor.idCard.trim() === '') {\r\n          this.$message.error(`请输入${visitorTitle}的身份证号`);\r\n          return false;\r\n        }\r\n\r\n        const idCard = visitor.idCard.trim().toUpperCase();\r\n\r\n        // 如果是18位身份证号，验证格式\r\n        if (idCard.length === 18) {\r\n          const idPattern = /^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$/;\r\n          if (!idPattern.test(idCard)) {\r\n            this.$message.error(`${visitorTitle}的身份证号格式不正确`);\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 检查身份证号是否重复\r\n        for (let j = 0; j < this.formData.visitors.length; j++) {\r\n          if (i !== j && visitor.idCard.trim().toUpperCase() === this.formData.visitors[j].idCard.trim().toUpperCase()) {\r\n            this.$message.error(`${visitorTitle}与访客${j === 0 ? '主联系人' : j + 1}的身份证号重复`);\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 检查手机号是否重复\r\n        for (let j = 0; j < this.formData.visitors.length; j++) {\r\n          if (i !== j && visitor.phone.trim() === this.formData.visitors[j].phone.trim()) {\r\n            this.$message.error(`${visitorTitle}与访客${j === 0 ? '主联系人' : j + 1}的手机号重复，请确认是否为同一人`);\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 清理和标准化数据\r\n        visitor.name = visitor.name.trim();\r\n        visitor.phone = visitor.phone.trim();\r\n        visitor.idCard = visitor.idCard.trim().toUpperCase();\r\n        visitor.company = (visitor.company || '').trim();\r\n      }\r\n\r\n      return true;\r\n    },\r\n\r\n    // 验证时间\r\n    validateTimes() {\r\n      if (!this.formData.visitInfo.plannedEntryDatetime) {\r\n        this.$message.error('请选择预计来访时间');\r\n        return false;\r\n      }\r\n\r\n      if (!this.formData.visitInfo.plannedExitDatetime) {\r\n        this.$message.error('请选择预计离开时间');\r\n        return false;\r\n      }\r\n\r\n      const entryTime = new Date(this.formData.visitInfo.plannedEntryDatetime);\r\n      const exitTime = new Date(this.formData.visitInfo.plannedExitDatetime);\r\n      const now = new Date();\r\n\r\n      // 检查来访时间不能早于当前时间1小时前\r\n      const oneHourBefore = new Date(now.getTime() - 60 * 60 * 1000);\r\n      if (entryTime < oneHourBefore) {\r\n        this.$message.error('预计来访时间不能早于当前时间1小时前');\r\n        return false;\r\n      }\r\n\r\n      // 检查离开时间必须晚于来访时间\r\n      if (exitTime <= entryTime) {\r\n        this.$message.error('预计离开时间必须晚于来访时间');\r\n        return false;\r\n      }\r\n\r\n      // 检查访问时长不能超过24小时\r\n      const visitDuration = exitTime.getTime() - entryTime.getTime();\r\n      const maxDuration = 24 * 60 * 60 * 1000; // 24小时\r\n      if (visitDuration > maxDuration) {\r\n        this.$message.error('单次访问时长不能超过24小时');\r\n        return false;\r\n      }\r\n\r\n      return true;\r\n    },\r\n\r\n    // 重置表单\r\n    resetForm() {\r\n      this.$confirm('确定要重置表单吗？当前填写的信息将会丢失。', '确认重置', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 重置所有数据\r\n        this.registrationCompleted = false;\r\n\r\n        this.formData = {\r\n          visitors: [\r\n            {\r\n              name: '',\r\n              phone: '',\r\n              idCard: '',\r\n              company: '',\r\n              isMainContact: true\r\n            }\r\n          ],\r\n          visitInfo: {\r\n            reasonForVisit: '',\r\n            hostEmployeeName: '',\r\n            departmentVisited: '',\r\n            vehiclePlateNumber: '',\r\n            plannedEntryDatetime: '',\r\n            plannedExitDatetime: ''\r\n          }\r\n        };\r\n\r\n        this.registrationId = null;\r\n\r\n        this.$message.success('表单已重置');\r\n      });\r\n    },\r\n\r\n    // 打印凭证\r\n    printCredential() {\r\n      this.$message.info('打印功能开发中...');\r\n    },\r\n\r\n    // 格式化时间显示\r\n    parseTime(time) {\r\n      if (!time) return '';\r\n      const date = new Date(time);\r\n      if (isNaN(date.getTime())) return '';\r\n\r\n      const year = date.getFullYear();\r\n      const month = String(date.getMonth() + 1).padStart(2, '0');\r\n      const day = String(date.getDate()).padStart(2, '0');\r\n      const hours = String(date.getHours()).padStart(2, '0');\r\n      const minutes = String(date.getMinutes()).padStart(2, '0');\r\n\r\n      return `${year}-${month}-${day} ${hours}:${minutes}`;\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 主容器 */\r\n.pc-register-container {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n  font-family: 'Helvetica Neue', Arial, sans-serif;\r\n}\r\n\r\n/* 顶部导航 */\r\n.top-nav {\r\n  background: #fff;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  padding: 0 40px;\r\n  position: sticky;\r\n  top: 0;\r\n  z-index: 100;\r\n}\r\n\r\n.nav-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  height: 70px;\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.logo-section {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n}\r\n\r\n.nav-logo {\r\n  height: 40px;\r\n}\r\n\r\n.company-name {\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n}\r\n\r\n.nav-actions {\r\n  display: flex;\r\n  gap: 20px;\r\n}\r\n\r\n/* 主内容区域 */\r\n.main-content {\r\n  display: flex;\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n  padding: 40px;\r\n  gap: 40px;\r\n  min-height: calc(100vh - 110px);\r\n}\r\n\r\n/* 左侧信息面板 */\r\n.info-panel {\r\n  flex: 0 0 400px;\r\n}\r\n\r\n.welcome-card {\r\n  background: #fff;\r\n  border-radius: 16px;\r\n  padding: 32px;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\r\n  height: fit-content;\r\n  position: sticky;\r\n  top: 110px;\r\n}\r\n\r\n.welcome-icon {\r\n  text-align: center;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.welcome-icon i {\r\n  font-size: 64px;\r\n  color: #3498db;\r\n}\r\n\r\n.welcome-card h2 {\r\n  text-align: center;\r\n  color: #2c3e50;\r\n  margin-bottom: 16px;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n}\r\n\r\n.welcome-desc {\r\n  text-align: center;\r\n  color: #7f8c8d;\r\n  margin-bottom: 32px;\r\n  font-size: 16px;\r\n  line-height: 1.6;\r\n}\r\n\r\n/* 流程步骤 */\r\n.process-steps {\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.step-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n  margin-bottom: 24px;\r\n  padding: 16px;\r\n  border-radius: 12px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.step-item.active {\r\n  background: linear-gradient(135deg, #3498db, #2980b9);\r\n  color: white;\r\n  transform: translateX(8px);\r\n}\r\n\r\n.step-item:not(.active) {\r\n  background: #f8f9fa;\r\n  color: #6c757d;\r\n}\r\n\r\n.step-circle {\r\n  width: 32px;\r\n  height: 32px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-weight: bold;\r\n  font-size: 14px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.step-item.active .step-circle {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  color: white;\r\n}\r\n\r\n.step-item:not(.active) .step-circle {\r\n  background: #dee2e6;\r\n  color: #6c757d;\r\n}\r\n\r\n.step-text h4 {\r\n  margin: 0 0 4px 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.step-text p {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  opacity: 0.8;\r\n}\r\n\r\n/* 安全提示 */\r\n.security-tips {\r\n  background: #f8f9fa;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  border-left: 4px solid #3498db;\r\n}\r\n\r\n.security-tips h4 {\r\n  margin: 0 0 12px 0;\r\n  color: #2c3e50;\r\n  font-size: 16px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.security-tips ul {\r\n  margin: 0;\r\n  padding-left: 20px;\r\n  color: #6c757d;\r\n}\r\n\r\n.security-tips li {\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n  line-height: 1.4;\r\n}\r\n\r\n/* 右侧表单面板 */\r\n.form-panel {\r\n  flex: 1;\r\n  background: #fff;\r\n  border-radius: 16px;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n/* 进度指示器 */\r\n.progress-indicator {\r\n  background: #f8f9fa;\r\n  padding: 24px 32px;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.progress-bar {\r\n  height: 6px;\r\n  background: #e9ecef;\r\n  border-radius: 3px;\r\n  overflow: hidden;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.progress-fill {\r\n  height: 100%;\r\n  background: linear-gradient(90deg, #3498db, #2980b9);\r\n  border-radius: 3px;\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.progress-text {\r\n  text-align: center;\r\n  color: #6c757d;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 表单内容 */\r\n.form-content {\r\n  padding: 32px;\r\n  max-height: calc(100vh - 300px);\r\n  overflow-y: auto;\r\n}\r\n\r\n.form-header {\r\n  text-align: center;\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.form-header h3 {\r\n  color: #2c3e50;\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  margin-bottom: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 12px;\r\n}\r\n\r\n.form-header p {\r\n  color: #7f8c8d;\r\n  margin: 0;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 验证方式网格 */\r\n.verify-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 20px;\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.verify-card {\r\n  position: relative;\r\n  background: #f8f9fa;\r\n  border: 2px solid #e9ecef;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.verify-card:hover {\r\n  border-color: #3498db;\r\n  background: #fff;\r\n  box-shadow: 0 4px 16px rgba(52, 152, 219, 0.15);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.verify-card.selected {\r\n  border-color: #3498db;\r\n  background: linear-gradient(135deg, #3498db, #2980b9);\r\n  color: white;\r\n  box-shadow: 0 8px 24px rgba(52, 152, 219, 0.3);\r\n}\r\n\r\n.verify-icon {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.verify-icon i {\r\n  font-size: 48px;\r\n  color: #3498db;\r\n}\r\n\r\n.verify-card.selected .verify-icon i {\r\n  color: white;\r\n}\r\n\r\n.verify-card h4 {\r\n  margin: 0 0 8px 0;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.verify-card p {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  opacity: 0.8;\r\n}\r\n\r\n.verify-status {\r\n  position: absolute;\r\n  top: 12px;\r\n  right: 12px;\r\n  width: 24px;\r\n  height: 24px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n/* 验证操作区域 */\r\n.verify-operation {\r\n  margin-top: 32px;\r\n  padding: 24px;\r\n  background: #f8f9fa;\r\n  border-radius: 12px;\r\n}\r\n\r\n.operation-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.operation-header h4 {\r\n  margin: 0;\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n/* 身份表单 */\r\n.identity-form {\r\n  background: white;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n}\r\n\r\n/* 身份证读卡器 */\r\n.id-card-reader {\r\n  text-align: center;\r\n}\r\n\r\n.reader-visual {\r\n  background: white;\r\n  padding: 40px;\r\n  border-radius: 12px;\r\n}\r\n\r\n.reader-animation {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.rotating {\r\n  animation: rotate 2s linear infinite;\r\n}\r\n\r\n@keyframes rotate {\r\n  from { transform: rotate(0deg); }\r\n  to { transform: rotate(360deg); }\r\n}\r\n\r\n.reader-animation i {\r\n  font-size: 64px;\r\n  color: #3498db;\r\n}\r\n\r\n.reader-visual h4 {\r\n  color: #2c3e50;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.reader-visual p {\r\n  color: #7f8c8d;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.reader-tips {\r\n  margin: 24px 0;\r\n}\r\n\r\n/* 人脸识别 */\r\n.face-recognition {\r\n  text-align: center;\r\n}\r\n\r\n.camera-container {\r\n  background: white;\r\n  padding: 40px;\r\n  border-radius: 12px;\r\n}\r\n\r\n.camera-frame {\r\n  position: relative;\r\n  width: 280px;\r\n  height: 210px;\r\n  margin: 0 auto 24px;\r\n  border: 3px solid #3498db;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #f8f9fa;\r\n  overflow: hidden;\r\n}\r\n\r\n.camera-overlay {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 16px;\r\n}\r\n\r\n.face-outline {\r\n  width: 120px;\r\n  height: 150px;\r\n  border: 2px dashed #3498db;\r\n  border-radius: 60px;\r\n  opacity: 0.6;\r\n}\r\n\r\n.camera-icon {\r\n  font-size: 32px;\r\n  color: #3498db;\r\n}\r\n\r\n.scanning-line {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 2px;\r\n  background: linear-gradient(90deg, transparent, #3498db, transparent);\r\n  animation: scan 2s ease-in-out infinite;\r\n}\r\n\r\n@keyframes scan {\r\n  0% { transform: translateY(0); }\r\n  50% { transform: translateY(206px); }\r\n  100% { transform: translateY(0); }\r\n}\r\n\r\n/* 表单分组 */\r\n.form-section {\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.section-title {\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin-bottom: 16px;\r\n  padding-bottom: 8px;\r\n  border-bottom: 2px solid #e9ecef;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n/* 同行人员 */\r\n.no-companions {\r\n  text-align: center;\r\n  padding: 40px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.no-companions i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  color: #bdc3c7;\r\n}\r\n\r\n.companions-table {\r\n  background: #f8f9fa;\r\n  padding: 16px;\r\n  border-radius: 8px;\r\n}\r\n\r\n/* 成功页面 */\r\n.success-content {\r\n  text-align: center;\r\n}\r\n\r\n.success-icon {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.success-icon i {\r\n  font-size: 80px;\r\n  color: #27ae60;\r\n}\r\n\r\n.success-content h4 {\r\n  color: #2c3e50;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.success-message {\r\n  color: #7f8c8d;\r\n  font-size: 16px;\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.register-summary {\r\n  background: #f8f9fa;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n  margin-bottom: 32px;\r\n  text-align: left;\r\n}\r\n\r\n.register-summary h5 {\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin-bottom: 16px;\r\n  text-align: center;\r\n}\r\n\r\n.access-credential {\r\n  background: #f8f9fa;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n  text-align: left;\r\n}\r\n\r\n.access-credential h5 {\r\n  color: #2c3e50;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin-bottom: 16px;\r\n  text-align: center;\r\n}\r\n\r\n.credential-card {\r\n  display: flex;\r\n  gap: 24px;\r\n  background: white;\r\n  padding: 24px;\r\n  border-radius: 12px;\r\n  border: 2px solid #e9ecef;\r\n}\r\n\r\n.qr-code-container {\r\n  flex: 0 0 120px;\r\n  text-align: center;\r\n}\r\n\r\n.qr-code {\r\n  width: 120px;\r\n  height: 120px;\r\n  border: 2px solid #e9ecef;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #f8f9fa;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.qr-code i {\r\n  font-size: 48px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.qr-code-id {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n  font-family: monospace;\r\n  margin: 0;\r\n}\r\n\r\n.credential-info {\r\n  flex: 1;\r\n}\r\n\r\n.credential-info h6 {\r\n  color: #2c3e50;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.credential-info ul {\r\n  margin: 0;\r\n  padding-left: 20px;\r\n  color: #6c757d;\r\n}\r\n\r\n.credential-info li {\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n  line-height: 1.4;\r\n}\r\n\r\n/* 底部操作按钮 */\r\n.form-actions {\r\n  padding: 24px 32px;\r\n  background: #f8f9fa;\r\n  border-top: 1px solid #e9ecef;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.final-actions {\r\n  display: flex;\r\n  gap: 16px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n  .main-content {\r\n    flex-direction: column;\r\n    gap: 24px;\r\n  }\r\n  \r\n  .info-panel {\r\n    flex: none;\r\n  }\r\n  \r\n  .welcome-card {\r\n    position: static;\r\n  }\r\n  \r\n  .verify-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .main-content {\r\n    padding: 20px;\r\n  }\r\n  \r\n  .nav-content {\r\n    padding: 0 20px;\r\n  }\r\n  \r\n  .company-name {\r\n    font-size: 16px;\r\n  }\r\n  \r\n  .form-content {\r\n    padding: 20px;\r\n  }\r\n  \r\n  .credential-card {\r\n    flex-direction: column;\r\n    text-align: center;\r\n  }\r\n  \r\n  .form-actions {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n}\r\n\r\n/* Element UI 样式覆盖 */\r\n.el-form-item {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.el-input__inner {\r\n  height: 44px;\r\n  border-radius: 8px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.el-input__inner:focus {\r\n  border-color: #3498db;\r\n  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.1);\r\n}\r\n\r\n.el-select {\r\n  width: 100%;\r\n}\r\n\r\n.el-date-editor {\r\n  width: 100%;\r\n}\r\n\r\n.el-button--large {\r\n  height: 44px;\r\n  padding: 12px 24px;\r\n  border-radius: 8px;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n}\r\n\r\n.el-button--primary {\r\n  background: linear-gradient(135deg, #3498db, #2980b9);\r\n  border: none;\r\n}\r\n\r\n.el-button--primary:hover {\r\n  background: linear-gradient(135deg, #2980b9, #1f4e79);\r\n}\r\n\r\n.el-descriptions {\r\n  border-radius: 8px;\r\n}\r\n\r\n.el-table {\r\n  border-radius: 8px;\r\n}\r\n\r\n.el-alert {\r\n  border-radius: 8px;\r\n}\r\n</style>\r\n"]}]}