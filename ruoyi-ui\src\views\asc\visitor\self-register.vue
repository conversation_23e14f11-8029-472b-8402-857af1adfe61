<template>
  <div class="pc-register-container">
    <!-- 顶部导航栏 -->
    <div class="top-nav">
      <div class="nav-content">
        <div class="logo-section">
          <img src="@/assets/logo/logo.png" alt="公司logo" class="nav-logo" />
          <span class="company-name">智慧园区访客登记系统</span>
        </div>
        <div class="nav-actions">
          <el-button type="text" @click="showHelp">使用帮助</el-button>
          <el-button type="text" @click="contactService">联系客服</el-button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧信息面板 -->
      <div class="info-panel">
        <div class="welcome-card">
          <div class="welcome-icon">
            <i class="el-icon-office-building"></i>
          </div>
          <h2>欢迎访问智慧园区</h2>
          <p class="welcome-desc">为了您的安全和便利，请填写以下信息完成访客登记</p>

          <div class="process-steps">
            <div class="step-item" :class="{ active: !registrationCompleted }">
              <div class="step-circle">1</div>
              <div class="step-text">
                <h4>填写信息</h4>
                <p>完善访问详细信息</p>
              </div>
            </div>
            <div class="step-item" :class="{ active: registrationCompleted }">
              <div class="step-circle">2</div>
              <div class="step-text">
                <h4>获取凭证</h4>
                <p>生成访问凭证二维码</p>
              </div>
            </div>
          </div>

          <div class="security-tips">
            <h4><i class="el-icon-lock"></i> 安全提示</h4>
            <ul>
              <li>请确保提供真实有效的身份信息</li>
              <li>您的个人信息将被严格保密</li>
              <li>访问凭证仅限当次使用</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- 右侧表单区域 -->
      <div class="form-panel">
        <!-- 表单标题 -->
        <div class="form-header">
          <h3 v-if="!registrationCompleted"><i class="el-icon-edit"></i> 访客登记信息</h3>
          <h3 v-else><i class="el-icon-circle-check"></i> 登记完成</h3>
          <p v-if="!registrationCompleted">请填写详细的访问信息</p>
          <p v-else>您的访客登记已成功提交</p>
        </div>

        <!-- 表单内容 -->
        <div class="form-content" v-if="!registrationCompleted">
          <el-form ref="visitForm" :model="formData" :rules="visitRules"
                   label-width="120px" class="visit-form">

            <!-- 基本访问信息 -->
            <div class="form-section">
              <h4 class="section-title">基本访问信息</h4>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="来访事由" prop="visitInfo.reasonForVisit">
                    <el-input v-model="formData.visitInfo.reasonForVisit" placeholder="请输入来访事由"
                              prefix-icon="el-icon-tickets" type="textarea" :rows="3" maxlength="200" show-word-limit />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="被访人姓名" prop="visitInfo.hostEmployeeName">
                    <el-input v-model="formData.visitInfo.hostEmployeeName" placeholder="请输入被访人姓名"
                              prefix-icon="el-icon-user" maxlength="20" />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="被访部门" prop="visitInfo.departmentVisited">
                    <el-input v-model="formData.visitInfo.departmentVisited" placeholder="请输入被访部门"
                              prefix-icon="el-icon-office-building" maxlength="50" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="车牌号">
                    <el-input v-model="formData.visitInfo.vehiclePlateNumber" placeholder="如有车辆请填写车牌号，多个用逗号分隔"
                              prefix-icon="el-icon-truck" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 时间信息 -->
            <div class="form-section">
              <h4 class="section-title">访问时间</h4>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="预计到访时间" prop="visitInfo.plannedEntryDatetime">
                    <el-date-picker
                      v-model="formData.visitInfo.plannedEntryDatetime"
                      type="datetime"
                      placeholder="选择到访时间"
                      style="width: 100%"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      :picker-options="entryPickerOptions"
                      @change="onArrivalTimeChange"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="预计离开时间" prop="visitInfo.plannedExitDatetime">
                    <el-date-picker
                      v-model="formData.visitInfo.plannedExitDatetime"
                      type="datetime"
                      placeholder="选择离开时间"
                      style="width: 100%"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      :picker-options="exitPickerOptions"
                      @change="onDepartureTimeChange"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 访客信息 -->
            <div class="form-section">
              <div class="section-header">
                <h4 class="section-title">访客信息（第一位为主联系人）共 {{ totalVisitors }} 人</h4>
                <el-button type="primary" size="small" icon="el-icon-plus" @click="addVisitor" :disabled="formData.visitors.length >= 10">
                  添加访客
                </el-button>
              </div>

              <div class="visitors-list">
                <div v-for="(visitor, index) in formData.visitors" :key="index" class="visitor-item">
                  <div class="visitor-header">
                    <h5 class="visitor-title">
                      {{ index === 0 ? '主联系人' : `访客 ${index + 1}` }}
                    </h5>
                    <el-button v-if="index > 0" size="mini" type="danger" icon="el-icon-delete"
                               @click="removeVisitor(index)" circle />
                  </div>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item :label="`姓名`" :prop="`visitors.${index}.name`">
                        <el-input v-model="visitor.name" placeholder="请输入姓名" maxlength="20" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item :label="`手机号`" :prop="`visitors.${index}.phone`">
                        <el-input v-model="visitor.phone" placeholder="请输入手机号" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item :label="`身份证号`" :prop="`visitors.${index}.idCard`">
                        <el-input v-model="visitor.idCard" placeholder="请输入身份证号" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item :label="`公司名称`">
                        <el-input v-model="visitor.company" placeholder="请输入公司名称（可选）" maxlength="100" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>
              </div>
            </div>
          </el-form>
        </div>

        <!-- 登记成功页面 -->
        <div class="success-content" v-if="registrationCompleted">
          <div class="success-icon">
            <i class="el-icon-circle-check"></i>
          </div>

          <h4>登记成功！</h4>
          <p class="success-message">您的访客登记已提交，请等待审核通过</p>

          <!-- 登记信息摘要 -->
          <div class="register-summary">
            <h5>登记信息摘要</h5>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="主联系人">
                {{ mainContact.name }}
              </el-descriptions-item>
              <el-descriptions-item label="联系电话">
                {{ mainContact.phone }}
              </el-descriptions-item>
              <el-descriptions-item label="被访人">
                {{ formData.visitInfo.hostEmployeeName }}
              </el-descriptions-item>
              <el-descriptions-item label="被访部门">
                {{ formData.visitInfo.departmentVisited }}
              </el-descriptions-item>
              <el-descriptions-item label="来访事由" span="2">
                {{ formData.visitInfo.reasonForVisit }}
              </el-descriptions-item>
              <el-descriptions-item label="预计到访时间">
                {{ parseTime(formData.visitInfo.plannedEntryDatetime) }}
              </el-descriptions-item>
              <el-descriptions-item label="预计离开时间">
                {{ parseTime(formData.visitInfo.plannedExitDatetime) }}
              </el-descriptions-item>
              <el-descriptions-item label="访客总数">
                {{ totalVisitors }} 人
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <!-- 访问凭证 -->
          <div class="access-credential" v-if="registrationId">
            <h5>访问凭证</h5>
            <div class="credential-card">
              <div class="qr-code-container">
                <div class="qr-code">
                  <i class="el-icon-qrcode"></i>
                </div>
                <p class="qr-code-id">{{ registrationId }}</p>
              </div>
              <div class="credential-info">
                <h6>使用说明</h6>
                <ul>
                  <li>请保存此二维码截图</li>
                  <li>审核通过后可用于园区门禁</li>
                  <li>凭证仅限当次访问使用</li>
                  <li>如有疑问请联系园区前台</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部操作按钮 -->
        <div class="form-actions">
          <el-button v-if="!registrationCompleted"
                     type="primary" @click="submitForm" size="large" :loading="submitting">
            {{ submitting ? '登记中...' : `确认登记 (${totalVisitors}人)` }}
          </el-button>

          <el-button v-if="!registrationCompleted" @click="resetForm" size="large">
            重置表单
          </el-button>

          <div v-if="registrationCompleted" class="final-actions">
            <el-button type="primary" @click="printCredential" size="large">
              <i class="el-icon-printer"></i> 打印凭证
            </el-button>
            <el-button @click="resetForm" size="large">
              <i class="el-icon-refresh"></i> 重新登记
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { submitVisitorRegistration } from "@/api/asc/visitor";

export default {
  name: "SelfRegister",
  data() {
    return {
      submitting: false,
      registrationCompleted: false,

      // 表单数据 - 与微信端保持一致的结构
      formData: {
        // 访客列表（第一位为主联系人）
        visitors: [
          {
            name: '',
            phone: '',
            idCard: '',
            company: '',
            isMainContact: true
          }
        ],
        // 来访信息
        visitInfo: {
          reasonForVisit: '',
          hostEmployeeName: '',
          departmentVisited: '',
          vehiclePlateNumber: '',
          plannedEntryDatetime: '',
          plannedExitDatetime: ''
        }
      },

      // 表单验证规则 - 与微信端保持一致
      visitRules: {
        'visitInfo.reasonForVisit': [
          { required: true, message: '请输入来访事由', trigger: 'blur' },
          { min: 2, max: 200, message: '来访事由长度应在2-200个字符之间', trigger: 'blur' }
        ],
        'visitInfo.hostEmployeeName': [
          { required: true, message: '请输入被访人姓名', trigger: 'blur' },
          { min: 2, max: 20, message: '被访人姓名长度应在2-20个字符之间', trigger: 'blur' }
        ],
        'visitInfo.departmentVisited': [
          { required: true, message: '请输入被访部门', trigger: 'blur' },
          { min: 2, max: 50, message: '部门名称长度应在2-50个字符之间', trigger: 'blur' }
        ],
        'visitInfo.plannedEntryDatetime': [
          { required: true, message: '请选择预计来访时间', trigger: 'change' }
        ],
        'visitInfo.plannedExitDatetime': [
          { required: true, message: '请选择预计离开时间', trigger: 'change' }
        ]
      },

      // 登记结果
      registrationId: null,

      // 时间选择器配置
      entryPickerOptions: {
        shortcuts: [{
          text: '现在',
          onClick(picker) {
            picker.$emit('pick', new Date());
          }
        }, {
          text: '1小时后',
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() + 3600 * 1000);
            picker.$emit('pick', date);
          }
        }, {
          text: '明天上午9点',
          onClick(picker) {
            const date = new Date();
            date.setDate(date.getDate() + 1);
            date.setHours(9, 0, 0, 0);
            picker.$emit('pick', date);
          }
        }],
        disabledDate(time) {
          // 允许选择当前时间前1小时到未来30天
          const oneHourBefore = Date.now() - 3600 * 1000;
          const thirtyDaysLater = Date.now() + 30 * 24 * 3600 * 1000;
          return time.getTime() < oneHourBefore || time.getTime() > thirtyDaysLater;
        }
      },

      exitPickerOptions: {
        shortcuts: [{
          text: '2小时后',
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() + 2 * 3600 * 1000);
            picker.$emit('pick', date);
          }
        }, {
          text: '今天下午6点',
          onClick(picker) {
            const date = new Date();
            date.setHours(18, 0, 0, 0);
            if (date.getTime() < Date.now()) {
              date.setDate(date.getDate() + 1);
            }
            picker.$emit('pick', date);
          }
        }, {
          text: '明天下午6点',
          onClick(picker) {
            const date = new Date();
            date.setDate(date.getDate() + 1);
            date.setHours(18, 0, 0, 0);
            picker.$emit('pick', date);
          }
        }],
        disabledDate(time) {
          // 允许选择当前时间到未来30天
          const thirtyDaysLater = Date.now() + 30 * 24 * 3600 * 1000;
          return time.getTime() < Date.now() - 8.64e7 || time.getTime() > thirtyDaysLater;
        }
      }
    };
  },

  computed: {
    // 总访客人数
    totalVisitors() {
      return this.formData.visitors.length;
    },

    // 主联系人信息
    mainContact() {
      return this.formData.visitors[0] || {};
    }
  },

  methods: {
    // 添加访客
    addVisitor() {
      if (this.formData.visitors.length >= 10) {
        this.$message.warning('最多只能添加10名访客');
        return;
      }

      this.formData.visitors.push({
        name: '',
        phone: '',
        idCard: '',
        company: '',
        isMainContact: false
      });

      this.$message.success('已添加访客');
    },

    // 移除访客
    removeVisitor(index) {
      if (index === 0) {
        this.$message.warning('不能删除主联系人');
        return;
      }

      this.formData.visitors.splice(index, 1);
      this.$message.success('已移除访客');
    },

    // 格式化日期为后端期望的格式
    formatDateForBackend(dateStr) {
      if (!dateStr) return '';

      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return '';

      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    /** 来访时间变更事件 */
    onArrivalTimeChange(value) {
      console.log('预计来访时间变更:', value);
      // 自动设置离开时间为来访时间后4小时
      if (value && !this.formData.visitInfo.plannedExitDatetime) {
        const arrivalTime = new Date(value);
        const departureTime = new Date(arrivalTime.getTime() + 4 * 60 * 60 * 1000);

        const year = departureTime.getFullYear();
        const month = String(departureTime.getMonth() + 1).padStart(2, '0');
        const day = String(departureTime.getDate()).padStart(2, '0');
        const hours = String(departureTime.getHours()).padStart(2, '0');
        const minutes = String(departureTime.getMinutes()).padStart(2, '0');
        const seconds = String(departureTime.getSeconds()).padStart(2, '0');

        this.formData.visitInfo.plannedExitDatetime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      }
    },

    /** 离开时间变更事件 */
    onDepartureTimeChange(value) {
      console.log('预计离开时间变更:', value);
      // 验证离开时间不能早于来访时间
      if (value && this.formData.visitInfo.plannedEntryDatetime) {
        const arrivalTime = new Date(this.formData.visitInfo.plannedEntryDatetime);
        const departureTime = new Date(value);

        if (departureTime <= arrivalTime) {
          this.$message.warning('预计离开时间不能早于或等于来访时间');
          this.formData.visitInfo.plannedExitDatetime = '';
        }
      }
    },

    /** 提交表单 */
    async submitForm() {
      try {
        // 验证表单
        await this.$refs.visitForm.validate();

        // 验证访客信息
        if (!this.validateVisitors()) {
          return;
        }

        // 验证时间
        if (!this.validateTimes()) {
          return;
        }

        this.submitting = true;

        // 获取主联系人信息
        const mainContact = this.formData.visitors[0];

        const submitData = {
          // VisitRegistrations 对象
          registration: {
            primaryContactName: mainContact.name.trim(),
            primaryContactPhone: mainContact.phone.trim(),
            reasonForVisit: this.formData.visitInfo.reasonForVisit,
            hostEmployeeName: this.formData.visitInfo.hostEmployeeName,
            hostEmployeeId: null, // 前端暂时没有被访人ID，后端可能会自动处理
            departmentVisited: this.formData.visitInfo.departmentVisited,
            plannedEntryDatetime: this.formatDateForBackend(this.formData.visitInfo.plannedEntryDatetime),
            plannedExitDatetime: this.formatDateForBackend(this.formData.visitInfo.plannedExitDatetime),
            vehiclePlateNumber: this.formData.visitInfo.vehiclePlateNumber || '',
            totalCompanions: this.formData.visitors.length - 1
          },
          // RegistrationAttendees 数组 - 使用后端期望的临时字段
          attendeesList: this.formData.visitors.map((visitor, index) => ({
            visitorName: visitor.name.trim(),
            visitorPhone: visitor.phone.trim(),
            visitorIdCard: visitor.idCard.trim().toUpperCase(),
            visitorCompany: (visitor.company || '').trim(),
            isPrimary: index === 0 ? "1" : "0", // 第一个访客必须是主联系人
            visitorAvatarPhoto: null // 暂时不支持头像上传
          }))
        };

        console.log('提交数据结构:', JSON.stringify(submitData, null, 2));

        // 调用API提交数据
        const response = await submitVisitorRegistration(submitData);

        if (response.code === 200) {
          this.registrationId = response.data || 'VR' + Date.now();
          this.$message.success(`${this.totalVisitors}名访客登记成功！`);
          this.registrationCompleted = true;
        } else {
          this.$message.error(response.msg || '登记失败，请重试');
        }
      } catch (error) {
        console.error('提交表单失败:', error);
        this.$message.error('登记失败，请检查网络连接');
      } finally {
        this.submitting = false;
      }
    },

    // 验证访客信息
    validateVisitors() {
      for (let i = 0; i < this.formData.visitors.length; i++) {
        const visitor = this.formData.visitors[i];
        const visitorTitle = i === 0 ? '主联系人' : `访客${i + 1}`;

        // 验证姓名
        if (!visitor.name || visitor.name.trim() === '') {
          this.$message.error(`请输入${visitorTitle}的姓名`);
          return false;
        }

        if (visitor.name.trim().length < 2 || visitor.name.trim().length > 20) {
          this.$message.error(`${visitorTitle}的姓名长度应在2-20个字符之间`);
          return false;
        }

        // 验证手机号
        if (!visitor.phone || visitor.phone.trim() === '') {
          this.$message.error(`请输入${visitorTitle}的手机号`);
          return false;
        }

        // 验证手机号格式（与后端保持一致）
        const phonePattern = /^1[3-9]\d{9}$/;
        if (!phonePattern.test(visitor.phone.trim())) {
          this.$message.error(`${visitorTitle}的手机号格式不正确`);
          return false;
        }

        // 验证身份证号
        if (!visitor.idCard || visitor.idCard.trim() === '') {
          this.$message.error(`请输入${visitorTitle}的身份证号`);
          return false;
        }

        const idCard = visitor.idCard.trim().toUpperCase();

        // 如果是18位身份证号，验证格式
        if (idCard.length === 18) {
          const idPattern = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
          if (!idPattern.test(idCard)) {
            this.$message.error(`${visitorTitle}的身份证号格式不正确`);
            return false;
          }
        }

        // 检查身份证号是否重复
        for (let j = 0; j < this.formData.visitors.length; j++) {
          if (i !== j && visitor.idCard.trim().toUpperCase() === this.formData.visitors[j].idCard.trim().toUpperCase()) {
            this.$message.error(`${visitorTitle}与访客${j === 0 ? '主联系人' : j + 1}的身份证号重复`);
            return false;
          }
        }

        // 检查手机号是否重复
        for (let j = 0; j < this.formData.visitors.length; j++) {
          if (i !== j && visitor.phone.trim() === this.formData.visitors[j].phone.trim()) {
            this.$message.error(`${visitorTitle}与访客${j === 0 ? '主联系人' : j + 1}的手机号重复，请确认是否为同一人`);
            return false;
          }
        }

        // 清理和标准化数据
        visitor.name = visitor.name.trim();
        visitor.phone = visitor.phone.trim();
        visitor.idCard = visitor.idCard.trim().toUpperCase();
        visitor.company = (visitor.company || '').trim();
      }

      return true;
    },

    // 验证时间
    validateTimes() {
      if (!this.formData.visitInfo.plannedEntryDatetime) {
        this.$message.error('请选择预计来访时间');
        return false;
      }

      if (!this.formData.visitInfo.plannedExitDatetime) {
        this.$message.error('请选择预计离开时间');
        return false;
      }

      const entryTime = new Date(this.formData.visitInfo.plannedEntryDatetime);
      const exitTime = new Date(this.formData.visitInfo.plannedExitDatetime);
      const now = new Date();

      // 检查来访时间不能早于当前时间1小时前
      const oneHourBefore = new Date(now.getTime() - 60 * 60 * 1000);
      if (entryTime < oneHourBefore) {
        this.$message.error('预计来访时间不能早于当前时间1小时前');
        return false;
      }

      // 检查离开时间必须晚于来访时间
      if (exitTime <= entryTime) {
        this.$message.error('预计离开时间必须晚于来访时间');
        return false;
      }

      // 检查访问时长不能超过24小时
      const visitDuration = exitTime.getTime() - entryTime.getTime();
      const maxDuration = 24 * 60 * 60 * 1000; // 24小时
      if (visitDuration > maxDuration) {
        this.$message.error('单次访问时长不能超过24小时');
        return false;
      }

      return true;
    },

    // 重置表单
    resetForm() {
      this.$confirm('确定要重置表单吗？当前填写的信息将会丢失。', '确认重置', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 重置所有数据
        this.registrationCompleted = false;

        this.formData = {
          visitors: [
            {
              name: '',
              phone: '',
              idCard: '',
              company: '',
              isMainContact: true
            }
          ],
          visitInfo: {
            reasonForVisit: '',
            hostEmployeeName: '',
            departmentVisited: '',
            vehiclePlateNumber: '',
            plannedEntryDatetime: '',
            plannedExitDatetime: ''
          }
        };

        this.registrationId = null;

        this.$message.success('表单已重置');
      });
    },

    // 打印凭证
    printCredential() {
      this.$message.info('打印功能开发中...');
    },

    // 格式化时间显示
    parseTime(time) {
      if (!time) return '';
      const date = new Date(time);
      if (isNaN(date.getTime())) return '';

      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}`;
    }
  }
};
</script>

<style scoped>
/* 主容器 */
.pc-register-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family: 'Helvetica Neue', Arial, sans-serif;
}

/* 顶部导航 */
.top-nav {
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 0 40px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
  max-width: 1400px;
  margin: 0 auto;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 15px;
}

.nav-logo {
  height: 40px;
}

.company-name {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
}

.nav-actions {
  display: flex;
  gap: 20px;
}

/* 主内容区域 */
.main-content {
  display: flex;
  max-width: 1400px;
  margin: 0 auto;
  padding: 40px;
  gap: 40px;
  min-height: calc(100vh - 110px);
}

/* 左侧信息面板 */
.info-panel {
  flex: 0 0 400px;
}

.welcome-card {
  background: #fff;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  height: fit-content;
  position: sticky;
  top: 110px;
}

.welcome-icon {
  text-align: center;
  margin-bottom: 24px;
}

.welcome-icon i {
  font-size: 64px;
  color: #3498db;
}

.welcome-card h2 {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 16px;
  font-size: 28px;
  font-weight: 600;
}

.welcome-desc {
  text-align: center;
  color: #7f8c8d;
  margin-bottom: 32px;
  font-size: 16px;
  line-height: 1.6;
}

/* 流程步骤 */
.process-steps {
  margin-bottom: 32px;
}

.step-item {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  padding: 16px;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.step-item.active {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  transform: translateX(8px);
}

.step-item:not(.active) {
  background: #f8f9fa;
  color: #6c757d;
}

.step-circle {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
  flex-shrink: 0;
}

.step-item.active .step-circle {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.step-item:not(.active) .step-circle {
  background: #dee2e6;
  color: #6c757d;
}

.step-text h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
}

.step-text p {
  margin: 0;
  font-size: 14px;
  opacity: 0.8;
}

/* 安全提示 */
.security-tips {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  border-left: 4px solid #3498db;
}

.security-tips h4 {
  margin: 0 0 12px 0;
  color: #2c3e50;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.security-tips ul {
  margin: 0;
  padding-left: 20px;
  color: #6c757d;
}

.security-tips li {
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 1.4;
}

/* 右侧表单面板 */
.form-panel {
  flex: 1;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 进度指示器 */
.progress-indicator {
  background: #f8f9fa;
  padding: 24px 32px;
  border-bottom: 1px solid #e9ecef;
}

.progress-bar {
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3498db, #2980b9);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  text-align: center;
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
}

/* 表单内容 */
.form-content {
  padding: 32px;
  max-height: calc(100vh - 300px);
  overflow-y: auto;
}

.form-header {
  text-align: center;
  margin-bottom: 32px;
}

.form-header h3 {
  color: #2c3e50;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.form-header p {
  color: #7f8c8d;
  margin: 0;
  font-size: 16px;
}

/* 验证方式网格 */
.verify-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 32px;
}

.verify-card {
  position: relative;
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.verify-card:hover {
  border-color: #3498db;
  background: #fff;
  box-shadow: 0 4px 16px rgba(52, 152, 219, 0.15);
  transform: translateY(-2px);
}

.verify-card.selected {
  border-color: #3498db;
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  box-shadow: 0 8px 24px rgba(52, 152, 219, 0.3);
}

.verify-icon {
  margin-bottom: 16px;
}

.verify-icon i {
  font-size: 48px;
  color: #3498db;
}

.verify-card.selected .verify-icon i {
  color: white;
}

.verify-card h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
}

.verify-card p {
  margin: 0;
  font-size: 14px;
  opacity: 0.8;
}

.verify-status {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 验证操作区域 */
.verify-operation {
  margin-top: 32px;
  padding: 24px;
  background: #f8f9fa;
  border-radius: 12px;
}

.operation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.operation-header h4 {
  margin: 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

/* 身份表单 */
.identity-form {
  background: white;
  padding: 24px;
  border-radius: 12px;
}

/* 身份证读卡器 */
.id-card-reader {
  text-align: center;
}

.reader-visual {
  background: white;
  padding: 40px;
  border-radius: 12px;
}

.reader-animation {
  margin-bottom: 24px;
}

.rotating {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.reader-animation i {
  font-size: 64px;
  color: #3498db;
}

.reader-visual h4 {
  color: #2c3e50;
  margin-bottom: 8px;
}

.reader-visual p {
  color: #7f8c8d;
  margin-bottom: 24px;
}

.reader-tips {
  margin: 24px 0;
}

/* 人脸识别 */
.face-recognition {
  text-align: center;
}

.camera-container {
  background: white;
  padding: 40px;
  border-radius: 12px;
}

.camera-frame {
  position: relative;
  width: 280px;
  height: 210px;
  margin: 0 auto 24px;
  border: 3px solid #3498db;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  overflow: hidden;
}

.camera-overlay {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.face-outline {
  width: 120px;
  height: 150px;
  border: 2px dashed #3498db;
  border-radius: 60px;
  opacity: 0.6;
}

.camera-icon {
  font-size: 32px;
  color: #3498db;
}

.scanning-line {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, #3498db, transparent);
  animation: scan 2s ease-in-out infinite;
}

@keyframes scan {
  0% { transform: translateY(0); }
  50% { transform: translateY(206px); }
  100% { transform: translateY(0); }
}

/* 表单分组 */
.form-section {
  margin-bottom: 32px;
}

.section-title {
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e9ecef;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

/* 同行人员 */
.no-companions {
  text-align: center;
  padding: 40px;
  color: #7f8c8d;
}

.no-companions i {
  font-size: 48px;
  margin-bottom: 16px;
  color: #bdc3c7;
}

.companions-table {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
}

/* 成功页面 */
.success-content {
  text-align: center;
}

.success-icon {
  margin-bottom: 24px;
}

.success-icon i {
  font-size: 80px;
  color: #27ae60;
}

.success-content h4 {
  color: #2c3e50;
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 12px;
}

.success-message {
  color: #7f8c8d;
  font-size: 16px;
  margin-bottom: 32px;
}

.register-summary {
  background: #f8f9fa;
  padding: 24px;
  border-radius: 12px;
  margin-bottom: 32px;
  text-align: left;
}

.register-summary h5 {
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  text-align: center;
}

.access-credential {
  background: #f8f9fa;
  padding: 24px;
  border-radius: 12px;
  text-align: left;
}

.access-credential h5 {
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  text-align: center;
}

.credential-card {
  display: flex;
  gap: 24px;
  background: white;
  padding: 24px;
  border-radius: 12px;
  border: 2px solid #e9ecef;
}

.qr-code-container {
  flex: 0 0 120px;
  text-align: center;
}

.qr-code {
  width: 120px;
  height: 120px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  margin-bottom: 8px;
}

.qr-code i {
  font-size: 48px;
  color: #7f8c8d;
}

.qr-code-id {
  font-size: 12px;
  color: #6c757d;
  font-family: monospace;
  margin: 0;
}

.credential-info {
  flex: 1;
}

.credential-info h6 {
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
}

.credential-info ul {
  margin: 0;
  padding-left: 20px;
  color: #6c757d;
}

.credential-info li {
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 1.4;
}

/* 底部操作按钮 */
.form-actions {
  padding: 24px 32px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.final-actions {
  display: flex;
  gap: 16px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
    gap: 24px;
  }
  
  .info-panel {
    flex: none;
  }
  
  .welcome-card {
    position: static;
  }
  
  .verify-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 20px;
  }
  
  .nav-content {
    padding: 0 20px;
  }
  
  .company-name {
    font-size: 16px;
  }
  
  .form-content {
    padding: 20px;
  }
  
  .credential-card {
    flex-direction: column;
    text-align: center;
  }
  
  .form-actions {
    flex-direction: column;
    gap: 16px;
  }
}

/* Element UI 样式覆盖 */
.el-form-item {
  margin-bottom: 24px;
}

.el-input__inner {
  height: 44px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.el-input__inner:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.1);
}

.el-select {
  width: 100%;
}

.el-date-editor {
  width: 100%;
}

.el-button--large {
  height: 44px;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
}

.el-button--primary {
  background: linear-gradient(135deg, #3498db, #2980b9);
  border: none;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, #2980b9, #1f4e79);
}

.el-descriptions {
  border-radius: 8px;
}

.el-table {
  border-radius: 8px;
}

.el-alert {
  border-radius: 8px;
}
</style>
