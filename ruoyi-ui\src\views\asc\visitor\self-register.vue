<template>
  <div class="pc-register-container">
    <!-- 顶部导航栏 -->
    <div class="top-nav">
      <div class="nav-content">
        <div class="logo-section">
          <img src="@/assets/logo/logo.png" alt="公司logo" class="nav-logo" />
          <span class="company-name">智慧园区访客登记系统</span>
        </div>
        <div class="nav-actions">
          <el-button type="text" @click="showHelp">使用帮助</el-button>
          <el-button type="text" @click="contactService">联系客服</el-button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧信息面板 -->
      <div class="info-panel">
        <div class="welcome-card">
          <div class="welcome-icon">
            <i class="el-icon-office-building"></i>
          </div>
          <h2>欢迎访问智慧园区</h2>
          <p class="welcome-desc">为了您的安全和便利，请按照以下步骤完成访客登记</p>
          
          <div class="process-steps">
            <div class="step-item active">
              <div class="step-circle">1</div>
              <div class="step-text">
                <h4>填写信息</h4>
                <p>完善访问详细信息</p>
              </div>
            </div>
            <div class="step-item" :class="{ active: registrationCompleted }">
              <div class="step-circle">2</div>
              <div class="step-text">
                <h4>获取凭证</h4>
                <p>生成访问凭证二维码</p>
              </div>
            </div>
          </div>

          <div class="security-tips">
            <h4><i class="el-icon-lock"></i> 安全提示</h4>
            <ul>
              <li>请确保提供真实有效的身份信息</li>
              <li>您的个人信息将被严格保密</li>
              <li>访问凭证仅限当次使用</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- 右侧表单区域 -->
      <div class="form-panel">
        <!-- 进度指示器 -->
        <div class="progress-indicator">
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: progressWidth }"></div>
          </div>
          <div class="progress-text">
            步骤 {{ currentStep + 1 }} / 3
          </div>
        </div>

        <!-- 表单内容 -->
        <div class="form-content">
          <!-- 步骤1: 身份验证 -->
          <div v-show="currentStep === 0" class="step-form">
            <div class="form-header">
              <h3><i class="el-icon-user"></i> 身份验证</h3>
              <p>请选择合适的验证方式进行身份确认</p>
            </div>
            
            <div class="verify-grid">
              <div class="verify-card" 
                   :class="{ selected: verifyMethod === 'id_card' }"
                   @click="selectVerifyMethod('id_card')">
                <div class="verify-icon">
                  <i class="el-icon-postcard"></i>
                </div>
                <h4>身份证验证</h4>
                <p>使用身份证读卡器快速验证</p>
                <div class="verify-status" v-if="verifyMethod === 'id_card'">
                  <i class="el-icon-check"></i>
                </div>
              </div>

              <div class="verify-card"
                   :class="{ selected: verifyMethod === 'face' }"
                   @click="selectVerifyMethod('face')">
                <div class="verify-icon">
                  <i class="el-icon-camera-solid"></i>
                </div>
                <h4>人脸识别</h4>
                <p>使用摄像头进行人脸识别</p>
                <div class="verify-status" v-if="verifyMethod === 'face'">
                  <i class="el-icon-check"></i>
                </div>
              </div>

              <div class="verify-card"
                   :class="{ selected: verifyMethod === 'manual' }"
                   @click="selectVerifyMethod('manual')">
                <div class="verify-icon">
                  <i class="el-icon-edit-outline"></i>
                </div>
                <h4>手动输入</h4>
                <p>手动填写身份信息</p>
                <div class="verify-status" v-if="verifyMethod === 'manual'">
                  <i class="el-icon-check"></i>
                </div>
              </div>

              <div class="verify-card"
                   :class="{ selected: verifyMethod === 'passport' }"
                   @click="selectVerifyMethod('passport')">
                <div class="verify-icon">
                  <i class="el-icon-document"></i>
                </div>
                <h4>护照验证</h4>
                <p>使用护照等其他证件</p>
                <div class="verify-status" v-if="verifyMethod === 'passport'">
                  <i class="el-icon-check"></i>
                </div>
              </div>
            </div>

            <!-- 验证方式具体操作区域 -->
            <div v-if="showManualInput" class="verify-operation">
              <div class="operation-header">
                <h4>请填写身份信息</h4>
                <el-button type="text" @click="backToVerifyMethod">
                  <i class="el-icon-arrow-left"></i> 重新选择验证方式
                </el-button>
              </div>
              
              <el-form ref="identityForm" :model="identityForm" :rules="identityRules" 
                       label-width="100px" class="identity-form">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="姓名" prop="name">
                      <el-input v-model="identityForm.name" placeholder="请输入真实姓名" 
                                prefix-icon="el-icon-user" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="手机号" prop="phone">
                      <el-input v-model="identityForm.phone" placeholder="请输入手机号码"
                                prefix-icon="el-icon-phone" />
                    </el-form-item>
                  </el-col>
                </el-row>
                
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="证件类型" prop="idType">
                      <el-select v-model="identityForm.idType" placeholder="请选择证件类型" style="width: 100%">
                        <el-option label="身份证" value="id_card" />
                        <el-option label="护照" value="passport" />
                        <el-option label="港澳通行证" value="hk_mo_pass" />
                        <el-option label="台湾通行证" value="tw_pass" />
                        <el-option label="其他" value="other" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="证件号码" prop="idNumber">
                      <el-input v-model="identityForm.idNumber" placeholder="请输入证件号码"
                                prefix-icon="el-icon-postcard" />
                    </el-form-item>
                  </el-col>
                </el-row>
                
                <el-form-item label="所属公司" prop="company">
                  <el-input v-model="identityForm.company" placeholder="请输入公司名称"
                            prefix-icon="el-icon-office-building" />
                </el-form-item>
              </el-form>
            </div>

            <!-- 身份证读卡器界面 -->
            <div v-if="showIdCardReader" class="verify-operation">
              <div class="operation-header">
                <h4>身份证读卡验证</h4>
                <el-button type="text" @click="backToVerifyMethod">
                  <i class="el-icon-arrow-left"></i> 重新选择验证方式
                </el-button>
              </div>
              
              <div class="id-card-reader">
                <div class="reader-visual">
                  <div class="reader-animation">
                    <i class="el-icon-loading rotating"></i>
                  </div>
                  <h4>请将身份证放置在读卡器上</h4>
                  <p>确保身份证平放在读卡器感应区域</p>
                  <div class="reader-tips">
                    <el-alert title="提示" type="info" :closable="false">
                      如果读卡失败，请检查身份证是否放置正确，或选择其他验证方式
                    </el-alert>
                  </div>
                  <el-button type="primary" @click="simulateIdCardRead" style="margin-top: 20px;">
                    模拟读取成功
                  </el-button>
                </div>
              </div>
            </div>

            <!-- 人脸识别界面 -->
            <div v-if="showFaceRecognition" class="verify-operation">
              <div class="operation-header">
                <h4>人脸识别验证</h4>
                <el-button type="text" @click="backToVerifyMethod">
                  <i class="el-icon-arrow-left"></i> 重新选择验证方式
                </el-button>
              </div>
              
              <div class="face-recognition">
                <div class="camera-container">
                  <div class="camera-frame">
                    <div class="camera-overlay">
                      <div class="face-outline"></div>
                      <i class="el-icon-camera-solid camera-icon"></i>
                    </div>
                    <div class="scanning-line"></div>
                  </div>
                  <h4>请面向摄像头</h4>
                  <p>保持面部清晰可见，等待识别完成</p>
                  <el-button type="primary" @click="simulateFaceRecognition" style="margin-top: 20px;">
                    模拟识别成功
                  </el-button>
                </div>
              </div>
            </div>
          </div>

          <!-- 步骤2: 填写访问信息 -->
          <div v-show="currentStep === 1" class="step-form">
            <div class="form-header">
              <h3><i class="el-icon-edit"></i> 完善访问信息</h3>
              <p>请填写详细的访问信息</p>
            </div>

            <el-form ref="visitForm" :model="visitForm" :rules="visitRules" 
                     label-width="120px" class="visit-form">
              
              <!-- 基本访问信息 -->
              <div class="form-section">
                <h4 class="section-title">基本访问信息</h4>
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="来访事由" prop="reasonForVisit">
                      <el-input v-model="visitForm.reasonForVisit" placeholder="请输入来访事由"
                                prefix-icon="el-icon-tickets" type="textarea" :rows="3" maxlength="200" show-word-limit />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="被访人姓名" prop="hostEmployeeName">
                      <el-input v-model="visitForm.hostEmployeeName" placeholder="请输入被访人姓名"
                                prefix-icon="el-icon-user" maxlength="20" />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="被访部门" prop="departmentVisited">
                      <el-input v-model="visitForm.departmentVisited" placeholder="请输入被访部门"
                                prefix-icon="el-icon-office-building" maxlength="50" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="车牌号">
                      <el-input v-model="visitForm.vehiclePlateNumber" placeholder="如有车辆请填写车牌号，多个用逗号分隔"
                                prefix-icon="el-icon-truck" />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>

              <!-- 时间信息 -->
              <div class="form-section">
                <h4 class="section-title">访问时间</h4>
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="预计到访时间" prop="plannedEntryDatetime">
                      <el-date-picker
                        v-model="visitForm.plannedEntryDatetime"
                        type="datetime"
                        placeholder="选择到访时间"
                        style="width: 100%"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        :picker-options="entryPickerOptions"
                        @change="onArrivalTimeChange"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="预计离开时间" prop="plannedExitDatetime">
                      <el-date-picker
                        v-model="visitForm.plannedExitDatetime"
                        type="datetime"
                        placeholder="选择离开时间"
                        style="width: 100%"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        :picker-options="exitPickerOptions"
                        @change="onDepartureTimeChange"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>

              <!-- 同行人员信息 -->
              <div class="form-section">
                <div class="section-header">
                  <h4 class="section-title">访客信息（第一位为主联系人）共 {{ totalVisitors }} 人</h4>
                  <el-button type="primary" size="small" icon="el-icon-plus" @click="addCompanion" :disabled="companionList.length >= 9">
                    添加访客
                  </el-button>
                </div>
                
                <div v-if="companionList.length === 0" class="no-companions">
                  <i class="el-icon-user-solid"></i>
                  <p>暂无同行人员，主访客信息已在身份验证步骤中填写</p>
                </div>
                
                <div v-else class="companions-table">
                  <el-table :data="companionList" border style="width: 100%;">
                    <el-table-column type="index" label="序号" width="60" align="center">
                      <template slot-scope="scope">
                        访客{{ scope.$index + 2 }}
                      </template>
                    </el-table-column>
                    <el-table-column label="姓名" width="150">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.name" placeholder="请输入姓名" size="small" maxlength="20" />
                      </template>
                    </el-table-column>
                    <el-table-column label="手机号" width="150">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.phone" placeholder="请输入手机号" size="small" />
                      </template>
                    </el-table-column>
                    <el-table-column label="身份证号" min-width="180">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.idNumber" placeholder="请输入身份证号" size="small" />
                      </template>
                    </el-table-column>
                    <el-table-column label="公司" width="150">
                      <template slot-scope="scope">
                        <el-input v-model="scope.row.company" placeholder="请输入公司（可选）" size="small" maxlength="100" />
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="80" align="center">
                      <template slot-scope="scope">
                        <el-button size="mini" type="danger" icon="el-icon-delete" 
                                   @click="removeCompanion(scope.$index)" circle />
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
            </el-form>
          </div>

          <!-- 步骤3: 完成登记 -->
          <div v-show="currentStep === 2" class="step-form">
            <div class="form-header">
              <h3><i class="el-icon-circle-check"></i> 登记完成</h3>
              <p>您的访客登记已成功提交</p>
            </div>

            <div class="success-content">
              <div class="success-icon">
                <i class="el-icon-circle-check"></i>
              </div>
              
              <h4>登记成功！</h4>
              <p class="success-message">您的访客登记已提交，请等待审核通过</p>
              
              <!-- 登记信息摘要 -->
              <div class="register-summary">
                <h5>登记信息摘要</h5>
                <el-descriptions :column="2" border>
                  <el-descriptions-item label="访客姓名">
                    {{ identityForm.name }}
                  </el-descriptions-item>
                  <el-descriptions-item label="联系电话">
                    {{ identityForm.phone }}
                  </el-descriptions-item>
                  <el-descriptions-item label="被访人">
                    {{ visitForm.hostEmployeeName }}
                  </el-descriptions-item>
                  <el-descriptions-item label="被访部门">
                    {{ visitForm.departmentVisited }}
                  </el-descriptions-item>
                  <el-descriptions-item label="来访事由" span="2">
                    {{ visitForm.reasonForVisit }}
                  </el-descriptions-item>
                  <el-descriptions-item label="预计到访时间">
                    {{ parseTime(visitForm.plannedEntryDatetime) }}
                  </el-descriptions-item>
                  <el-descriptions-item label="预计离开时间">
                    {{ parseTime(visitForm.plannedExitDatetime) }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>

              <!-- 访问凭证 -->
              <div class="access-credential" v-if="registrationId">
                <h5>访问凭证</h5>
                <div class="credential-card">
                  <div class="qr-code-container">
                    <div class="qr-code">
                      <i class="el-icon-qrcode"></i>
                    </div>
                    <p class="qr-code-id">{{ registrationId }}</p>
                  </div>
                  <div class="credential-info">
                    <h6>使用说明</h6>
                    <ul>
                      <li>请保存此二维码截图</li>
                      <li>审核通过后可用于园区门禁</li>
                      <li>凭证仅限当次访问使用</li>
                      <li>如有疑问请联系园区前台</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部操作按钮 -->
        <div class="form-actions">
          <el-button v-if="currentStep > 0" @click="prevStep" size="large">
            <i class="el-icon-arrow-left"></i> 上一步
          </el-button>
          
          <el-button v-if="currentStep === 0 && verifyMethod && !showVerifyOperation" 
                     type="primary" @click="handleVerifyNext" size="large" :loading="verifying">
            下一步 <i class="el-icon-arrow-right"></i>
          </el-button>
          
          <el-button v-if="currentStep === 0 && showManualInput" 
                     type="primary" @click="confirmIdentity" size="large" :loading="verifying">
            确认身份 <i class="el-icon-arrow-right"></i>
          </el-button>
          
          <el-button v-if="currentStep === 1" 
                     type="primary" @click="nextStep" size="large" :loading="submitting">
            提交登记 <i class="el-icon-arrow-right"></i>
          </el-button>
          
          <div v-if="currentStep === 2" class="final-actions">
            <el-button type="primary" @click="printCredential" size="large">
              <i class="el-icon-printer"></i> 打印凭证
            </el-button>
            <el-button @click="resetRegister" size="large">
              <i class="el-icon-refresh"></i> 重新登记
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { submitVisitorRegistration } from "@/api/asc/visitor";

export default {
  name: "SelfRegister",
  data() {
    return {
      currentStep: 0,
      verifying: false,
      submitting: false,
      
      // 验证方式
      verifyMethod: '',
      showManualInput: false,
      showIdCardReader: false,
      showFaceRecognition: false,
      
      // 身份信息
      identityForm: {
        name: '',
        phone: '',
        idType: '',
        idNumber: '',
        company: ''
      },
      identityRules: {
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' },
          { min: 2, max: 20, message: '姓名长度应在2-20个字符之间', trigger: 'blur' },
          { pattern: /^[\u4e00-\u9fa5a-zA-Z·\s]+$/, message: '姓名只能包含中文、英文字母和常见符号', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ],
        idType: [{ required: true, message: '请选择证件类型', trigger: 'change' }],
        idNumber: [
          { required: true, message: '请输入证件号码', trigger: 'blur' },
          { min: 15, max: 18, message: '证件号码长度不正确', trigger: 'blur' }
        ],
        company: [{ max: 100, message: '公司名称不能超过100个字符', trigger: 'blur' }]
      },
      
      // 访问信息
      visitForm: {
        reasonForVisit: '',
        hostEmployeeName: '',
        departmentVisited: '',
        vehiclePlateNumber: '',
        plannedEntryDatetime: null,
        plannedExitDatetime: null
      },
      visitRules: {
        reasonForVisit: [
          { required: true, message: '请输入来访事由', trigger: 'blur' },
          { min: 2, max: 200, message: '来访事由长度应在2-200个字符之间', trigger: 'blur' }
        ],
        hostEmployeeName: [
          { required: true, message: '请输入被访人姓名', trigger: 'blur' },
          { min: 2, max: 20, message: '被访人姓名长度应在2-20个字符之间', trigger: 'blur' }
        ],
        departmentVisited: [
          { required: true, message: '请输入被访部门', trigger: 'blur' },
          { min: 2, max: 50, message: '部门名称长度应在2-50个字符之间', trigger: 'blur' }
        ],
        plannedEntryDatetime: [{ required: true, message: '请选择预计到访时间', trigger: 'change' }],
        plannedExitDatetime: [{ required: true, message: '请选择预计离开时间', trigger: 'change' }]
      },
      
      // 同行人员
      companionList: [],
      
      // 登记结果
      registrationId: null,

      // 时间选择器配置
      entryPickerOptions: {
        shortcuts: [{
          text: '现在',
          onClick(picker) {
            picker.$emit('pick', new Date());
          }
        }, {
          text: '1小时后',
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() + 3600 * 1000);
            picker.$emit('pick', date);
          }
        }, {
          text: '明天上午9点',
          onClick(picker) {
            const date = new Date();
            date.setDate(date.getDate() + 1);
            date.setHours(9, 0, 0, 0);
            picker.$emit('pick', date);
          }
        }],
        disabledDate(time) {
          // 允许选择当前时间前1小时到未来30天
          const oneHourBefore = Date.now() - 3600 * 1000;
          const thirtyDaysLater = Date.now() + 30 * 24 * 3600 * 1000;
          return time.getTime() < oneHourBefore || time.getTime() > thirtyDaysLater;
        }
      },
      
      exitPickerOptions: {
        shortcuts: [{
          text: '2小时后',
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() + 2 * 3600 * 1000);
            picker.$emit('pick', date);
          }
        }, {
          text: '今天下午6点',
          onClick(picker) {
            const date = new Date();
            date.setHours(18, 0, 0, 0);
            if (date.getTime() < Date.now()) {
              date.setDate(date.getDate() + 1);
            }
            picker.$emit('pick', date);
          }
        }, {
          text: '明天下午6点',
          onClick(picker) {
            const date = new Date();
            date.setDate(date.getDate() + 1);
            date.setHours(18, 0, 0, 0);
            picker.$emit('pick', date);
          }
        }],
        disabledDate(time) {
          // 允许选择当前时间到未来30天
          const thirtyDaysLater = Date.now() + 30 * 24 * 3600 * 1000;
          return time.getTime() < Date.now() - 8.64e7 || time.getTime() > thirtyDaysLater;
        }
      }
    };
  },
  
  computed: {
    progressWidth() {
      return ((this.currentStep + 1) / 3) * 100 + '%';
    },
    
    showVerifyOperation() {
      return this.showManualInput || this.showIdCardReader || this.showFaceRecognition;
    },

    // 总访客人数（主访客 + 同行人员）
    totalVisitors() {
      return 1 + this.companionList.length;
    }
  },
  
  methods: {
    /** 选择验证方式 */
    selectVerifyMethod(method) {
      this.verifyMethod = method;
      this.resetVerifyDisplay();
      
      switch (method) {
        case 'manual':
          this.showManualInput = true;
          break;
        case 'id_card':
          this.showIdCardReader = true;
          break;
        case 'face':
          this.showFaceRecognition = true;
          break;
        case 'passport':
          this.showManualInput = true;
          this.identityForm.idType = 'passport';
          break;
      }
    },
    
    /** 重置验证显示 */
    resetVerifyDisplay() {
      this.showManualInput = false;
      this.showIdCardReader = false;
      this.showFaceRecognition = false;
    },
    
    /** 返回验证方式选择 */
    backToVerifyMethod() {
      this.resetVerifyDisplay();
      this.verifyMethod = '';
    },

    /** 处理验证方式下一步 */
    handleVerifyNext() {
      if (this.verifyMethod === 'manual') {
        this.showManualInput = true;
      } else if (this.verifyMethod === 'id_card') {
        this.showIdCardReader = true;
      } else if (this.verifyMethod === 'face') {
        this.showFaceRecognition = true;
      } else if (this.verifyMethod === 'passport') {
        this.showManualInput = true;
        this.identityForm.idType = 'passport';
      }
    },
    
    /** 确认身份 */
    confirmIdentity() {
      this.$refs.identityForm.validate(valid => {
        if (valid) {
          this.verifying = true;
          // 模拟验证过程
          setTimeout(() => {
            this.verifying = false;
            this.$message.success('身份验证成功');
            this.nextStep();
          }, 1000);
        }
      });
    },
    
    /** 模拟身份证读取 */
    simulateIdCardRead() {
      this.identityForm = {
        name: '张三',
        phone: '13800138000',
        idType: 'id_card',
        idNumber: '110101199001011234',
        company: '某某科技有限公司'
      };
      this.$message.success('身份证信息读取成功');
      setTimeout(() => {
        this.nextStep();
      }, 1000);
    },
    
    /** 模拟人脸识别 */
    simulateFaceRecognition() {
      this.identityForm = {
        name: '李四',
        phone: '13900139000',
        idType: 'id_card',
        idNumber: '110101199002021234',
        company: '某某贸易有限公司'
      };
      this.$message.success('人脸识别成功');
      setTimeout(() => {
        this.nextStep();
      }, 1000);
    },
    
    /** 下一步 */
    nextStep() {
      if (this.currentStep === 1) {
        this.submitRegistration();
      } else {
        this.currentStep++;
      }
    },
    
    /** 上一步 */
    prevStep() {
      this.currentStep--;
      if (this.currentStep === 0) {
        this.resetVerifyDisplay();
      }
    },
    
    /** 添加同行人员 */
    addCompanion() {
      if (this.companionList.length >= 9) {
        this.$message.warning('最多只能添加9名同行人员（总计10名访客）');
        return;
      }

      this.companionList.push({
        name: '',
        phone: '',
        idNumber: '',
        company: ''
      });
      
      this.$message.success('已添加访客');
    },
    
    /** 来访时间变更事件 */
    onArrivalTimeChange(value) {
      console.log('预计来访时间变更:', value);
      // 自动设置离开时间为来访时间后4小时
      if (value && !this.visitForm.plannedExitDatetime) {
        const arrivalTime = new Date(value);
        const departureTime = new Date(arrivalTime.getTime() + 4 * 60 * 60 * 1000);
        
        const year = departureTime.getFullYear();
        const month = String(departureTime.getMonth() + 1).padStart(2, '0');
        const day = String(departureTime.getDate()).padStart(2, '0');
        const hours = String(departureTime.getHours()).padStart(2, '0');
        const minutes = String(departureTime.getMinutes()).padStart(2, '0');
        const seconds = String(departureTime.getSeconds()).padStart(2, '0');
        
        this.visitForm.plannedExitDatetime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      }
    },

    /** 离开时间变更事件 */
    onDepartureTimeChange(value) {
      console.log('预计离开时间变更:', value);
      // 验证离开时间不能早于来访时间
      if (value && this.visitForm.plannedEntryDatetime) {
        const arrivalTime = new Date(this.visitForm.plannedEntryDatetime);
        const departureTime = new Date(value);

        if (departureTime <= arrivalTime) {
          this.$message.warning('预计离开时间不能早于或等于来访时间');
          this.visitForm.plannedExitDatetime = '';
        }
      }
    },
    
    /** 提交登记 */
    submitRegistration() {
      this.$refs.visitForm.validate(valid => {
        if (valid) {
          // 验证同行人员信息
          if (!this.validateVisitors()) {
            return;
          }

          // 验证时间
          if (!this.validateTimes()) {
            return;
          }

          this.submitting = true;
          
          // 构建提交数据 - 按照微信端相同的数据结构
          const submitData = {
            // VisitRegistrations 对象
            registration: {
              primaryContactName: this.identityForm.name.trim(),
              primaryContactPhone: this.identityForm.phone.trim(),
              reasonForVisit: this.visitForm.reasonForVisit.trim(),
              hostEmployeeName: this.visitForm.hostEmployeeName.trim(),
              hostEmployeeId: null, // 前端暂时没有被访人ID，后端可能会自动处理
              departmentVisited: this.visitForm.departmentVisited.trim(),
              plannedEntryDatetime: this.formatDateForBackend(this.visitForm.plannedEntryDatetime),
              plannedExitDatetime: this.formatDateForBackend(this.visitForm.plannedExitDatetime),
              vehiclePlateNumber: this.visitForm.vehiclePlateNumber ? this.visitForm.vehiclePlateNumber.trim() : '',
              totalCompanions: this.companionList.length
            },
            // RegistrationAttendees 数组 - 使用后端期望的临时字段
            attendeesList: [
              // 主访客
              {
                visitorName: this.identityForm.name.trim(),
                visitorPhone: this.identityForm.phone.trim(),
                visitorIdCard: this.identityForm.idNumber.trim().toUpperCase(),
                visitorCompany: this.identityForm.company ? this.identityForm.company.trim() : '',
                isPrimary: "1", // 第一个访客必须是主联系人
                visitorAvatarPhoto: null // 暂时不支持头像上传
              },
              // 同行人员
              ...this.companionList
                .filter(item => item.name && item.phone && item.idNumber)
                .map(item => ({
                  visitorName: item.name.trim(),
                  visitorPhone: item.phone.trim(),
                  visitorIdCard: item.idNumber.trim().toUpperCase(),
                  visitorCompany: item.company ? item.company.trim() : '',
                  isPrimary: "0",
                  visitorAvatarPhoto: null
                }))
            ]
          };

          console.log('提交数据结构:', JSON.stringify(submitData, null, 2));
          
          submitVisitorRegistration(submitData)
            .then(response => {
              this.registrationId = response.data || 'VR' + Date.now();
              this.$message.success(`${this.totalVisitors}名访客登记成功！`);
              this.currentStep++;
            })
            .catch(error => {
              this.$message.error(error.msg || '登记失败，请重试');
            })
            .finally(() => {
              this.submitting = false;
            });
        }
      });
    },

    /** 验证访客信息 */
    validateVisitors() {
      // 验证主访客信息（已在身份验证步骤中验证，这里再次确认）
      if (!this.identityForm.name || this.identityForm.name.trim().length < 2) {
        this.$message.error('主访客姓名不能为空且长度不能少于2个字符');
        return false;
      }

      if (!this.identityForm.phone || !/^1[3-9]\d{9}$/.test(this.identityForm.phone.trim())) {
        this.$message.error('主访客手机号格式不正确');
        return false;
      }

      if (!this.identityForm.idNumber || this.identityForm.idNumber.trim().length < 15) {
        this.$message.error('主访客身份证号不能为空');
        return false;
      }

      // 验证同行人员信息
      for (let i = 0; i < this.companionList.length; i++) {
        const visitor = this.companionList[i];
        const visitorTitle = `访客${i + 2}`;

        // 验证姓名
        if (!visitor.name || visitor.name.trim() === '') {
          this.$message.error(`请输入${visitorTitle}的姓名`);
          return false;
        }
        
        if (visitor.name.trim().length < 2 || visitor.name.trim().length > 20) {
          this.$message.error(`${visitorTitle}的姓名长度应在2-20个字符之间`);
          return false;
        }

        // 验证姓名格式
        const namePattern = /^[\u4e00-\u9fa5a-zA-Z·\s]+$/;
        if (!namePattern.test(visitor.name.trim())) {
          this.$message.error(`${visitorTitle}的姓名只能包含中文、英文字母和常见符号`);
          return false;
        }

        // 验证手机号
        if (!visitor.phone || visitor.phone.trim() === '') {
          this.$message.error(`请输入${visitorTitle}的手机号`);
          return false;
        }

        const phonePattern = /^1[3-9]\d{9}$/;
        if (!phonePattern.test(visitor.phone.trim())) {
          this.$message.error(`${visitorTitle}的手机号格式不正确`);
          return false;
        }

        // 验证身份证号
        if (!visitor.idNumber || visitor.idNumber.trim() === '') {
          this.$message.error(`请输入${visitorTitle}的身份证号`);
          return false;
        }
        
        const idCard = visitor.idNumber.trim().toUpperCase();
        
        // 如果是18位身份证号，验证格式
        if (idCard.length === 18) {
          const idPattern = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
          if (!idPattern.test(idCard)) {
            this.$message.error(`${visitorTitle}的身份证号格式不正确`);
            return false;
          }
        }

        // 检查身份证号是否与主访客重复
        if (visitor.idNumber.trim().toUpperCase() === this.identityForm.idNumber.trim().toUpperCase()) {
          this.$message.error(`${visitorTitle}与主访客的身份证号重复`);
          return false;
        }

        // 检查身份证号是否与其他同行人员重复
        for (let j = 0; j < this.companionList.length; j++) {
          if (i !== j && visitor.idNumber.trim().toUpperCase() === this.companionList[j].idNumber.trim().toUpperCase()) {
            this.$message.error(`${visitorTitle}与访客${j + 2}的身份证号重复`);
            return false;
          }
        }

        // 检查手机号是否与主访客重复
        if (visitor.phone.trim() === this.identityForm.phone.trim()) {
          this.$message.error(`${visitorTitle}与主访客的手机号重复，请确认是否为同一人`);
          return false;
        }

        // 检查手机号是否与其他同行人员重复
        for (let j = 0; j < this.companionList.length; j++) {
          if (i !== j && visitor.phone.trim() === this.companionList[j].phone.trim()) {
            this.$message.error(`${visitorTitle}与访客${j + 2}的手机号重复，请确认是否为同一人`);
            return false;
          }
        }
        
        // 清理和标准化数据
        visitor.name = visitor.name.trim();
        visitor.phone = visitor.phone.trim();
        visitor.idNumber = visitor.idNumber.trim().toUpperCase();
        visitor.company = (visitor.company || '').trim();
      }

      return true;
    },

    /** 验证时间 */
    validateTimes() {
      if (!this.visitForm.plannedEntryDatetime) {
        this.$message.error('请选择预计来访时间');
        return false;
      }

      if (!this.visitForm.plannedExitDatetime) {
        this.$message.error('请选择预计离开时间');
        return false;
      }

      const arrivalTime = new Date(this.visitForm.plannedEntryDatetime);
      const departureTime = new Date(this.visitForm.plannedExitDatetime);
      const now = new Date();

      // 验证来访时间不能是过去时间（允许当前时间前1小时的误差）
      const oneHourBefore = new Date(now.getTime() - 60 * 60 * 1000);
      if (arrivalTime < oneHourBefore) {
        this.$message.error('预计来访时间不能是过去时间');
        return false;
      }

      // 验证离开时间必须晚于来访时间
      if (departureTime <= arrivalTime) {
        this.$message.error('预计离开时间必须晚于来访时间');
        return false;
      }

      return true;
    },

    /** 格式化日期为后端期望的格式 */
    formatDateForBackend(dateStr) {
      if (!dateStr) return '';
      
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return '';
      
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    
    /** 重新登记 */
    resetRegister() {
      this.currentStep = 0;
      this.resetVerifyDisplay();
      this.verifyMethod = '';
      this.identityForm = {
        name: '',
        phone: '',
        idType: '',
        idNumber: '',
        company: ''
      };
      this.visitForm = {
        reasonForVisit: '',
        hostEmployeeName: '',
        departmentVisited: '',
        vehiclePlateNumber: '',
        plannedEntryDatetime: null,
        plannedExitDatetime: null
      };
      this.companionList = [];
      this.registrationId = null;
    },
    
    /** 打印凭证 */
    printCredential() {
      this.$message.info('正在生成打印文件...');
      // TODO: 实现打印功能
    },

    /** 显示帮助 */
    showHelp() {
      this.$alert('如需帮助，请联系园区前台或拨打服务热线', '使用帮助', {
        confirmButtonText: '确定'
      });
    },

    /** 联系客服 */
    contactService() {
      this.$alert('服务热线：400-1234-567\n服务时间：周一至周五 8:00-18:00', '联系客服', {
        confirmButtonText: '确定'
      });
    }
  }
};
</script>

<style scoped>
/* 主容器 */
.pc-register-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family: 'Helvetica Neue', Arial, sans-serif;
}

/* 顶部导航 */
.top-nav {
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 0 40px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
  max-width: 1400px;
  margin: 0 auto;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 15px;
}

.nav-logo {
  height: 40px;
}

.company-name {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
}

.nav-actions {
  display: flex;
  gap: 20px;
}

/* 主内容区域 */
.main-content {
  display: flex;
  max-width: 1400px;
  margin: 0 auto;
  padding: 40px;
  gap: 40px;
  min-height: calc(100vh - 110px);
}

/* 左侧信息面板 */
.info-panel {
  flex: 0 0 400px;
}

.welcome-card {
  background: #fff;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  height: fit-content;
  position: sticky;
  top: 110px;
}

.welcome-icon {
  text-align: center;
  margin-bottom: 24px;
}

.welcome-icon i {
  font-size: 64px;
  color: #3498db;
}

.welcome-card h2 {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 16px;
  font-size: 28px;
  font-weight: 600;
}

.welcome-desc {
  text-align: center;
  color: #7f8c8d;
  margin-bottom: 32px;
  font-size: 16px;
  line-height: 1.6;
}

/* 流程步骤 */
.process-steps {
  margin-bottom: 32px;
}

.step-item {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  padding: 16px;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.step-item.active {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  transform: translateX(8px);
}

.step-item:not(.active) {
  background: #f8f9fa;
  color: #6c757d;
}

.step-circle {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
  flex-shrink: 0;
}

.step-item.active .step-circle {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.step-item:not(.active) .step-circle {
  background: #dee2e6;
  color: #6c757d;
}

.step-text h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
}

.step-text p {
  margin: 0;
  font-size: 14px;
  opacity: 0.8;
}

/* 安全提示 */
.security-tips {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  border-left: 4px solid #3498db;
}

.security-tips h4 {
  margin: 0 0 12px 0;
  color: #2c3e50;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.security-tips ul {
  margin: 0;
  padding-left: 20px;
  color: #6c757d;
}

.security-tips li {
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 1.4;
}

/* 右侧表单面板 */
.form-panel {
  flex: 1;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 进度指示器 */
.progress-indicator {
  background: #f8f9fa;
  padding: 24px 32px;
  border-bottom: 1px solid #e9ecef;
}

.progress-bar {
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3498db, #2980b9);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  text-align: center;
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
}

/* 表单内容 */
.form-content {
  padding: 32px;
  max-height: calc(100vh - 300px);
  overflow-y: auto;
}

.form-header {
  text-align: center;
  margin-bottom: 32px;
}

.form-header h3 {
  color: #2c3e50;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.form-header p {
  color: #7f8c8d;
  margin: 0;
  font-size: 16px;
}

/* 验证方式网格 */
.verify-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 32px;
}

.verify-card {
  position: relative;
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.verify-card:hover {
  border-color: #3498db;
  background: #fff;
  box-shadow: 0 4px 16px rgba(52, 152, 219, 0.15);
  transform: translateY(-2px);
}

.verify-card.selected {
  border-color: #3498db;
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  box-shadow: 0 8px 24px rgba(52, 152, 219, 0.3);
}

.verify-icon {
  margin-bottom: 16px;
}

.verify-icon i {
  font-size: 48px;
  color: #3498db;
}

.verify-card.selected .verify-icon i {
  color: white;
}

.verify-card h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
}

.verify-card p {
  margin: 0;
  font-size: 14px;
  opacity: 0.8;
}

.verify-status {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 验证操作区域 */
.verify-operation {
  margin-top: 32px;
  padding: 24px;
  background: #f8f9fa;
  border-radius: 12px;
}

.operation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.operation-header h4 {
  margin: 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

/* 身份表单 */
.identity-form {
  background: white;
  padding: 24px;
  border-radius: 12px;
}

/* 身份证读卡器 */
.id-card-reader {
  text-align: center;
}

.reader-visual {
  background: white;
  padding: 40px;
  border-radius: 12px;
}

.reader-animation {
  margin-bottom: 24px;
}

.rotating {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.reader-animation i {
  font-size: 64px;
  color: #3498db;
}

.reader-visual h4 {
  color: #2c3e50;
  margin-bottom: 8px;
}

.reader-visual p {
  color: #7f8c8d;
  margin-bottom: 24px;
}

.reader-tips {
  margin: 24px 0;
}

/* 人脸识别 */
.face-recognition {
  text-align: center;
}

.camera-container {
  background: white;
  padding: 40px;
  border-radius: 12px;
}

.camera-frame {
  position: relative;
  width: 280px;
  height: 210px;
  margin: 0 auto 24px;
  border: 3px solid #3498db;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  overflow: hidden;
}

.camera-overlay {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.face-outline {
  width: 120px;
  height: 150px;
  border: 2px dashed #3498db;
  border-radius: 60px;
  opacity: 0.6;
}

.camera-icon {
  font-size: 32px;
  color: #3498db;
}

.scanning-line {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, #3498db, transparent);
  animation: scan 2s ease-in-out infinite;
}

@keyframes scan {
  0% { transform: translateY(0); }
  50% { transform: translateY(206px); }
  100% { transform: translateY(0); }
}

/* 表单分组 */
.form-section {
  margin-bottom: 32px;
}

.section-title {
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e9ecef;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

/* 同行人员 */
.no-companions {
  text-align: center;
  padding: 40px;
  color: #7f8c8d;
}

.no-companions i {
  font-size: 48px;
  margin-bottom: 16px;
  color: #bdc3c7;
}

.companions-table {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
}

/* 成功页面 */
.success-content {
  text-align: center;
}

.success-icon {
  margin-bottom: 24px;
}

.success-icon i {
  font-size: 80px;
  color: #27ae60;
}

.success-content h4 {
  color: #2c3e50;
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 12px;
}

.success-message {
  color: #7f8c8d;
  font-size: 16px;
  margin-bottom: 32px;
}

.register-summary {
  background: #f8f9fa;
  padding: 24px;
  border-radius: 12px;
  margin-bottom: 32px;
  text-align: left;
}

.register-summary h5 {
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  text-align: center;
}

.access-credential {
  background: #f8f9fa;
  padding: 24px;
  border-radius: 12px;
  text-align: left;
}

.access-credential h5 {
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  text-align: center;
}

.credential-card {
  display: flex;
  gap: 24px;
  background: white;
  padding: 24px;
  border-radius: 12px;
  border: 2px solid #e9ecef;
}

.qr-code-container {
  flex: 0 0 120px;
  text-align: center;
}

.qr-code {
  width: 120px;
  height: 120px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  margin-bottom: 8px;
}

.qr-code i {
  font-size: 48px;
  color: #7f8c8d;
}

.qr-code-id {
  font-size: 12px;
  color: #6c757d;
  font-family: monospace;
  margin: 0;
}

.credential-info {
  flex: 1;
}

.credential-info h6 {
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
}

.credential-info ul {
  margin: 0;
  padding-left: 20px;
  color: #6c757d;
}

.credential-info li {
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 1.4;
}

/* 底部操作按钮 */
.form-actions {
  padding: 24px 32px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.final-actions {
  display: flex;
  gap: 16px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
    gap: 24px;
  }
  
  .info-panel {
    flex: none;
  }
  
  .welcome-card {
    position: static;
  }
  
  .verify-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 20px;
  }
  
  .nav-content {
    padding: 0 20px;
  }
  
  .company-name {
    font-size: 16px;
  }
  
  .form-content {
    padding: 20px;
  }
  
  .credential-card {
    flex-direction: column;
    text-align: center;
  }
  
  .form-actions {
    flex-direction: column;
    gap: 16px;
  }
}

/* Element UI 样式覆盖 */
.el-form-item {
  margin-bottom: 24px;
}

.el-input__inner {
  height: 44px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.el-input__inner:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.1);
}

.el-select {
  width: 100%;
}

.el-date-editor {
  width: 100%;
}

.el-button--large {
  height: 44px;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
}

.el-button--primary {
  background: linear-gradient(135deg, #3498db, #2980b9);
  border: none;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, #2980b9, #1f4e79);
}

.el-descriptions {
  border-radius: 8px;
}

.el-table {
  border-radius: 8px;
}

.el-alert {
  border-radius: 8px;
}
</style>
