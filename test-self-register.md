# 访客自助登记页面修改测试

## 修改内容总结

### 1. 数据结构统一
- 将原有的多步骤验证流程简化为单页面表单
- 数据结构与微信端保持一致：
  ```javascript
  formData: {
    visitors: [{ name, phone, idCard, company, isMainContact }],
    visitInfo: { reasonForVisit, hostEmployeeName, departmentVisited, vehiclePlateNumber, plannedEntryDatetime, plannedExitDatetime }
  }
  ```

### 2. 界面简化
- 移除了复杂的身份验证步骤（身份证读取、人脸识别等）
- 改为直接填写表单的方式
- 保持了PC端的美观界面设计

### 3. 功能对齐
- 访客管理：支持添加/删除多个访客，第一位为主联系人
- 表单验证：与微信端相同的验证规则
- 数据提交：使用相同的API接口和数据格式

### 4. 主要修改点
1. **模板结构**：简化为单页面表单
2. **数据模型**：统一为 `formData.visitors` 和 `formData.visitInfo`
3. **验证逻辑**：统一验证规则和错误提示
4. **提交逻辑**：使用相同的数据结构调用API

## 测试要点

### 功能测试
1. 表单填写和验证
2. 访客添加/删除功能
3. 时间选择和验证
4. 数据提交和成功页面

### 兼容性测试
1. 与微信端数据结构一致性
2. API调用格式正确性
3. 后端数据处理兼容性

### 用户体验测试
1. 界面响应性
2. 操作流畅性
3. 错误提示友好性

## 预期结果
- PC端和微信端访客登记功能完全一致
- 用户体验简化且统一
- 后端数据处理无需修改
