{"remainingRequest": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\api\\archives\\materialClass.js", "dependencies": [{"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\api\\archives\\materialClass.js", "mtime": 1750833543328}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\babel.config.js", "mtime": 1750833543308}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750836929039}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750836940534}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750836931805}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "list", "queryParam", "request", "url", "method", "params", "getDetail", "materialClassId", "add", "data", "update", "listMaterialClassExcludeChild", "classId", "del"], "sources": ["D:/projects/gaoyi-plat/ruoyi-ui/src/api/archives/materialClass.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询分类列表\r\nexport function list(queryParam) {\r\n  return request({\r\n    url: 'archives/materialclass/list',\r\n    method: 'get',\r\n    params: queryParam   //在URL后面拼接\r\n  })\r\n}\r\n\r\nexport function getDetail(materialClassId) {\r\n  return request({\r\n    url: 'archives/materialclass/' + materialClassId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\nexport function add(data) {\r\n  return request({\r\n    url: 'archives/materialclass',\r\n    method: 'post',\r\n    data: data   //在body中\r\n  })\r\n}\r\n\r\nexport function update(data) {\r\n  return request({\r\n    url: 'archives/materialclass',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\nexport function listMaterialClassExcludeChild(classId) {\r\n  return request({\r\n    url: 'archives/materialclass/exclude/' + classId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\nexport function del(materialClassId) {\r\n  return request({\r\n    url: 'archives/materialclass/' + materialClassId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// export function treeSelect(data) {\r\n//   return request({\r\n//     url: 'archives/material/tree',\r\n//     method: 'post',\r\n//     data: data\r\n//   })\r\n// }\r\n\r\n// export function listByClassid(classid) {\r\n//   return request({\r\n//     url: 'archives//material/listbyclassid/' + classid,\r\n//     method: 'get',\r\n//   })\r\n// }\r\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,IAAIA,CAACC,UAAU,EAAE;EAC/B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ,UAAU,CAAG;EACvB,CAAC,CAAC;AACJ;AAEO,SAASK,SAASA,CAACC,eAAe,EAAE;EACzC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB,GAAGI,eAAe;IAChDH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEO,SAASI,GAAGA,CAACC,IAAI,EAAE;EACxB,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA,IAAI,CAAG;EACf,CAAC,CAAC;AACJ;AAEO,SAASC,MAAMA,CAACD,IAAI,EAAE;EAC3B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;AAEO,SAASE,6BAA6BA,CAACC,OAAO,EAAE;EACrD,OAAO,IAAAV,gBAAO,EAAC;IACbC,GAAG,EAAE,iCAAiC,GAAGS,OAAO;IAChDR,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEO,SAASS,GAAGA,CAACN,eAAe,EAAE;EACnC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB,GAAGI,eAAe;IAChDH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "ignoreList": []}]}