{"remainingRequest": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\api\\asc\\deviceArea.js", "dependencies": [{"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\src\\api\\asc\\deviceArea.js", "mtime": 1750833543328}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\babel.config.js", "mtime": 1750833543308}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750836929039}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750836940534}, {"path": "D:\\projects\\gaoyi-plat\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750836931805}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listDeviceArea", "query", "request", "url", "method", "params", "getDeviceArea", "id", "addDeviceArea", "data", "updateDeviceArea", "delDeviceArea", "delDeviceAreaByDeviceId", "deviceId", "exportDeviceArea"], "sources": ["D:/projects/gaoyi-plat/ruoyi-ui/src/api/asc/deviceArea.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询设备区域权限列表\r\nexport function listDeviceArea(query) {\r\n  return request({\r\n    url: '/asc/deviceArea/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询设备区域权限详细\r\nexport function getDeviceArea(id) {\r\n  return request({\r\n    url: '/asc/deviceArea/' + id,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增设备区域权限\r\nexport function addDeviceArea(data) {\r\n  return request({\r\n    url: '/asc/deviceArea',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改设备区域权限\r\nexport function updateDeviceArea(data) {\r\n  return request({\r\n    url: '/asc/deviceArea',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除设备区域权限\r\nexport function delDeviceArea(id) {\r\n  return request({\r\n    url: '/asc/deviceArea/' + id,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 根据设备ID删除设备区域权限\r\nexport function delDeviceAreaByDeviceId(deviceId) {\r\n  return request({\r\n    url: '/asc/deviceArea/device/' + deviceId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 导出设备区域权限\r\nexport function exportDeviceArea(query) {\r\n  return request({\r\n    url: '/asc/deviceArea/export',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n} "], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,cAAcA,CAACC,KAAK,EAAE;EACpC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,aAAaA,CAACC,EAAE,EAAE;EAChC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB,GAAGI,EAAE;IAC5BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,aAAaA,CAACC,IAAI,EAAE;EAClC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,gBAAgBA,CAACD,IAAI,EAAE;EACrC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,aAAaA,CAACJ,EAAE,EAAE;EAChC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB,GAAGI,EAAE;IAC5BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,uBAAuBA,CAACC,QAAQ,EAAE;EAChD,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB,GAAGU,QAAQ;IACzCT,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASU,gBAAgBA,CAACb,KAAK,EAAE;EACtC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}